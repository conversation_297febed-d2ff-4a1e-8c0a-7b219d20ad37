const db = require('../config/database');
const adminService = require('../services/adminService');

async function seedAdminData() {
  try {
    console.log('Starting admin data seeding...');

    // Đồng bộ bảng từ database
    console.log('Syncing tables from database...');
    await adminService.syncTablesFromDatabase();

    // Cập nhật thông tin cho bảng User
    const userTable = await db.queryOne(`
      SELECT * FROM admintable WHERE name = 'user'
    `);

    if (userTable) {
      await db.query(`
        UPDATE admintable 
        SET display_name = ?, description = ?, icon = ?, order_index = ?
        WHERE id = ?
      `, ['Users', 'Quản lý người dùng hệ thống', 'fas fa-users', 1, userTable.id]);

      // Cập nhật cấu hình hiển thị cho các cột
      const columns = await db.query(`
        SELECT * FROM admincolumn WHERE table_id = ?
      `, [userTable.id]);

      for (const column of columns) {
        let updateData = {};

        switch (column.name) {
          case 'id':
            updateData = {
              display_name: 'ID',
              is_visible_list: true,
              is_visible_form: false,
              is_searchable: false,
              is_sortable: true,
              form_type: 'hidden'
            };
            break;
          case 'fullname':
            updateData = {
              display_name: 'Full Name',
              is_visible_list: true,
              is_visible_form: true,
              is_searchable: true,
              is_sortable: true,
              form_type: 'input',
              validation_rules: JSON.stringify({ required: true, minLength: 2 })
            };
            break;
          case 'email':
            updateData = {
              display_name: 'Email',
              is_visible_list: true,
              is_visible_form: true,
              is_searchable: true,
              is_sortable: true,
              form_type: 'email',
              validation_rules: JSON.stringify({ required: true, email: true })
            };
            break;
          case 'password':
            updateData = {
              display_name: 'Password',
              is_visible_list: false,
              is_visible_form: true,
              is_searchable: false,
              is_sortable: false,
              form_type: 'password',
              validation_rules: JSON.stringify({ required: true, minLength: 6 })
            };
            break;
          case 'phone':
            updateData = {
              display_name: 'Phone',
              is_visible_list: true,
              is_visible_form: true,
              is_searchable: true,
              is_sortable: true,
              form_type: 'tel'
            };
            break;
          case 'gender':
            updateData = {
              display_name: 'Gender',
              is_visible_list: true,
              is_visible_form: true,
              is_searchable: false,
              is_sortable: true,
              form_type: 'select',
              validation_rules: JSON.stringify({ 
                options: [
                  { value: 1, label: 'Male' },
                  { value: 2, label: 'Female' },
                  { value: 0, label: 'Other' }
                ]
              })
            };
            break;
          case 'active':
            updateData = {
              display_name: 'Active',
              is_visible_list: true,
              is_visible_form: true,
              is_searchable: false,
              is_sortable: true,
              form_type: 'checkbox'
            };
            break;
          case 'session_id':
            updateData = {
              display_name: 'Session ID',
              is_visible_list: false,
              is_visible_form: false,
              is_searchable: false,
              is_sortable: false
            };
            break;
          case 'createdAt':
            updateData = {
              display_name: 'Created At',
              is_visible_list: true,
              is_visible_form: false,
              is_searchable: false,
              is_sortable: true,
              form_type: 'datetime'
            };
            break;
          case 'updatedAt':
            updateData = {
              display_name: 'Updated At',
              is_visible_list: true,
              is_visible_form: false,
              is_searchable: false,
              is_sortable: true,
              form_type: 'datetime'
            };
            break;
        }

        if (Object.keys(updateData).length > 0) {
          await db.query(`
            UPDATE admincolumn 
            SET display_name = ?, is_visible_list = ?, is_visible_form = ?, 
                is_searchable = ?, is_sortable = ?, form_type = ?, validation_rules = ?
            WHERE id = ?
          `, [
            updateData.display_name,
            updateData.is_visible_list,
            updateData.is_visible_form,
            updateData.is_searchable,
            updateData.is_sortable,
            updateData.form_type,
            updateData.validation_rules,
            column.id
          ]);
        }
      }

      console.log('Updated User table configuration');
    }

    // Cập nhật thông tin cho bảng Role
    const roleTable = await db.queryOne(`
      SELECT * FROM admintable WHERE name = 'role'
    `);

    if (roleTable) {
      await db.query(`
        UPDATE admintable 
        SET display_name = ?, description = ?, icon = ?, order_index = ?
        WHERE id = ?
      `, ['Roles', 'Quản lý vai trò người dùng', 'fas fa-user-tag', 2, roleTable.id]);

      console.log('Updated Role table configuration');
    }

    // Cập nhật thông tin cho bảng Role_User
    const roleUserTable = await db.queryOne(`
      SELECT * FROM admintable WHERE name = 'role_user'
    `);

    if (roleUserTable) {
      await db.query(`
        UPDATE admintable 
        SET display_name = ?, description = ?, icon = ?, order_index = ?
        WHERE id = ?
      `, ['User Roles', 'Quản lý vai trò của người dùng', 'fas fa-link', 3, roleUserTable.id]);

      console.log('Updated Role_User table configuration');
    }

    console.log('Admin data seeding completed successfully!');
  } catch (error) {
    console.error('Error seeding admin data:', error);
  }
}

// Chạy script nếu được gọi trực tiếp
if (require.main === module) {
  seedAdminData();
}

module.exports = seedAdminData;
