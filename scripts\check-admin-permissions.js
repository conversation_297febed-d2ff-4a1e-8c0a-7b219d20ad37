const db = require('../config/database');

async function checkAdminPermissions() {
  try {
    console.log('🔍 Checking admin permissions...\n');

    // Connect to database
    await db.connect('development');

    // 1. Check admin role permissions
    console.log('1. Admin role permissions:');
    const adminPermissions = await db.query(`
      SELECT p.name, p.display_name, p.table_name, p.action
      FROM permissions p
      INNER JOIN role_permissions rp ON p.id = rp.permission_id
      WHERE rp.role_id = 1
      ORDER BY p.table_name, p.action
    `);

    console.log(`✅ Admin has ${adminPermissions.length} permissions:`);
    
    // Group by table
    const permissionsByTable = {};
    adminPermissions.forEach(perm => {
      if (!permissionsByTable[perm.table_name]) {
        permissionsByTable[perm.table_name] = [];
      }
      permissionsByTable[perm.table_name].push(perm);
    });

    Object.keys(permissionsByTable).forEach(tableName => {
      console.log(`\n📋 ${tableName}:`);
      permissionsByTable[tableName].forEach(perm => {
        console.log(`   - ${perm.action}: ${perm.display_name}`);
      });
    });

    // 2. Check if admin_menus permissions exist
    console.log('\n2. Checking admin_menus permissions:');
    const adminMenusPermissions = await db.query(`
      SELECT * FROM permissions WHERE table_name = 'admin_menus'
    `);

    if (adminMenusPermissions.length > 0) {
      console.log(`✅ Found ${adminMenusPermissions.length} admin_menus permissions:`);
      adminMenusPermissions.forEach(perm => {
        console.log(`   - ${perm.name}: ${perm.display_name}`);
      });

      // Check if admin has these permissions
      console.log('\n3. Checking if admin has admin_menus permissions:');
      const adminMenusRolePermissions = await db.query(`
        SELECT p.name, p.display_name
        FROM permissions p
        INNER JOIN role_permissions rp ON p.id = rp.permission_id
        WHERE rp.role_id = 1 AND p.table_name = 'admin_menus'
      `);

      if (adminMenusRolePermissions.length > 0) {
        console.log(`✅ Admin has ${adminMenusRolePermissions.length} admin_menus permissions:`);
        adminMenusRolePermissions.forEach(perm => {
          console.log(`   - ${perm.name}: ${perm.display_name}`);
        });
      } else {
        console.log('❌ Admin does NOT have admin_menus permissions');
        
        // Grant admin_menus permissions to admin
        console.log('\n4. Granting admin_menus permissions to admin...');
        for (const perm of adminMenusPermissions) {
          await db.query(`
            INSERT IGNORE INTO role_permissions (role_id, permission_id, granted_by)
            VALUES (1, ?, 1)
          `, [perm.id]);
          console.log(`   ✅ Granted ${perm.name} to admin`);
        }
      }
    } else {
      console.log('❌ No admin_menus permissions found');
    }

    // 3. Test permission check
    console.log('\n5. Testing permission check:');
    const permissionService = require('../services/permissionService');
    
    const hasMenuPermission = await permissionService.checkUserPermission(1, 'browse_admin_menus');
    console.log(`✅ Admin can browse admin_menus: ${hasMenuPermission}`);

    const hasEditMenuPermission = await permissionService.checkUserPermission(1, 'edit_admin_menus');
    console.log(`✅ Admin can edit admin_menus: ${hasEditMenuPermission}`);

    const hasAddMenuPermission = await permissionService.checkUserPermission(1, 'add_admin_menus');
    console.log(`✅ Admin can add admin_menus: ${hasAddMenuPermission}`);

    const hasDeleteMenuPermission = await permissionService.checkUserPermission(1, 'delete_admin_menus');
    console.log(`✅ Admin can delete admin_menus: ${hasDeleteMenuPermission}`);

  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    process.exit(0);
  }
}

checkAdminPermissions();
