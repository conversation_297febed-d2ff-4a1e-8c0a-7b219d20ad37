const db = require('../config/database');

class PermissionService {
  constructor() {
    // Cache permissions để tăng hiệu suất
    this.permissionCache = new Map();
    this.userPermissionCache = new Map();
    this.cacheTimeout = 5 * 60 * 1000; // 5 phút
  }

  /**
   * Tạo quyền tự động cho bảng mới
   * @param {string} tableName - Tên bảng
   * @param {string} displayName - Tên hiển thị của bảng
   */
  async createTablePermissions(tableName, displayName) {
    try {
      console.log(`🔐 Creating permissions for table: ${tableName}`);
      
      const actions = ['read', 'edit', 'add', 'delete'];
      const permissions = [];

      for (const action of actions) {
        const permissionName = `${action}_${tableName}`;
        const permissionDisplayName = `${this.capitalizeFirst(action)} ${displayName}`;
        const description = this.getActionDescription(action, displayName);

        // Ki<PERSON>m tra xem quyền đã tồn tại chưa
        const existingPermission = await db.queryOne(`
          SELECT id FROM permissions WHERE name = ?
        `, [permissionName]);

        if (!existingPermission) {
          await db.query(`
            INSERT INTO permissions (name, display_name, description, table_name, action)
            VALUES (?, ?, ?, ?, ?)
          `, [permissionName, permissionDisplayName, description, tableName, action]);

          permissions.push({
            name: permissionName,
            display_name: permissionDisplayName,
            action: action
          });
        }
      }

      // Xóa cache để reload permissions
      this.clearCache();
      
      console.log(`✅ Created ${permissions.length} permissions for ${tableName}`);
      return permissions;
    } catch (error) {
      console.error('Error creating table permissions:', error);
      throw error;
    }
  }

  /**
   * Tạo quyền cho system modules (Tables, Menus, Roles)
   */
  async createSystemPermissions() {
    try {
      const systemModules = [
        { name: 'tables', display_name: 'Tables', table_name: 'admintable' },
        { name: 'menus', display_name: 'Menus', table_name: 'admin_menus' },
        { name: 'roles', display_name: 'Roles', table_name: 'role' },
        { name: 'permissions', display_name: 'Permissions', table_name: 'permissions' }
      ];

      for (const module of systemModules) {
        await this.createTablePermissions(module.name, module.display_name);
      }

      console.log('✅ System permissions created successfully');
    } catch (error) {
      console.error('Error creating system permissions:', error);
      throw error;
    }
  }

  /**
   * Kiểm tra user có quyền cụ thể hay không
   * @param {number} userId - ID của user
   * @param {string} permissionName - Tên quyền cần kiểm tra
   * @param {string} tableName - Tên bảng (optional)
   * @param {string} action - Hành động (optional)
   */
  async checkUserPermission(userId, permissionName = null, tableName = null, action = null) {
    try {
      // Tạo cache key
      const cacheKey = `user_${userId}_${permissionName || `${tableName}_${action}`}`;
      
      // Kiểm tra cache
      const cached = this.userPermissionCache.get(cacheKey);
      if (cached && (Date.now() - cached.timestamp) < this.cacheTimeout) {
        return cached.hasPermission;
      }

      let whereClause = '';
      let params = [userId];

      if (permissionName) {
        whereClause = 'AND p.name = ?';
        params.push(permissionName);
      } else if (tableName && action) {
        whereClause = 'AND p.table_name = ? AND p.action = ?';
        params.push(tableName, action);
      } else {
        throw new Error('Must provide either permissionName or both tableName and action');
      }

      const result = await db.queryOne(`
        SELECT COUNT(*) as count
        FROM role_permissions rp
        INNER JOIN permissions p ON rp.permission_id = p.id
        INNER JOIN role_user ur ON rp.role_id = ur.role_id
        WHERE ur.user_id = ? ${whereClause}
      `, params);

      const hasPermission = result.count > 0;

      // Cache kết quả
      this.userPermissionCache.set(cacheKey, {
        hasPermission,
        timestamp: Date.now()
      });

      return hasPermission;
    } catch (error) {
      console.error('Error checking user permission:', error);
      return false;
    }
  }

  /**
   * Lấy tất cả permissions của user
   * @param {number} userId - ID của user
   */
  async getUserPermissions(userId) {
    try {
      const cacheKey = `user_all_permissions_${userId}`;
      
      // Kiểm tra cache
      const cached = this.userPermissionCache.get(cacheKey);
      if (cached && (Date.now() - cached.timestamp) < this.cacheTimeout) {
        return cached.permissions;
      }

      const permissions = await db.query(`
        SELECT DISTINCT p.name, p.display_name, p.table_name, p.action
        FROM role_permissions rp
        INNER JOIN permissions p ON rp.permission_id = p.id
        INNER JOIN role_user ur ON rp.role_id = ur.role_id
        WHERE ur.user_id = ?
        ORDER BY p.table_name, p.action
      `, [userId]);

      // Cache kết quả
      this.userPermissionCache.set(cacheKey, {
        permissions,
        timestamp: Date.now()
      });

      return permissions;
    } catch (error) {
      console.error('Error getting user permissions:', error);
      return [];
    }
  }

  /**
   * Gán quyền cho role
   * @param {number} roleId - ID của role
   * @param {number} permissionId - ID của permission
   * @param {number} grantedBy - ID của user thực hiện gán quyền
   */
  async grantPermissionToRole(roleId, permissionId, grantedBy = null) {
    try {
      await db.query(`
        INSERT IGNORE INTO role_permissions (role_id, permission_id, granted_by)
        VALUES (?, ?, ?)
      `, [roleId, permissionId, grantedBy]);

      // Xóa cache
      this.clearUserPermissionCache();
      
      console.log(`✅ Granted permission ${permissionId} to role ${roleId}`);
      return true;
    } catch (error) {
      console.error('Error granting permission to role:', error);
      throw error;
    }
  }

  /**
   * Thu hồi quyền từ role
   * @param {number} roleId - ID của role
   * @param {number} permissionId - ID của permission
   */
  async revokePermissionFromRole(roleId, permissionId) {
    try {
      await db.query(`
        DELETE FROM role_permissions 
        WHERE role_id = ? AND permission_id = ?
      `, [roleId, permissionId]);

      // Xóa cache
      this.clearUserPermissionCache();
      
      console.log(`✅ Revoked permission ${permissionId} from role ${roleId}`);
      return true;
    } catch (error) {
      console.error('Error revoking permission from role:', error);
      throw error;
    }
  }

  /**
   * Lấy tất cả permissions của role
   * @param {number} roleId - ID của role
   */
  async getRolePermissions(roleId) {
    try {
      return await db.query(`
        SELECT p.id, p.name, p.display_name, p.table_name, p.action, p.description,
               rp.granted_at, rp.granted_by
        FROM role_permissions rp
        INNER JOIN permissions p ON rp.permission_id = p.id
        WHERE rp.role_id = ?
        ORDER BY p.table_name, p.action
      `, [roleId]);
    } catch (error) {
      console.error('Error getting role permissions:', error);
      return [];
    }
  }

  /**
   * Lấy tất cả permissions
   */
  async getAllPermissions() {
    try {
      const cacheKey = 'all_permissions';
      
      // Kiểm tra cache
      const cached = this.permissionCache.get(cacheKey);
      if (cached && (Date.now() - cached.timestamp) < this.cacheTimeout) {
        return cached.permissions;
      }

      const permissions = await db.query(`
        SELECT id, name, display_name, description, table_name, action,
               created_at, updated_at
        FROM permissions
        ORDER BY table_name, action
      `);

      // Cache kết quả
      this.permissionCache.set(cacheKey, {
        permissions,
        timestamp: Date.now()
      });

      return permissions;
    } catch (error) {
      console.error('Error getting all permissions:', error);
      return [];
    }
  }

  /**
   * Lấy permissions theo bảng
   * @param {string} tableName - Tên bảng
   */
  async getTablePermissions(tableName) {
    try {
      return await db.query(`
        SELECT id, name, display_name, description, action
        FROM permissions
        WHERE table_name = ?
        ORDER BY CASE action
          WHEN 'read' THEN 1
          WHEN 'edit' THEN 2
          WHEN 'add' THEN 3
          WHEN 'delete' THEN 4
          ELSE 5
        END
      `, [tableName]);
    } catch (error) {
      console.error('Error getting table permissions:', error);
      return [];
    }
  }

  /**
   * Đồng bộ permissions cho tất cả bảng hiện tại
   */
  async syncAllTablePermissions() {
    try {
      console.log('🔄 Syncing permissions for all tables...');
      
      const tables = await db.query(`
        SELECT name, display_name FROM admintable WHERE is_active = 1
      `);

      let totalCreated = 0;
      for (const table of tables) {
        const permissions = await this.createTablePermissions(table.name, table.display_name);
        totalCreated += permissions.length;
      }

      console.log(`✅ Sync completed: ${totalCreated} permissions processed for ${tables.length} tables`);
      return { tablesProcessed: tables.length, permissionsCreated: totalCreated };
    } catch (error) {
      console.error('Error syncing table permissions:', error);
      throw error;
    }
  }

  /**
   * Xóa permissions cho bảng đã bị xóa
   * @param {string} tableName - Tên bảng
   */
  async removeTablePermissions(tableName) {
    try {
      // Xóa role_permissions trước
      await db.query(`
        DELETE rp FROM role_permissions rp
        INNER JOIN permissions p ON rp.permission_id = p.id
        WHERE p.table_name = ?
      `, [tableName]);

      // Xóa permissions
      const result = await db.query(`
        DELETE FROM permissions WHERE table_name = ?
      `, [tableName]);

      // Xóa cache
      this.clearCache();
      
      console.log(`✅ Removed permissions for table: ${tableName}`);
      return result.affectedRows;
    } catch (error) {
      console.error('Error removing table permissions:', error);
      throw error;
    }
  }

  /**
   * Xóa cache
   */
  clearCache() {
    this.permissionCache.clear();
    this.clearUserPermissionCache();
  }

  /**
   * Xóa cache permissions của user
   */
  clearUserPermissionCache() {
    this.userPermissionCache.clear();
  }

  /**
   * Helper functions
   */
  capitalizeFirst(str) {
    return str.charAt(0).toUpperCase() + str.slice(1);
  }

  getActionDescription(action, displayName) {
    const descriptions = {
      read: `Quyền xem ${displayName}`,
      edit: `Quyền chỉnh sửa ${displayName}`,
      add: `Quyền tạo ${displayName} mới`,
      delete: `Quyền xóa ${displayName}`
    };
    return descriptions[action] || `Quyền ${action} ${displayName}`;
  }

  /**
   * Kiểm tra user có phải là Admin không
   * @param {number} userId - ID của user
   */
  async isAdmin(userId) {
    try {
      const result = await db.queryOne(`
        SELECT COUNT(*) as count
        FROM role_user ur
        INNER JOIN role r ON ur.role_id = r.id
        WHERE ur.user_id = ? AND r.name = 'Admin'
      `, [userId]);

      return result.count > 0;
    } catch (error) {
      console.error('Error checking admin:', error);
      return false;
    }
  }

  /**
   * Tạo permission động
   * @param {Object} permissionData - Dữ liệu permission
   */
  async createPermission(permissionData) {
    try {
      const { name, display_name, description, table_name, action } = permissionData;
      
      const result = await db.query(`
        INSERT INTO permissions (name, display_name, description, table_name, action)
        VALUES (?, ?, ?, ?, ?)
      `, [name, display_name, description, table_name, action]);

      // Xóa cache
      this.clearCache();

      return result.insertId;
    } catch (error) {
      console.error('Error creating permission:', error);
      throw error;
    }
  }

  /**
   * Cập nhật permission
   * @param {number} permissionId - ID của permission
   * @param {Object} permissionData - Dữ liệu cập nhật
   */
  async updatePermission(permissionId, permissionData) {
    try {
      const { display_name, description } = permissionData;
      
      await db.query(`
        UPDATE permissions 
        SET display_name = ?, description = ?, updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
      `, [display_name, description, permissionId]);

      // Xóa cache
      this.clearCache();

      return true;
    } catch (error) {
      console.error('Error updating permission:', error);
      throw error;
    }
  }

  /**
   * Xóa permission
   * @param {number} permissionId - ID của permission
   */
  async deletePermission(permissionId) {
    try {
      // Xóa role_permissions trước
      await db.query(`
        DELETE FROM role_permissions WHERE permission_id = ?
      `, [permissionId]);

      // Xóa permission
      await db.query(`
        DELETE FROM permissions WHERE id = ?
      `, [permissionId]);

      // Xóa cache
      this.clearCache();

      return true;
    } catch (error) {
      console.error('Error deleting permission:', error);
      throw error;
    }
  }
}

module.exports = new PermissionService(); 