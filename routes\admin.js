var express = require('express');
var router = express.Router();
const adminController = require('../controller/adminController');
const menuController = require('../controller/menuController');
const commonService = require('../services/commonService');
const { checkPermission, requireAdmin, checkSpecificPermission } = require('../middleware/permissionMiddleware');

// Middleware kiểm tra đăng nhập cho tất cả routes admin
router.use(commonService.isAuthenticated);

// ==================== DASHBOARD ====================
router.get('/', adminController.index);

// ==================== TABLES MANAGEMENT ====================
router.get('/tables', checkSpecificPermission('read_admintable'), adminController.tables);
router.get('/tables/data', checkSpecificPermission('read_admintable'), adminController.getTablesData);
router.post('/tables', checkSpecificPermission('add_admintable'), adminController.createTable);
router.get('/tables/:id', checkSpecificPermission('read_admintable'), adminController.getTable);
router.put('/tables/:id', checkSpecificPermission('edit_admintable'), adminController.updateTable);
router.delete('/tables/:id', checkSpecificPermission('delete_admintable'), adminController.deleteTable);

// ==================== TABLE STRUCTURE MANAGEMENT ====================
router.get('/tables/:tableId/structure', checkSpecificPermission('read_admintable'), adminController.tableStructure);
router.post('/tables/:tableId/structure', checkSpecificPermission('edit_admintable'), adminController.updateTableStructure);

// ==================== COLUMNS MANAGEMENT ====================
router.post('/tables/:tableId/columns', checkSpecificPermission('add_admincolumn'), adminController.addColumn);
router.put('/columns/:id', checkSpecificPermission('edit_admincolumn'), adminController.updateColumn);
router.delete('/columns/:id', checkSpecificPermission('delete_admincolumn'), adminController.deleteColumn);

// ==================== RELATIONS MANAGEMENT ====================
router.post('/relations', checkSpecificPermission('add_adminrelation'), adminController.createRelation);
router.delete('/relations/:id', checkSpecificPermission('delete_adminrelation'), adminController.deleteRelation);

// ==================== DYNAMIC CRUD OPERATIONS ====================
// Middleware để check quyền dựa trên tableId
const checkTablePermission = (action) => {
  return async (req, res, next) => {
    try {
      const { tableId } = req.params;
      const adminService = require('../services/adminService');
      const table = await adminService.getAdminTableById(parseInt(tableId));
      
      if (!table) {
        return res.status(404).json({ success: false, message: 'Table not found' });
      }
      
      // Gọi middleware kiểm tra permission với tên bảng
      const permissionMiddleware = checkPermission(action, table.name);
      return permissionMiddleware(req, res, next);
    } catch (error) {
      console.error('Table permission middleware error:', error);
      return res.status(500).json({ success: false, message: 'Permission check failed' });
    }
  };
};

// ⭐ Updated to use 5-action system (browse, read, edit, add, delete)
router.get('/tables/:tableId/data', checkTablePermission('browse'), adminController.tableData);
router.get('/tables/:tableId/data/api', checkTablePermission('browse'), adminController.getTableDataApi);
router.get('/tables/:tableId/records/:recordId', checkTablePermission('read'), adminController.getRecord);
router.get('/tables/:tableId/records/:recordId/related', checkTablePermission('read'), adminController.checkRelatedRecords);
router.post('/tables/:tableId/records', checkTablePermission('add'), adminController.createRecord);
router.put('/tables/:tableId/records/:recordId', checkTablePermission('edit'), adminController.updateRecord);
router.delete('/tables/:tableId/records/:recordId', checkTablePermission('delete'), adminController.deleteRecord);
router.get('/dropdown/:tableName', adminController.getDropdownData); // Public API for dropdowns
router.get('/tables/:tableId/relations/:columnName/dropdown', checkTablePermission('browse'), adminController.getRelationDropdownData);

// ==================== UTILITY ENDPOINTS ====================
router.post('/sync-tables', requireAdmin(), adminController.syncTables);
router.get('/tables/:tableId/columns', checkSpecificPermission('read_admintable'), adminController.getTableColumns);

// ==================== MENU MANAGEMENT ====================
router.get('/menus', checkPermission('browse', 'admin_menus'), menuController.index);
router.get('/menus/data', checkPermission('browse', 'admin_menus'), menuController.getMenusData);
router.get('/menus/:id', checkPermission('read', 'admin_menus'), menuController.getMenu);
router.post('/menus', checkPermission('add', 'admin_menus'), menuController.createMenu);
router.put('/menus/:id', checkPermission('edit', 'admin_menus'), menuController.updateMenu);
router.delete('/menus/:id', checkPermission('delete', 'admin_menus'), menuController.deleteMenu);
router.post('/menus/sync-tables', requireAdmin(), menuController.syncTableMenus);
router.post('/menus/update-order', checkPermission('edit', 'admin_menus'), menuController.updateMenuOrder);
router.post('/menus/create-defaults', requireAdmin(), menuController.createDefaultMenus);

// ==================== MENU API ENDPOINTS ====================
router.get('/api/sidebar-menus', menuController.getSidebarMenus); // Public for sidebar
router.get('/api/parent-menus', checkPermission('browse', 'admin_menus'), menuController.getParentMenus);
router.get('/api/admin-tables', checkSpecificPermission('read_admintable'), menuController.getAdminTables);

// ==================== PERMISSION MANAGEMENT ====================
const permissionController = require('../controller/permissionController');

// API endpoints first (to avoid route conflicts)
router.get('/permissions/api/data', checkPermission('browse', 'permissions'), permissionController.api);
router.get('/permissions/api/filter-options', checkPermission('browse', 'permissions'), permissionController.filterOptions);
router.post('/permissions/sync', requireAdmin(), permissionController.syncPermissions);
router.get('/permissions/table/:tableName', checkPermission('browse', 'permissions'), permissionController.getTablePermissions);
router.post('/permissions/table/create', requireAdmin(), permissionController.createTablePermissions);

// Role permissions management
router.get('/permissions/role/:roleId/permissions', checkPermission('edit', 'role'), permissionController.rolePermissions);
router.post('/permissions/role/:roleId/permissions', checkPermission('edit', 'role'), permissionController.updateRolePermissions);

// Main permission CRUD routes
router.get('/permissions', checkPermission('browse', 'permissions'), permissionController.index);
router.get('/permissions/create', checkPermission('add', 'permissions'), permissionController.create);
router.post('/permissions', checkPermission('add', 'permissions'), permissionController.store);
router.get('/permissions/:id/edit', checkPermission('edit', 'permissions'), permissionController.edit);
router.get('/permissions/:id', checkPermission('read', 'permissions'), permissionController.show);
router.put('/permissions/:id', checkPermission('edit', 'permissions'), permissionController.update);
router.delete('/permissions/:id', checkPermission('delete', 'permissions'), permissionController.destroy);

// User permissions API
router.get('/api/my-permissions', permissionController.myPermissions);

module.exports = router;
