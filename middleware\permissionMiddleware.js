const permissionService = require('../services/permissionService');

/**
 * Middleware kiểm tra quyền truy cập
 * @param {string} action - Hành động cần kiểm tra (read, edit, add, delete)
 * @param {string} tableName - <PERSON><PERSON><PERSON> bả<PERSON> (optional, sẽ lấy từ params nếu không có)
 */
function checkPermission(action, tableName = null) {
  return async (req, res, next) => {
    try {
      // Kiểm tra user đã đăng nhập chưa
      if (!req.user || !req.user.id) {
        return res.status(401).json({
          success: false,
          message: 'Bạn cần đăng nhập để tiếp tục'
        });
      }

      const userId = req.user.id;
      
      // Kiểm tra Admin - Admin có tất cả quyền
      const isAdmin = await permissionService.isAdmin(userId);
      if (isAdmin) {
        return next();
      }

      // Lấy tableName từ params nếu không được cung cấp
      const targetTable = tableName || req.params.tableName;
      
      if (!targetTable) {
        return res.status(400).json({
          success: false,
          message: 'Không xác định được bảng cần kiểm tra quyền'
        });
      }

      // Kiểm tra quyền
      const hasPermission = await permissionService.checkUserPermission(
        userId, 
        null, 
        targetTable, 
        action
      );

      if (!hasPermission) {
        return res.status(403).json({
          success: false,
          message: `Bạn không có quyền ${getActionDisplayName(action)} ${targetTable}`,
          required_permission: `${action}_${targetTable}`
        });
      }

      // Lưu thông tin permission đã kiểm tra vào request để sử dụng sau này
      req.checkedPermission = {
        action,
        tableName: targetTable,
        userId
      };

      next();
    } catch (error) {
      console.error('Permission middleware error:', error);
      return res.status(500).json({
        success: false,
        message: 'Lỗi kiểm tra quyền truy cập'
      });
    }
  };
}

/**
 * Middleware kiểm tra quyền theo tên cụ thể
 * @param {string} permissionName - Tên quyền cần kiểm tra
 */
function checkSpecificPermission(permissionName) {
  return async (req, res, next) => {
    try {
      if (!req.user || !req.user.id) {
        return res.status(401).json({
          success: false,
          message: 'Bạn cần đăng nhập để tiếp tục'
        });
      }

      const userId = req.user.id;
      
      // Kiểm tra Admin
      const isAdmin = await permissionService.isAdmin(userId);
      if (isAdmin) {
        return next();
      }

      // Kiểm tra quyền cụ thể
      const hasPermission = await permissionService.checkUserPermission(userId, permissionName);

      if (!hasPermission) {
        return res.status(403).json({
          success: false,
          message: `Bạn không có quyền ${permissionName}`,
          required_permission: permissionName
        });
      }

      req.checkedPermission = {
        permissionName,
        userId
      };

      next();
    } catch (error) {
      console.error('Specific permission middleware error:', error);
      return res.status(500).json({
        success: false,
        message: 'Lỗi kiểm tra quyền truy cập'
      });
    }
  };
}

/**
 * Middleware kiểm tra quyền cho nhiều hành động (OR logic)
 * User chỉ cần có 1 trong các quyền được liệt kê
 * @param {Array} permissions - Mảng các quyền cần kiểm tra
 */
function checkAnyPermission(permissions) {
  return async (req, res, next) => {
    try {
      if (!req.user || !req.user.id) {
        return res.status(401).json({
          success: false,
          message: 'Bạn cần đăng nhập để tiếp tục'
        });
      }

      const userId = req.user.id;
      
      // Kiểm tra Admin
      const isAdmin = await permissionService.isAdmin(userId);
      if (isAdmin) {
        return next();
      }

      // Kiểm tra từng quyền
      let hasAnyPermission = false;
      let checkedPermissions = [];

      for (const permission of permissions) {
        let hasPermission = false;
        
        if (typeof permission === 'string') {
          // Quyền theo tên
          hasPermission = await permissionService.checkUserPermission(userId, permission);
          checkedPermissions.push(permission);
        } else if (permission.action && permission.tableName) {
          // Quyền theo action + table
          hasPermission = await permissionService.checkUserPermission(
            userId, null, permission.tableName, permission.action
          );
          checkedPermissions.push(`${permission.action}_${permission.tableName}`);
        }

        if (hasPermission) {
          hasAnyPermission = true;
          break;
        }
      }

      if (!hasAnyPermission) {
        return res.status(403).json({
          success: false,
          message: 'Bạn không có quyền thực hiện hành động này',
          required_permissions: checkedPermissions
        });
      }

      next();
    } catch (error) {
      console.error('Any permission middleware error:', error);
      return res.status(500).json({
        success: false,
        message: 'Lỗi kiểm tra quyền truy cập'
      });
    }
  };
}

/**
 * Middleware kiểm tra tất cả quyền (AND logic)
 * User phải có tất cả các quyền được liệt kê
 * @param {Array} permissions - Mảng các quyền cần kiểm tra
 */
function checkAllPermissions(permissions) {
  return async (req, res, next) => {
    try {
      if (!req.user || !req.user.id) {
        return res.status(401).json({
          success: false,
          message: 'Bạn cần đăng nhập để tiếp tục'
        });
      }

      const userId = req.user.id;
      
      // Kiểm tra Admin
      const isAdmin = await permissionService.isAdmin(userId);
      if (isAdmin) {
        return next();
      }

      // Kiểm tra tất cả quyền
      let missingPermissions = [];

      for (const permission of permissions) {
        let hasPermission = false;
        let permissionName = '';
        
        if (typeof permission === 'string') {
          hasPermission = await permissionService.checkUserPermission(userId, permission);
          permissionName = permission;
        } else if (permission.action && permission.tableName) {
          hasPermission = await permissionService.checkUserPermission(
            userId, null, permission.tableName, permission.action
          );
          permissionName = `${permission.action}_${permission.tableName}`;
        }

        if (!hasPermission) {
          missingPermissions.push(permissionName);
        }
      }

      if (missingPermissions.length > 0) {
        return res.status(403).json({
          success: false,
          message: 'Bạn không có đủ quyền để thực hiện hành động này',
          missing_permissions: missingPermissions
        });
      }

      next();
    } catch (error) {
      console.error('All permissions middleware error:', error);
      return res.status(500).json({
        success: false,
        message: 'Lỗi kiểm tra quyền truy cập'
      });
    }
  };
}

/**
 * Middleware kiểm tra quyền chỉ với Admin
 */
function requireAdmin() {
  return async (req, res, next) => {
    try {
      if (!req.user || !req.user.id) {
        return res.status(401).json({
          success: false,
          message: 'Bạn cần đăng nhập để tiếp tục'
        });
      }

      const isAdmin = await permissionService.isAdmin(req.user.id);
      
      if (!isAdmin) {
        return res.status(403).json({
          success: false,
          message: 'Chỉ Admin mới có quyền thực hiện hành động này'
        });
      }

      next();
    } catch (error) {
      console.error('Admin middleware error:', error);
      return res.status(500).json({
        success: false,
        message: 'Lỗi kiểm tra quyền Admin'
      });
    }
  };
}

/**
 * Middleware để inject thông tin permissions của user vào request
 * Không block request, chỉ thêm thông tin
 */
function injectUserPermissions() {
  return async (req, res, next) => {
    try {
      if (req.user && req.user.id) {
        req.user.permissions = await permissionService.getUserPermissions(req.user.id);
        req.user.isAdmin = await permissionService.isAdmin(req.user.id);
      }
      next();
    } catch (error) {
      console.error('Inject user permissions error:', error);
      next(); // Không block request nếu có lỗi
    }
  };
}

/**
 * Helper function để tạo middleware cho CRUD chuẩn
 * @param {string} tableName - Tên bảng
 */
function createCrudPermissions(tableName) {
  return {
    read: checkPermission('read', tableName),
    edit: checkPermission('edit', tableName),
    add: checkPermission('add', tableName),
    delete: checkPermission('delete', tableName)
  };
}

/**
 * Helper function để get display name của action
 */
function getActionDisplayName(action) {
  const displayNames = {
    read: 'xem',
    edit: 'chỉnh sửa',
    add: 'thêm mới',
    delete: 'xóa'
  };
  return displayNames[action] || action;
}

/**
 * Middleware để log các lần kiểm tra quyền (cho debugging)
 */
function logPermissionCheck() {
  return (req, res, next) => {
    if (process.env.NODE_ENV === 'development' && req.checkedPermission) {
      console.log(`🔍 Permission checked: ${JSON.stringify(req.checkedPermission)}`);
    }
    next();
  };
}

module.exports = {
  checkPermission,
  checkSpecificPermission,
  checkAnyPermission,
  checkAllPermissions,
  requireAdmin,
  injectUserPermissions,
  createCrudPermissions,
  logPermissionCheck
}; 