# Database Setup Guide - Backend CoreUI Project

## 📋 Tổng quan

File `scripts/run-database-setup.js` là script setup database hoàn chỉnh cho dự án Backend CoreUI - một hệ thống admin tương tự Laravel Voyager với đầy đủ các chức năng:

### ✅ Cá<PERSON> chức năng được setup:

1. **User Management System** - H<PERSON> thống quản lý người dùng
2. **Role-Based Permission System** - Hệ thống phân quyền theo vai trò
3. **Dynamic CRUD Admin Interface** - Giao diện admin CRUD động
4. **Database Table Management** - Quản lý cấu trúc bảng database
5. **Foreign Key Relationships** - <PERSON>uan hệ khóa ngoại
6. **Dynamic Menu System** - Hệ thống menu động
7. **Session Management** - Quản lý phiên đăng nhập
8. **File Upload Support** - Hỗ trợ upload file
9. **Search & Pagination** - T<PERSON><PERSON> kiếm và phân trang
10. **Data Validation** - Validation dữ liệu

## 🗄️ Cấu trúc Database

### Bảng chính:

1. **`user`** - Người dùng hệ thống
2. **`role`** - Vai trò người dùng
3. **`permissions`** - Quyền hệ thống
4. **`role_user`** - Liên kết người dùng - vai trò
5. **`role_permissions`** - Liên kết vai trò - quyền
6. **`user_sessions`** - Phiên đăng nhập
7. **`admintable`** - Metadata bảng admin
8. **`admincolumn`** - Metadata cột admin
9. **`adminrelation`** - Quan hệ khóa ngoại
10. **`admin_menus`** - Menu hệ thống

## 🚀 Cách sử dụng

### 1. Chuẩn bị môi trường

Đảm bảo file `.env` có cấu hình database đúng:

```env
DB_HOST=localhost
DB_USER=root
DB_PASSWORD=your_password
DB_NAME=coreui
```

### 2. Chạy setup database

```bash
# Chạy script setup hoàn chỉnh
node scripts/run-database-setup.js

# Hoặc test connection trước
node scripts/test-db-connection.js
```

### 3. Kết quả mong đợi

Script sẽ:
- ✅ Tự động tạo database nếu chưa có
- ✅ Drop và tạo lại tất cả bảng
- ✅ Insert dữ liệu mẫu
- ✅ Thiết lập quan hệ khóa ngoại
- ✅ Verify toàn bộ setup
- ✅ Test các chức năng chính

## 👥 Tài khoản mẫu

### Admin (Toàn quyền)
- **Email:** <EMAIL>
- **Password:** password123
- **Quyền:** Tất cả chức năng

### Manager (Quản lý)
- **Email:** <EMAIL>
- **Password:** password123
- **Quyền:** Quản lý hạn chế

### Editor (Biên tập)
- **Email:** <EMAIL>
- **Password:** password123
- **Quyền:** Quản lý nội dung

### User (Người dùng)
- **Email:** <EMAIL>
- **Password:** password123
- **Quyền:** Truy cập cơ bản

## 🔧 Troubleshooting

### Lỗi thường gặp:

1. **"Unknown database"**
   - Script sẽ tự động tạo database
   - Kiểm tra quyền CREATE DATABASE

2. **"Access denied"**
   - Kiểm tra username/password trong .env
   - Đảm bảo user có quyền CREATE/DROP

3. **"Connection refused"**
   - Kiểm tra MySQL server đang chạy
   - Kiểm tra host/port trong .env

### Debug:

```bash
# Test connection đơn giản
node scripts/test-db-connection.js

# Xem log chi tiết
node scripts/run-database-setup.js 2>&1 | tee setup.log
```

## 📊 Thống kê Setup

Sau khi chạy thành công, bạn sẽ thấy:

- ✅ **10 bảng** được tạo
- ✅ **4 người dùng** mẫu
- ✅ **4 vai trò** hệ thống
- ✅ **30+ quyền** được định nghĩa
- ✅ **45+ liên kết** role-permission
- ✅ **7 bảng admin** được cấu hình
- ✅ **26+ cột admin** metadata
- ✅ **2 quan hệ** khóa ngoại

## 🎯 Bước tiếp theo

1. **Khởi động ứng dụng:**
   ```bash
   npm start
   ```

2. **Truy cập admin:**
   ```
   http://localhost:3000/admin
   ```

3. **Đăng nhập với tài khoản admin**

4. **Khám phá các chức năng:**
   - User Management → Quản lý người dùng
   - System Management → Quản lý bảng
   - Dynamic CRUD → Thêm/sửa/xóa dữ liệu

## 📝 Lưu ý quan trọng

- ⚠️ Script sẽ **XÓA TẤT CẢ** dữ liệu hiện có
- ✅ Luôn backup database trước khi chạy
- ✅ Chỉ chạy trong môi trường development
- ✅ Đổi password mặc định trong production

## 🔄 Chạy lại Setup

Nếu cần chạy lại setup:

```bash
# Backup dữ liệu quan trọng trước
mysqldump -u root -p coreui > backup.sql

# Chạy lại setup
node scripts/run-database-setup.js
```

## 🛠️ Scripts Available

### 1. Complete Database Manager (Recommended)
```bash
# Hiển thị tất cả commands
node scripts/complete-database-manager.js help

# Setup database hoàn chỉnh
node scripts/complete-database-manager.js setup

# Kiểm tra tính toàn vẹn
node scripts/complete-database-manager.js verify

# Backup database
node scripts/complete-database-manager.js backup

# Reset database (backup + setup mới)
node scripts/complete-database-manager.js reset

# Xem trạng thái database
node scripts/complete-database-manager.js status
```

### 2. Individual Scripts
```bash
# Setup database
node scripts/run-database-setup.js

# Verify integrity
node scripts/verify-database-integrity.js

# Test connection
node scripts/test-db-connection.js
```

## 📁 Files Created

- `scripts/run-database-setup.js` - Script setup database chính
- `scripts/verify-database-integrity.js` - Script kiểm tra tính toàn vẹn
- `scripts/test-db-connection.js` - Script test connection
- `scripts/complete-database-manager.js` - Manager tổng hợp tất cả
- `database/complete_database_schema.sql` - Schema SQL hoàn chỉnh
- `DATABASE_SETUP_README.md` - Hướng dẫn này

## 🎯 Quick Start

```bash
# 1. Setup database hoàn chỉnh
node scripts/complete-database-manager.js setup

# 2. Verify everything is OK
node scripts/complete-database-manager.js verify

# 3. Start application
npm start

# 4. Open browser
# http://localhost:3000/admin
```

---

**🎉 Chúc bạn sử dụng hệ thống admin thành công!**

**💡 Tip:** Sử dụng `complete-database-manager.js` cho tất cả các tác vụ database management!
