const db = require('../config/database');
const { Validator } = require('../utils/validation');
const commonService = require('./commonService');

class DynamicCrudService {
  constructor() {
    // Sử dụng database connection pool
  }

  // Lấy dữ liệu với phân trang và tìm kiếm
  async getTableData(tableName, options = {}) {
    try {
      const {
        page = 1,
        perPage = 25,
        search = '',
        orderBy = 'id',
        orderDirection = 'desc',
        filters = {}
      } = options;

      // Lấy thông tin bảng admin
      const adminTable = await db.queryOne(`
        SELECT * FROM admintable WHERE name = ?
      `, [tableName]);

      if (!adminTable) {
        throw new Error('Table not found in admin configuration');
      }

      // Lấy columns
      const columns = await db.query(`
        SELECT * FROM admincolumn 
        WHERE table_id = ? AND is_visible_list = true
        ORDER BY order_index ASC
      `, [adminTable.id]);

      // Lấy relations
      const relations = await db.query(`
        SELECT ar.*, ft.name as foreign_table_name, ac.name as column_name
        FROM adminrelation ar
        LEFT JOIN admintable ft ON ar.foreign_table_id = ft.id
        LEFT JOIN admincolumn ac ON ar.column_id = ac.id
        WHERE ar.table_id = ?
      `, [adminTable.id]);

      adminTable.columns = columns;
      adminTable.relations = relations;

      // Xây dựng điều kiện WHERE
      let whereConditions = [];
      let queryParams = [];

      // Thêm điều kiện tìm kiếm
      if (search) {
        const searchableColumns = columns.filter(col => col.is_searchable);
        const searchConditions = [];
        
        // Thêm điều kiện tìm kiếm cho các cột searchable khác
        if (searchableColumns.length > 0) {
          const columnSearchConditions = searchableColumns.map(col => `\`${col.name}\` LIKE ?`);
          searchConditions.push(...columnSearchConditions);
          searchableColumns.forEach(() => queryParams.push(`%${search}%`));
        }
        
        whereConditions.push(`(${searchConditions.join(' OR ')})`);
      }

      // Thêm filters
      Object.keys(filters).forEach(key => {
        if (filters[key] !== null && filters[key] !== undefined && filters[key] !== '') {
          whereConditions.push(`\`${key}\` = ?`);
          queryParams.push(filters[key]);
        }
      });

      // Xây dựng câu query
      // Luôn bao gồm trường id, sau đó thêm các cột visible
      const idColumn = '`id`';
      const visibleColumns = columns.map(col => `\`${col.name}\``).join(', ');
      let selectColumns = visibleColumns;
      
      // Đảm bảo id được bao gồm (nếu chưa có trong visible columns)
      if (!columns.find(col => col.name === 'id')) {
        selectColumns = `${idColumn}, ${visibleColumns}`;
      }
      
      let whereClause = whereConditions.length > 0 ? ` WHERE ${whereConditions.join(' AND ')}` : '';
      let orderClause = '';

      // Thêm ORDER BY
      if (orderBy) {
        // Nếu orderBy là 'id', luôn cho phép sort
        if (orderBy === 'id') {
          orderClause = ` ORDER BY \`${orderBy}\` ${orderDirection.toUpperCase()}`;
        } else {
          // Kiểm tra xem cột có sortable không
          const sortableColumn = columns.find(col => col.name === orderBy && col.is_sortable);
          if (sortableColumn) {
            orderClause = ` ORDER BY \`${orderBy}\` ${orderDirection.toUpperCase()}`;
          }
        }
      }

      // Thêm LIMIT và OFFSET
      const offset = (page - 1) * perPage;
      const limitClause = ` LIMIT ${perPage} OFFSET ${offset}`;

      // Thực hiện queries
      const dataQuery = `SELECT ${selectColumns} FROM \`${tableName}\`${whereClause}${orderClause}${limitClause}`;
      const countQuery = `SELECT COUNT(*) as total FROM \`${tableName}\`${whereClause}`;

      const [rows, countResult] = await Promise.all([
        db.query(dataQuery, queryParams),
        db.queryOne(countQuery, queryParams)
      ]);

      const total = Number(countResult.total);

      // Xử lý dữ liệu với relations
      const processedRows = await this.processRowsWithRelations(rows, adminTable);

      return {
        data: processedRows,
        total,
        totalFiltered: total,
        page,
        perPage,
        totalPages: Math.ceil(total / perPage)
      };
    } catch (error) {
      console.error('Error getting table data:', error);
      throw error;
    }
  }

  // Xử lý dữ liệu với relations
  async processRowsWithRelations(rows, adminTable) {
    if (!adminTable.relations || adminTable.relations.length === 0) {
      return rows;
    }

    for (const row of rows) {
      for (const relation of adminTable.relations) {
        // Get column name from relation object structure
        const columnName = relation.column_name || relation.column?.name;
        const foreignTableName = relation.foreign_table_name || relation.foreign_table?.name;
        const displayColumn = relation.display_column;
        const foreignColumn = relation.foreign_column;

        if (!columnName || !foreignTableName || !displayColumn || !foreignColumn) {
          console.warn('Incomplete relation data:', relation);
          continue;
        }

        const foreignKeyValue = row[columnName];

        if (foreignKeyValue) {
          try {
            const query = `SELECT \`${displayColumn}\` FROM \`${foreignTableName}\` WHERE \`${foreignColumn}\` = ?`;
            const foreignRows = await db.query(query, [foreignKeyValue]);

            if (foreignRows.length > 0) {
              row[`${columnName}_display`] = foreignRows[0][displayColumn];
            } else {
              row[`${columnName}_display`] = '(Không tồn tại)';
            }
          } catch (error) {
            console.error(`Error fetching relation data for ${columnName}:`, error);
            row[`${columnName}_display`] = '(Lỗi)';
          }
        } else {
          // Set empty display value for null foreign keys
          row[`${columnName}_display`] = '';
        }
      }
    }

    return rows;
  }

  // Lấy một record
  async getRecord(tableName, id) {
    try {
      // Lấy thông tin bảng admin
      const adminTable = await db.queryOne(`
        SELECT * FROM admintable WHERE name = ?
      `, [tableName]);

      if (!adminTable) {
        throw new Error('Table not found in admin configuration');
      }

      // Lấy columns
      const columns = await db.query(`
        SELECT * FROM admincolumn
        WHERE table_id = ?
      `, [adminTable.id]);

      // Lấy relations
      const relations = await db.query(`
        SELECT ar.*, ft.name as foreign_table_name, ac.name as column_name
        FROM adminrelation ar
        LEFT JOIN admintable ft ON ar.foreign_table_id = ft.id
        LEFT JOIN admincolumn ac ON ar.column_id = ac.id
        WHERE ar.table_id = ?
      `, [adminTable.id]);

      adminTable.columns = columns;
      adminTable.relations = relations;

      // Lấy record từ database
      const rows = await db.query(`SELECT * FROM \`${tableName}\` WHERE id = ?`, [id]);
      const record = rows[0] || null;

      if (!record) {
        return null;
      }

      // Xử lý dữ liệu với relations
      const processedRows = await this.processRowsWithRelations([record], adminTable);
      return processedRows[0];
    } catch (error) {
      console.error('Error getting record:', error);
      throw error;
    }
  }

  // Tạo record mới
  async createRecord(tableName, data) {
    try {
      // Lấy thông tin bảng admin
      const adminTable = await db.queryOne(`
        SELECT * FROM admintable WHERE name = ?
      `, [tableName]);

      if (!adminTable) {
        throw new Error('Table not found in admin configuration');
      }

      // Lấy columns
      const columns = await db.query(`
        SELECT * FROM admincolumn
        WHERE table_id = ?
      `, [adminTable.id]);

      // Lọc dữ liệu theo cấu hình columns
      const filteredData = {};
      columns.forEach(col => {
        if (data.hasOwnProperty(col.name) && col.name !== 'id' && col.is_visible_form) {
          filteredData[col.name] = data[col.name];
        }
      });

      // Sử dụng commonService.addRecordTable
      const result = await commonService.addRecordTable(filteredData, tableName);

      if (!result.success) {
        throw new Error(result.message);
      }

      // Trả về record vừa tạo
      return await this.getRecord(tableName, result.data.insertId);
    } catch (error) {
      console.error('Error creating record:', error);
      throw error;
    }
  }

  // Cập nhật record
  async updateRecord(tableName, id, data) {
    try {
      // Lấy thông tin bảng admin
      const adminTable = await db.queryOne(`
        SELECT * FROM admintable WHERE name = ?
      `, [tableName]);

      if (!adminTable) {
        throw new Error('Table not found in admin configuration');
      }

      // Lấy columns
      const columns = await db.query(`
        SELECT * FROM admincolumn
        WHERE table_id = ?
      `, [adminTable.id]);

      // Lọc dữ liệu theo cấu hình columns
      const filteredData = {};
      columns.forEach(col => {
        if (data.hasOwnProperty(col.name) && col.name !== 'id' && col.is_visible_form) {
          filteredData[col.name] = data[col.name];
        }
      });

      // Sử dụng commonService.updateRecordTable
      const result = await commonService.updateRecordTable(filteredData, { id: id }, tableName);

      if (!result.success) {
        throw new Error(result.message);
      }

      // Trả về record đã cập nhật
      return await this.getRecord(tableName, id);
    } catch (error) {
      console.error('Error updating record:', error);
      throw error;
    }
  }

  // Xóa record
  async deleteRecord(tableName, id) {
    try {
      // Thử xóa trực tiếp trước bằng commonService
      try {
        const result = await commonService.deleteRecordTable({ id: id }, {}, tableName);
        return result.success;
      } catch (deleteError) {
        // Nếu lỗi foreign key constraint, thử xóa cascade
        if (deleteError.code === 'ER_ROW_IS_REFERENCED_2') {
          console.log(`Foreign key constraint detected for ${tableName}:${id}, attempting cascade delete...`);
          return await this.deleteRecordWithCascade(tableName, id);
        }
        throw deleteError;
      }
    } catch (error) {
      console.error('Error deleting record:', error);
      throw error;
    }
  }

  // Xóa record với cascade (xóa tất cả foreign key references)
  async deleteRecordWithCascade(tableName, id, deletedRecords = new Set()) {
    const startTime = Date.now();
    let deletedCount = 0;
    const recordKey = `${tableName}:${id}`;

    try {
      // Kiểm tra để tránh infinite recursion
      if (deletedRecords.has(recordKey)) {
        console.log(`⚠️ Record ${recordKey} already processed, skipping to avoid infinite loop`);
        return true;
      }

      console.log(`🔥 Starting cascade delete for ${tableName}:${id}`);

      // Lấy danh sách các bảng tham chiếu đến bảng này
      const foreignKeyReferences = await this.getForeignKeyReferences(tableName);

      console.log(`📋 Found ${foreignKeyReferences.length} foreign key references for ${tableName}`);

      // Xóa tất cả records trong các bảng con trước
      for (const reference of foreignKeyReferences) {
        const childRecords = await db.query(
          `SELECT id FROM \`${reference.table_name}\` WHERE \`${reference.column_name}\` = ?`,
          [id]
        );

        console.log(`🔍 Found ${childRecords.length} child records in ${reference.table_name}`);

        // Xóa từng child record (có thể có cascade tiếp)
        for (const childRecord of childRecords) {
          const childKey = `${reference.table_name}:${childRecord.id}`;
          if (!deletedRecords.has(childKey)) {
            console.log(`  ➤ Deleting ${reference.table_name}:${childRecord.id}`);
            await this.deleteRecordWithCascade(reference.table_name, childRecord.id, deletedRecords);
            deletedCount++;
          }
        }
      }

      // Mark record as processed before deleting to avoid circular references
      deletedRecords.add(recordKey);

      // Sau khi xóa hết child records, xóa parent record
      console.log(`🎯 Deleting parent record ${tableName}:${id}`);
      const result = await commonService.deleteRecordTable({ id: id }, {}, tableName);

      if (result.success) {
        deletedCount++;
      }

      const duration = Date.now() - startTime;
      console.log(`✅ Cascade delete completed: ${deletedCount} records deleted in ${duration}ms`);

      return result.success;

    } catch (error) {
      const duration = Date.now() - startTime;
      console.error(`❌ Cascade delete failed after ${duration}ms:`, error);
      throw error;
    }
  }

  // Lấy danh sách các bảng tham chiếu đến bảng hiện tại
  async getForeignKeyReferences(tableName) {
    try {
      const query = `
        SELECT
          TABLE_NAME as table_name,
          COLUMN_NAME as column_name,
          CONSTRAINT_NAME as constraint_name,
          REFERENCED_TABLE_NAME as referenced_table_name,
          REFERENCED_COLUMN_NAME as referenced_column_name
        FROM
          information_schema.KEY_COLUMN_USAGE
        WHERE
          REFERENCED_TABLE_SCHEMA = DATABASE()
          AND REFERENCED_TABLE_NAME = ?
          AND REFERENCED_COLUMN_NAME = 'id'
      `;

      return await db.query(query, [tableName]);
    } catch (error) {
      console.error('Error getting foreign key references:', error);
      return [];
    }
  }

  // Kiểm tra các records liên quan trước khi xóa
  async getRelatedRecords(tableName, id) {
    try {
      const foreignKeyReferences = await this.getForeignKeyReferences(tableName);
      const relatedRecords = [];

      for (const reference of foreignKeyReferences) {
        const count = await db.queryOne(
          `SELECT COUNT(*) as count FROM \`${reference.table_name}\` WHERE \`${reference.column_name}\` = ?`,
          [id]
        );

        if (count.count > 0) {
          // Lấy một vài records mẫu để hiển thị
          const samples = await db.query(
            `SELECT * FROM \`${reference.table_name}\` WHERE \`${reference.column_name}\` = ? LIMIT 3`,
            [id]
          );

          relatedRecords.push({
            table: reference.table_name,
            column: reference.column_name,
            count: count.count,
            samples: samples
          });
        }
      }

      return relatedRecords;
    } catch (error) {
      console.error('Error getting related records:', error);
      return [];
    }
  }

  // Lấy dữ liệu cho dropdown
  async getDropdownData(tableName, valueColumn = 'id', displayColumn = null) {
    try {
      // Auto-detect display column if not provided
      if (!displayColumn) {
        displayColumn = await this.getDisplayColumnForTable(tableName);
      }

      const query = `SELECT \`${valueColumn}\`, \`${displayColumn}\` FROM \`${tableName}\` ORDER BY \`${displayColumn}\` ASC`;
      const rows = await db.query(query);
      // Chuyển đổi về dạng { value, label }
      return rows.map(row => ({
        value: row[valueColumn],
        label: row[displayColumn]
      }));
    } catch (error) {
      console.error('Error getting dropdown data:', error);
      throw error;
    }
  }

  // Helper function to auto-detect display column for a table
  async getDisplayColumnForTable(tableName) {
    try {
      // Get table structure
      const columns = await db.query(`DESCRIBE \`${tableName}\``);
      const columnNames = columns.map(col => col.Field);

      // Priority order for display columns
      const displayColumnPriority = [
        'name', 'title', 'fullname', 'display_name',
        'label', 'description', 'email', 'username'
      ];

      // Find the first matching column
      for (const priority of displayColumnPriority) {
        if (columnNames.includes(priority)) {
          return priority;
        }
      }

      // If no common display column found, use the second column (skip id)
      if (columnNames.length > 1) {
        return columnNames[1];
      }

      // Fallback to id if only one column
      return 'id';
    } catch (error) {
      console.error('Error detecting display column:', error);
      return 'id'; // Safe fallback
    }
  }

  // Validate dữ liệu using centralized Validator
  validateData(data, columns) {
    try {
      return Validator.validateRecordData(data, columns);
    } catch (error) {
      if (error.name === 'ValidationError' && error.errors) {
        // Convert ValidationError to simple error array for backward compatibility
        return error.errors.map(err => err.message);
      }
      throw error;
    }
  }
}

module.exports = new DynamicCrudService();
