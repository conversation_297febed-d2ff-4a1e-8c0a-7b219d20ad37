// Test script để kiểm tra menu update
const axios = require('axios');

async function testMenuUpdate() {
  try {
    console.log('🧪 Testing menu update...\n');

    // Test data giống như từ form
    const testData = {
      title: 'Test Updated Menu',
      url: '/test-updated',
      icon: 'star',
      parent_id: '',
      order_index: '999',
      is_active: 'true',  // String như từ form
      is_title: 'false',  // String như từ form
      is_divider: '',     // Empty string
      target: '',
      badge_text: 'TEST',
      badge_color: 'warning',
      table_id: ''
    };

    console.log('1. Test data to send:', testData);

    // Gửi PUT request
    const response = await axios.put('http://localhost:3000/admin/menus/12', testData, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      }
    });

    console.log('2. Response:', response.data);
    console.log('✅ Menu update successful!');

  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
    if (error.response?.data) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    }
  }
}

// Chạy test
if (require.main === module) {
  testMenuUpdate().then(() => {
    console.log('\n🎉 Test completed!');
    process.exit(0);
  }).catch(error => {
    console.error('💥 Test crashed:', error);
    process.exit(1);
  });
}

module.exports = { testMenuUpdate };
