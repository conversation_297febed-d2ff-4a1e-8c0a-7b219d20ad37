{"name": "backend-coreui", "version": "0.0.0", "private": true, "scripts": {"start": "node ./bin/www", "db:setup": "node scripts/complete-database-manager.js setup", "db:verify": "node scripts/complete-database-manager.js verify", "db:backup": "node scripts/complete-database-manager.js backup", "db:reset": "node scripts/complete-database-manager.js reset", "db:status": "node scripts/complete-database-manager.js status", "db:help": "node scripts/complete-database-manager.js help", "db:test": "node scripts/test-setup.js", "test:device": "node scripts/test-device-functionality.js", "test:quick": "node scripts/quick-test.js"}, "dependencies": {"axios": "^1.10.0", "bcrypt": "^6.0.0", "cookie-parser": "~1.4.4", "debug": "~2.6.9", "dotenv": "^16.4.5", "ejs": "^3.1.10", "express": "~4.16.1", "express-ejs-layouts": "^2.5.1", "http-errors": "~1.6.3", "jsonwebtoken": "^9.0.2", "md5": "^2.3.0", "moment": "^2.30.1", "morgan": "~1.9.1", "mysql2": "^3.14.0", "node-cache": "^5.1.2", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "serve-favicon": "^2.5.0"}}