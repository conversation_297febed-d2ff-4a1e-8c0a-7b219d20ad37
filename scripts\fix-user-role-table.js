const db = require('../config/database');

async function fixUserRoleTable() {
  try {
    console.log('🔧 Fixing user_role table...');

    // Initialize database connection
    db.connect('development');
    
    // 1. Create user_role table
    console.log('1. Creating user_role table...');
    await db.query(`
      CREATE TABLE IF NOT EXISTS user_role (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        role_id INT NOT NULL,
        assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        assigned_by INT NULL,
        
        UNIQUE KEY unique_user_role (user_id, role_id),
        FOREIGN KEY (user_id) REFERENCES user(id) ON DELETE CASCADE,
        FOREIGN KEY (role_id) REFERENCES role(id) ON DELETE CASCADE,
        FOREIGN KEY (assigned_by) REFERENCES user(id) ON DELETE SET NULL,
        
        INDEX idx_user_id (user_id),
        INDEX idx_role_id (role_id)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);
    
    console.log('✅ user_role table created successfully');

    // 2. Check existing users and roles
    console.log('2. Checking existing users and roles...');
    const [users, roles] = await Promise.all([
      db.query('SELECT id, email FROM user LIMIT 5'),
      db.query('SELECT id, name FROM role')
    ]);

    console.log(`✅ Found ${users.length} users and ${roles.length} roles`);

    // 3. Assign default roles to users
    console.log('3. Assigning default roles to users...');
    
    // Find Admin role (assume id = 1 or name = 'Admin')
    const adminRole = roles.find(r => r.name === 'Admin' || r.id === 1);
    
    if (adminRole && users.length > 0) {
      // Assign Admin role to first user
      const firstUser = users[0];
      await db.query(`
        INSERT IGNORE INTO user_role (user_id, role_id, assigned_by)
        VALUES (?, ?, ?)
      `, [firstUser.id, adminRole.id, firstUser.id]);
      
      console.log(`✅ Assigned Admin role to user: ${firstUser.email}`);

      // Assign other roles to remaining users (if any)
      if (users.length > 1 && roles.length > 1) {
        const otherRole = roles.find(r => r.id !== adminRole.id);
        if (otherRole) {
          for (let i = 1; i < users.length; i++) {
            const user = users[i];
            await db.query(`
              INSERT IGNORE INTO user_role (user_id, role_id, assigned_by)
              VALUES (?, ?, ?)
            `, [user.id, otherRole.id, firstUser.id]);
          }
          console.log(`✅ Assigned ${otherRole.name} role to ${users.length - 1} other users`);
        }
      }
    }

    // 4. Create sample roles if none exist
    if (roles.length === 0) {
      console.log('4. Creating sample roles...');
      
      const sampleRoles = [
        { name: 'Admin' },
        { name: 'Editor' },
        { name: 'User' }
      ];

      for (const roleData of sampleRoles) {
        await db.query(`
          INSERT IGNORE INTO role (name)
          VALUES (?)
        `, [roleData.name]);
      }

      console.log(`✅ Created ${sampleRoles.length} sample roles`);

      // Re-assign roles to users
      if (users.length > 0) {
        const newRoles = await db.query('SELECT id, name FROM role');
        const adminRole = newRoles.find(r => r.name === 'Admin');
        
        if (adminRole) {
          await db.query(`
            INSERT IGNORE INTO user_role (user_id, role_id)
            VALUES (?, ?)
          `, [users[0].id, adminRole.id]);
          console.log(`✅ Assigned Admin role to first user`);
        }
      }
    }

    // 5. Verify the setup
    console.log('5. Verifying setup...');
    const userRoleCount = await db.queryOne('SELECT COUNT(*) as count FROM user_role');
    console.log(`✅ Total user-role assignments: ${userRoleCount.count}`);

    // Show user-role assignments
    const assignments = await db.query(`
      SELECT 
        u.email,
        r.name as role_name,
        ur.assigned_at
      FROM user_role ur
      INNER JOIN user u ON ur.user_id = u.id
      INNER JOIN role r ON ur.role_id = r.id
      ORDER BY ur.assigned_at DESC
    `);

    console.log('\n📋 Current user-role assignments:');
    assignments.forEach(assignment => {
      console.log(`   - ${assignment.email} → ${assignment.role_name} (${new Date(assignment.assigned_at).toLocaleString()})`);
    });

    console.log('\n🎉 User-role table setup completed successfully!');

  } catch (error) {
    console.error('❌ Error fixing user_role table:', error);
  }
  
  process.exit(0);
}

fixUserRoleTable(); 