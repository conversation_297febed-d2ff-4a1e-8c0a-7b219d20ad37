// Script để reset hoàn toàn hệ thống menu
const mysql = require('mysql2/promise');

async function resetMenuSystem() {
  let connection;
  
  try {
    console.log('🔄 Resetting menu system completely...\n');

    // Tạo connection đến database
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_NAME || 'coreui'
    });

    console.log('✅ Connected to database');

    // 1. Drop bảng admin_menus hoàn toàn
    console.log('\n1. Dropping admin_menus table...');
    
    try {
      await connection.execute('DROP TABLE IF EXISTS admin_menus');
      console.log('✅ Dropped admin_menus table');
    } catch (error) {
      console.log('⚠️  Table might not exist:', error.message);
    }

    // 2. Tạo lại bảng với cấu trúc đúng
    console.log('\n2. Creating admin_menus table with correct structure...');
    
    const createTableSQL = `
      CREATE TABLE admin_menus (
        id INT AUTO_INCREMENT PRIMARY KEY,
        title VARCHAR(255) NOT NULL,
        url VARCHAR(500) NULL,
        icon VARCHAR(100) NULL,
        parent_id INT NULL,
        order_index INT NOT NULL DEFAULT 0,
        is_active BOOLEAN NOT NULL DEFAULT TRUE,
        is_divider BOOLEAN NOT NULL DEFAULT FALSE,
        is_title BOOLEAN NOT NULL DEFAULT FALSE,
        target VARCHAR(50) NULL,
        badge_text VARCHAR(50) NULL,
        badge_color VARCHAR(50) NULL,
        table_id INT NULL,
        created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

        INDEX idx_parent_id (parent_id),
        INDEX idx_table_id (table_id),
        INDEX idx_order (order_index),
        INDEX idx_active (is_active)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    `;

    await connection.execute(createTableSQL);
    console.log('✅ Created admin_menus table with proper structure');

    // Add foreign key constraints after table creation
    console.log('   Adding foreign key constraints...');

    try {
      // Self-referencing foreign key
      await connection.execute(`
        ALTER TABLE admin_menus
        ADD CONSTRAINT fk_admin_menus_parent
        FOREIGN KEY (parent_id) REFERENCES admin_menus(id)
        ON DELETE CASCADE
      `);
      console.log('   ✅ Added parent_id foreign key');
    } catch (error) {
      console.log('   ⚠️  Could not add parent_id foreign key:', error.message);
    }

    try {
      // Foreign key to admin_tables
      await connection.execute(`
        ALTER TABLE admin_menus
        ADD CONSTRAINT fk_admin_menus_table
        FOREIGN KEY (table_id) REFERENCES admin_tables(id)
        ON DELETE CASCADE
      `);
      console.log('   ✅ Added table_id foreign key');
    } catch (error) {
      console.log('   ⚠️  Could not add table_id foreign key:', error.message);
    }

    // 3. Insert menu mặc định
    console.log('\n3. Inserting default menus...');
    
    const defaultMenus = [
      // Root menus
      {
        title: 'Dashboard',
        url: '/',
        icon: 'speedometer',
        parent_id: null,
        order_index: 1,
        badge_text: 'NEW',
        badge_color: 'info'
      },
      
      // Database Admin section
      {
        title: 'Database Admin',
        url: null,
        icon: null,
        parent_id: null,
        order_index: 10,
        is_title: true
      },
      {
        title: 'Admin Dashboard',
        url: '/admin',
        icon: 'storage',
        parent_id: null,
        order_index: 11
      },
      {
        title: 'Manage Tables',
        url: '/admin/tables',
        icon: 'grid',
        parent_id: null,
        order_index: 12
      },
      {
        title: 'Menu Management',
        url: '/admin/menus',
        icon: 'menu',
        parent_id: null,
        order_index: 13
      },
      
      // Data Management section
      {
        title: 'Data Management',
        url: null,
        icon: 'storage',
        parent_id: null,
        order_index: 100,
        is_title: true
      }
    ];

    for (const menuData of defaultMenus) {
      const insertSQL = `
        INSERT INTO admin_menus (
          title, url, icon, parent_id, order_index, 
          is_active, is_title, is_divider, badge_text, badge_color
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `;
      
      await connection.execute(insertSQL, [
        menuData.title,
        menuData.url || null,
        menuData.icon || null,
        menuData.parent_id || null,
        menuData.order_index,
        menuData.is_active !== false,
        menuData.is_title || false,
        menuData.is_divider || false,
        menuData.badge_text || null,
        menuData.badge_color || null
      ]);
      
      console.log(`   ✅ Created menu: ${menuData.title}`);
    }

    // 4. Verify data
    console.log('\n4. Verifying created menus...');
    
    const [menus] = await connection.execute(`
      SELECT id, title, url, icon, parent_id, order_index, 
             is_active, is_title, created_at, updated_at 
      FROM admin_menus 
      ORDER BY order_index
    `);
    
    console.log(`📊 Created ${menus.length} menus:`);
    menus.forEach(menu => {
      console.log(`   - ID ${menu.id}: ${menu.title} | Order: ${menu.order_index} | Active: ${menu.is_active}`);
    });

    // 5. Test Prisma connection
    console.log('\n5. Testing Prisma connection...');
    
    try {
      // Restart Prisma client để clear cache
      const prisma = require('../prisma/client');
      await prisma.$disconnect();
      
      // Tạo connection mới
      const newPrisma = require('../prisma/client');
      const testMenus = await newPrisma.adminMenu.findMany({
        take: 3,
        select: {
          id: true,
          title: true,
          created_at: true,
          updated_at: true
        }
      });
      
      console.log(`✅ Prisma connection working! Sample menus:`);
      testMenus.forEach(menu => {
        console.log(`   - ${menu.title}: Created ${menu.created_at}, Updated ${menu.updated_at}`);
      });
      
      await newPrisma.$disconnect();
    } catch (error) {
      console.log('❌ Prisma connection issue:', error.message);
    }

    console.log('\n🎉 Menu system reset completed successfully!');
    console.log('\n📋 Next steps:');
    console.log('   1. Restart your server');
    console.log('   2. Visit: http://localhost:3000/admin/menus');
    console.log('   3. Use "Sync Table Menus" to add your admin tables');

  } catch (error) {
    console.error('❌ Reset failed:', error.message);
    console.error('Stack trace:', error.stack);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
      console.log('\n🔌 Database connection closed');
    }
  }
}

// Chạy reset nếu file được gọi trực tiếp
if (require.main === module) {
  resetMenuSystem().then(() => {
    console.log('\n✨ Reset completed!');
    process.exit(0);
  }).catch(error => {
    console.error('💥 Reset crashed:', error);
    process.exit(1);
  });
}

module.exports = { resetMenuSystem };
