const db = require('../config/database');

async function setupDefaultMenus() {
  try {
    console.log('🎯 Setting up default admin menus...\n');
    
    db.connect('development');
    
    // 1. Clear existing menus
    console.log('1. Clearing existing menus...');
    await db.query('DELETE FROM admin_menus');
    console.log('✅ Existing menus cleared');
    
    // 2. Insert default menu structure
    console.log('\n2. Creating default menu structure...');
    
    const defaultMenus = [
      // Main navigation
      {
        title: 'Dashboard',
        url: '/admin',
        icon: 'fas fa-tachometer-alt',
        parent_id: null,
        order_index: 1,
        is_active: 1,
        is_title: 0,
        table_id: null
      },
      {
        title: 'User Management',
        url: null,
        icon: 'fas fa-users-cog',
        parent_id: null,
        order_index: 2,
        is_active: 1,
        is_title: 1,
        table_id: null
      },
      {
        title: 'System Management',
        url: null,
        icon: 'fas fa-cogs',
        parent_id: null,
        order_index: 3,
        is_active: 1,
        is_title: 1,
        table_id: null
      }
    ];
    
    // Insert main menus first
    for (const menu of defaultMenus) {
      const result = await db.query(`
        INSERT INTO admin_menus (title, url, icon, parent_id, order_index, is_active, is_title, table_id)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        menu.title, menu.url, menu.icon, menu.parent_id, 
        menu.order_index, menu.is_active, menu.is_title, menu.table_id
      ]);
      console.log(`✅ Created menu: ${menu.title} (ID: ${result.insertId})`);
    }
    
    // 3. Get parent menu IDs
    const userMgmtMenu = await db.queryOne("SELECT id FROM admin_menus WHERE title = 'User Management'");
    const systemMgmtMenu = await db.queryOne("SELECT id FROM admin_menus WHERE title = 'System Management'");
    
    // 4. Get table IDs
    const userTable = await db.queryOne("SELECT id FROM admintable WHERE name = 'user'");
    const roleTable = await db.queryOne("SELECT id FROM admintable WHERE name = 'role'");
    const userRoleTable = await db.queryOne("SELECT id FROM admintable WHERE name = 'user_role'");
    const permissionsTable = await db.queryOne("SELECT id FROM admintable WHERE name = 'permissions'");
    const admintableTable = await db.queryOne("SELECT id FROM admintable WHERE name = 'admintable'");
    const adminMenusTable = await db.queryOne("SELECT id FROM admintable WHERE name = 'admin_menus'");
    
    // 5. Insert submenu items
    console.log('\n3. Creating submenu items...');
    
    const subMenus = [
      // User Management submenu
      {
        title: 'Users',
        url: '/admin/tables/user/data',
        icon: 'fas fa-users',
        parent_id: userMgmtMenu.id,
        order_index: 1,
        table_id: userTable?.id
      },
      {
        title: 'Roles',
        url: '/admin/tables/role/data',
        icon: 'fas fa-user-tag',
        parent_id: userMgmtMenu.id,
        order_index: 2,
        table_id: roleTable?.id
      },
      {
        title: 'User Roles',
        url: '/admin/tables/user_role/data',
        icon: 'fas fa-link',
        parent_id: userMgmtMenu.id,
        order_index: 3,
        table_id: userRoleTable?.id
      },
      {
        title: 'Permissions',
        url: '/admin/tables/permissions/data',
        icon: 'fas fa-key',
        parent_id: userMgmtMenu.id,
        order_index: 4,
        table_id: permissionsTable?.id
      },
      
      // System Management submenu
      {
        title: 'Tables',
        url: '/admin/tables',
        icon: 'fas fa-table',
        parent_id: systemMgmtMenu.id,
        order_index: 1,
        table_id: admintableTable?.id
      },
      {
        title: 'Menus',
        url: '/admin/menus',
        icon: 'fas fa-bars',
        parent_id: systemMgmtMenu.id,
        order_index: 2,
        table_id: adminMenusTable?.id
      }
    ];
    
    for (const menu of subMenus) {
      const result = await db.query(`
        INSERT INTO admin_menus (title, url, icon, parent_id, order_index, is_active, is_title, table_id)
        VALUES (?, ?, ?, ?, ?, 1, 0, ?)
      `, [
        menu.title, menu.url, menu.icon, menu.parent_id, 
        menu.order_index, menu.table_id
      ]);
      console.log(`✅ Created submenu: ${menu.title} (ID: ${result.insertId})`);
    }
    
    // 6. Verify menu structure
    console.log('\n4. Verifying menu structure...');
    const allMenus = await db.query(`
      SELECT 
        m.id, m.title, m.url, m.icon, m.parent_id, m.order_index, m.is_title,
        p.title as parent_title,
        t.display_name as table_name
      FROM admin_menus m
      LEFT JOIN admin_menus p ON m.parent_id = p.id
      LEFT JOIN admintable t ON m.table_id = t.id
      ORDER BY m.parent_id IS NULL DESC, m.parent_id, m.order_index
    `);
    
    console.log('📋 Menu structure:');
    allMenus.forEach(menu => {
      const indent = menu.parent_id ? '  └─ ' : '';
      const type = menu.is_title ? ' [TITLE]' : '';
      const table = menu.table_name ? ` (${menu.table_name})` : '';
      console.log(`${indent}${menu.title}${type}${table}`);
    });
    
    console.log(`\n✅ Created ${allMenus.length} menu items successfully!`);
    
    // 7. Check permissions for default users
    console.log('\n5. Checking user permissions...');
    const permissionService = require('../services/permissionService');
    
    const users = [
      { id: 1, email: '<EMAIL>', role: 'Admin' },
      { id: 2, email: '<EMAIL>', role: 'Manager' },
      { id: 3, email: '<EMAIL>', role: 'Editor' }
    ];
    
    for (const user of users) {
      const hasAdminTableAccess = await permissionService.checkUserPermission(user.id, 'read_admintable');
      const hasUserAccess = await permissionService.checkUserPermission(user.id, null, 'user', 'browse');
      console.log(`📋 ${user.email} (${user.role}):`);
      console.log(`   - Admin Tables: ${hasAdminTableAccess ? '✅' : '❌'}`);
      console.log(`   - User Management: ${hasUserAccess ? '✅' : '❌'}`);
    }
    
    console.log('\n🎉 Default menus setup completed successfully!');
    console.log('\n📋 Next steps:');
    console.log('   1. Start server: node ./bin/www');
    console.log('   2. Login as admin: <EMAIL> / password123');
    console.log('   3. Check sidebar menu navigation');
    
  } catch (error) {
    console.error('❌ Setup failed:', error);
  } finally {
    try {
      db.get().end();
      console.log('\n🔌 Database connection closed');
    } catch (e) {
      console.log('⚠️  Database already closed');
    }
  }
}

setupDefaultMenus();
