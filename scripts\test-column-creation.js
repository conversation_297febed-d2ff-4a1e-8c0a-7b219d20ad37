// Test script để kiểm tra việc tạo cột đơn lẻ
const schemaService = require('../services/schemaService');

async function testColumnCreation() {
  try {
    console.log('🧪 Testing Column Creation...\n');

    // Test data cho cột mới
    const testColumn = {
      name: 'test_field',
      display_name: 'Test Field',
      type: 'varchar',
      length: 255,
      is_nullable: true,
      is_primary: false,
      is_auto_increment: false,
      default_value: null
    };

    console.log('1. Testing column data validation...');
    console.log('Column data:', JSON.stringify(testColumn, null, 2));

    // Test getColumnType method
    console.log('\n2. Testing getColumnType method...');
    const columnType = schemaService.getColumnType(testColumn);
    console.log('Generated column type:', columnType);

    // Test với các loại dữ liệu khác nhau
    const testTypes = [
      { type: 'int', expected: 'INT' },
      { type: 'varchar', length: 100, expected: 'VARCHAR(100)' },
      { type: 'varchar', expected: 'VARCHAR(255)' }, // default length
      { type: 'text', expected: 'TEXT' },
      { type: 'datetime', expected: 'DATETIME' },
      { type: 'boolean', expected: 'BOOLEAN' },
      { type: 'decimal', precision: 10, scale: 2, expected: 'DECIMAL(10, 2)' },
      { type: 'unknown', expected: 'VARCHAR(255)' } // default case
    ];

    console.log('\n3. Testing different column types...');
    testTypes.forEach(test => {
      const result = schemaService.getColumnType(test);
      const status = result === test.expected ? '✅' : '❌';
      console.log(`   ${status} ${test.type}: ${result} (expected: ${test.expected})`);
    });

    // Test với dữ liệu không hợp lệ
    console.log('\n4. Testing invalid data handling...');
    
    const invalidTests = [
      { name: '', type: 'varchar', description: 'Empty name' },
      { name: 'test', type: '', description: 'Empty type' },
      { name: null, type: 'varchar', description: 'Null name' },
      { name: 'test', type: null, description: 'Null type' }
    ];

    for (const invalidTest of invalidTests) {
      try {
        console.log(`   Testing: ${invalidTest.description}`);
        const result = schemaService.getColumnType(invalidTest);
        console.log(`   ⚠️  Unexpected success: ${result}`);
      } catch (error) {
        console.log(`   ✅ Correctly caught error: ${error.message}`);
      }
    }

    // Test SQL generation
    console.log('\n5. Testing SQL generation...');
    const sqlTests = [
      {
        column: { name: 'id', type: 'int', is_nullable: false, is_auto_increment: true },
        expected: '`id` INT NOT NULL AUTO_INCREMENT'
      },
      {
        column: { name: 'name', type: 'varchar', length: 255, is_nullable: false },
        expected: '`name` VARCHAR(255) NOT NULL'
      },
      {
        column: { name: 'description', type: 'text', is_nullable: true },
        expected: '`description` TEXT'
      },
      {
        column: { name: 'status', type: 'varchar', length: 50, is_nullable: false, default_value: "'active'" },
        expected: "`status` VARCHAR(50) NOT NULL DEFAULT 'active'"
      }
    ];

    sqlTests.forEach((test, index) => {
      console.log(`   Test ${index + 1}: ${test.column.name}`);
      
      let sql = `\`${test.column.name}\` ${schemaService.getColumnType(test.column)}`;
      
      if (!test.column.is_nullable) {
        sql += ' NOT NULL';
      }
      
      if (test.column.is_auto_increment) {
        sql += ' AUTO_INCREMENT';
      }
      
      if (test.column.default_value) {
        sql += ` DEFAULT ${test.column.default_value}`;
      }
      
      const status = sql === test.expected ? '✅' : '❌';
      console.log(`   ${status} Generated: ${sql}`);
      if (sql !== test.expected) {
        console.log(`       Expected: ${test.expected}`);
      }
    });

    console.log('\n✅ Column creation tests completed!');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Stack trace:', error.stack);
  }
}

// Chạy test
if (require.main === module) {
  testColumnCreation().then(() => {
    console.log('\n🎉 Test finished!');
    process.exit(0);
  }).catch(error => {
    console.error('💥 Test crashed:', error);
    process.exit(1);
  });
}

module.exports = { testColumnCreation };
