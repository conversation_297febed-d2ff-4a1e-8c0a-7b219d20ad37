const commonService = require('../services/commonService');

module.exports = {
    index: function (req, res, next) {
        const breadcrumb = [
            {name: 'Home', url:'/', active: false},
            {name: 'Dashboard', url:'/', active: true},
        ];
        
        // Handle JWT-compatible flash messages from query parameters only
        const messages = {};
        
        // Check for query-based messages (JWT-compatible)
        if (req.query.error) {
            messages.error = [decodeURIComponent(req.query.error)];
        }
        if (req.query.success) {
            messages.success = [decodeURIComponent(req.query.success)];
        }
        if (req.query.info) {
            messages.info = [decodeURIComponent(req.query.info)];
        }
        if (req.query.warning) {
            messages.warning = [decodeURIComponent(req.query.warning)];
        }
        
        res.render('index', { 
            title: 'Express', 
            breadcrumb: breadcrumb,
            messages: messages
        });
    },
   
}