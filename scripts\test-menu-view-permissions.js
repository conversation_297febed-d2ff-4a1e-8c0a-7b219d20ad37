const db = require('../config/database');
const permissionService = require('../services/permissionService');

async function testMenuViewPermissions() {
  try {
    await db.connect();
    console.log('🧪 Testing Menu View Permissions (Similar to controller logic)\n');

    const userId = 1; // Admin user
    
    // Mô phỏng logic trong controller/menuController.js index()
    const userPermissions = {
      canBrowse: await permissionService.checkUserPermission(userId, null, 'admin_menus', 'browse'),
      canRead: await permissionService.checkUserPermission(userId, null, 'admin_menus', 'read'),
      canCreate: await permissionService.checkUserPermission(userId, null, 'admin_menus', 'add'),
      canUpdate: await permissionService.checkUserPermission(userId, null, 'admin_menus', 'edit'),
      canDelete: await permissionService.checkUserPermission(userId, null, 'admin_menus', 'delete')
    };

    console.log('📊 User Permissions (as passed to view):');
    Object.entries(userPermissions).forEach(([key, value]) => {
      console.log(`   ${key}: ${value ? '✅' : '❌'}`);
    });

    // Mô phỏng logic trong controller/menuController.js getMenusData()
    const dataPermissions = {
      canRead: await permissionService.checkUserPermission(userId, null, 'admin_menus', 'read'),
      canUpdate: await permissionService.checkUserPermission(userId, null, 'admin_menus', 'edit'),
      canDelete: await permissionService.checkUserPermission(userId, null, 'admin_menus', 'delete')
    };

    console.log('\n📊 Data API Permissions (for action buttons):');
    Object.entries(dataPermissions).forEach(([key, value]) => {
      console.log(`   ${key}: ${value ? '✅' : '❌'}`);
    });

    // Kiểm tra browse access (trang có thể load được không)
    if (!userPermissions.canBrowse) {
      console.log('\n❌ User will be redirected from /admin/menus because canBrowse = false');
    } else {
      console.log('\n✅ User can access /admin/menus page');
    }

    // Kiểm tra tình trạng buttons
    console.log('\n🔘 Button visibility:');
    console.log(`   Add Menu button: ${userPermissions.canCreate ? '✅ Visible' : '❌ Hidden'}`);
    console.log(`   Edit buttons: ${dataPermissions.canUpdate ? '✅ Visible' : '❌ Hidden'}`);
    console.log(`   Delete buttons: ${dataPermissions.canDelete ? '✅ Visible' : '❌ Hidden'}`);
    console.log(`   View buttons: ${dataPermissions.canRead ? '✅ Visible' : '❌ Hidden'}`);

    // Kiểm tra admin status
    const isAdmin = await permissionService.isAdmin(userId);
    console.log(`\n👑 Admin status: ${isAdmin ? '✅ Is Admin' : '❌ Not Admin'}`);

    console.log('\n✅ All permissions are working correctly!');
    console.log('🎯 Admin user should now see all buttons (Add, Edit, Delete, View)');

  } catch (error) {
    console.error('❌ Error testing menu view permissions:', error);
  } finally {
    process.exit();
  }
}

testMenuViewPermissions(); 