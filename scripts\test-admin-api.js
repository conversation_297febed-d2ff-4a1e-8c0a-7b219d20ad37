const axios = require('axios');
const jwtService = require('../services/jwtService');

async function testAdminAPI() {
  try {
    console.log('🧪 Testing Admin API Access...\n');
    
    // 1. Create JWT token for admin user
    console.log('1. Creating JWT token for admin user...');
    const adminUser = { 
      id: 1, 
      email: '<EMAIL>', 
      fullname: 'System Administrator',
      role: 'Admin'
    };
    
    const { token } = jwtService.createToken(adminUser);
    console.log('✅ JWT token created successfully');
    console.log(`📋 Token: ${token.substring(0, 50)}...`);
    
    // 2. Test server is running
    console.log('\n2. Testing if server is running...');
    try {
      const response = await axios.get('http://localhost:3000/', {
        timeout: 5000
      });
      console.log('✅ Server is running');
    } catch (error) {
      if (error.code === 'ECONNREFUSED') {
        console.log('❌ Server is not running. Please start with: node ./bin/www');
        return;
      } else {
        console.log('⚠️  Server response:', error.response?.status || error.message);
      }
    }
    
    // 3. Test admin/tables API with JWT token
    console.log('\n3. Testing /admin/tables API...');
    try {
      const response = await axios.get('http://localhost:3000/admin/tables', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        timeout: 10000
      });
      
      console.log('✅ Admin tables API accessible');
      console.log(`📋 Response status: ${response.status}`);
      console.log(`📋 Response type: ${response.headers['content-type']}`);
      
      if (response.data) {
        console.log('📋 Response contains data');
      }
      
    } catch (error) {
      if (error.response) {
        console.log(`❌ API Error: ${error.response.status} - ${error.response.statusText}`);
        console.log(`📋 Error data:`, error.response.data);
      } else {
        console.log(`❌ Request Error: ${error.message}`);
      }
    }
    
    // 4. Test admin/tables/data API
    console.log('\n4. Testing /admin/tables/data API...');
    try {
      const response = await axios.get('http://localhost:3000/admin/tables/data', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        timeout: 10000
      });
      
      console.log('✅ Admin tables data API accessible');
      console.log(`📋 Response status: ${response.status}`);
      
      if (response.data && response.data.data) {
        console.log(`📋 Found ${response.data.data.length} tables`);
        response.data.data.forEach(table => {
          console.log(`   - ${table.name}: ${table.display_name}`);
        });
      }
      
    } catch (error) {
      if (error.response) {
        console.log(`❌ API Error: ${error.response.status} - ${error.response.statusText}`);
        console.log(`📋 Error data:`, error.response.data);
      } else {
        console.log(`❌ Request Error: ${error.message}`);
      }
    }
    
    console.log('\n🎉 Admin API test completed!');
    console.log('\n📋 Next steps:');
    console.log('   1. Make sure server is running: node ./bin/www');
    console.log('   2. Open browser: http://localhost:3000/admin');
    console.log('   3. Login with: <EMAIL> / password123');
    console.log('   4. Navigate to Tables section');
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

testAdminAPI();
