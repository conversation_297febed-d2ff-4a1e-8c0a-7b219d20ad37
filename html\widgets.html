<!DOCTYPE html>


<html lang="en">
  <head>
    <base href="./">
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, shrink-to-fit=no">
    <meta name="description" content="CoreUI - Open Source Bootstrap Admin Template">
    <meta name="author" content="<PERSON><PERSON><PERSON>ek">
    <meta name="keyword" content="Bootstrap,Admin,Template,Open,Source,jQuery,CSS,HTML,RWD,Dashboard">
    <title>CoreUI Free Bootstrap Admin Template</title>
    <link rel="apple-touch-icon" sizes="57x57" href="/public/assets/favicon/apple-icon-57x57.png">
    <link rel="apple-touch-icon" sizes="60x60" href="/public/assets/favicon/apple-icon-60x60.png">
    <link rel="apple-touch-icon" sizes="72x72" href="/public/assets/favicon/apple-icon-72x72.png">
    <link rel="apple-touch-icon" sizes="76x76" href="/public/assets/favicon/apple-icon-76x76.png">
    <link rel="apple-touch-icon" sizes="114x114" href="/public/assets/favicon/apple-icon-114x114.png">
    <link rel="apple-touch-icon" sizes="120x120" href="/public/assets/favicon/apple-icon-120x120.png">
    <link rel="apple-touch-icon" sizes="144x144" href="/public/assets/favicon/apple-icon-144x144.png">
    <link rel="apple-touch-icon" sizes="152x152" href="/public/assets/favicon/apple-icon-152x152.png">
    <link rel="apple-touch-icon" sizes="180x180" href="/public/assets/favicon/apple-icon-180x180.png">
    <link rel="icon" type="image/png" sizes="192x192" href="/public/assets/favicon/android-icon-192x192.png">
    <link rel="icon" type="image/png" sizes="32x32" href="/public/assets/favicon/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="96x96" href="/public/assets/favicon/favicon-96x96.png">
    <link rel="icon" type="image/png" sizes="16x16" href="/public/assets/favicon/favicon-16x16.png">
    <link rel="manifest" href="/public/assets/favicon/manifest.json">
    <meta name="msapplication-TileColor" content="#ffffff">
    <meta name="msapplication-TileImage" content="/public/assets/favicon/ms-icon-144x144.png">
    <meta name="theme-color" content="#ffffff">
    <!-- Vendors styles-->
    <link rel="stylesheet" href="/public/css/vendors/simplebar/simplebar.css">
    
    <!-- Main styles for this application-->
    <link href="/public/css/style.css" rel="stylesheet">
    
    <script src="/public/js/config.js"></script>
    <script src="/public/js/color-modes.js"></script>
    <link href="/public/css/vendors/coreui-chartjs.css" rel="stylesheet">
  </head>
  <body>
    <div class="sidebar sidebar-dark sidebar-fixed border-end" id="sidebar">
      <div class="sidebar-header border-bottom">
        <div class="sidebar-brand">
          <svg class="sidebar-brand-full" width="88" height="32" alt="CoreUI Logo">
            <use xlink:href="/public/assets/brand/coreui.svg#full"></use>
          </svg>
          <svg class="sidebar-brand-narrow" width="32" height="32" alt="CoreUI Logo">
            <use xlink:href="/public/assets/brand/coreui.svg#signet"></use>
          </svg>
        </div>
        <button class="btn-close d-lg-none" type="button" data-theme="dark" aria-label="Close" onclick="coreui.Sidebar.getInstance(document.querySelector('#sidebar')).toggle()"></button>
      </div>
      <ul class="sidebar-nav" data="navigation" data-simplebar>
        <li class="nav-item"><a class="nav-link" href="index.html">
            <svg class="nav-icon">
              <use xlink:href="/public/icons/sprites/free.svg#cil-speedometer"></use>
            </svg> Dashboard<span class="badge badge-sm bg-info ms-auto">NEW</span></a></li>
        <li class="nav-title">Theme</li>
        <li class="nav-item"><a class="nav-link" href="colors.html">
            <svg class="nav-icon">
              <use xlink:href="/public/icons/sprites/free.svg#cil-drop"></use>
            </svg> Colors</a></li>
        <li class="nav-item"><a class="nav-link" href="typography.html">
            <svg class="nav-icon">
              <use xlink:href="/public/icons/sprites/free.svg#cil-pencil"></use>
            </svg> Typography</a></li>
        <li class="nav-title">Components</li>
        <li class="nav-group"><a class="nav-link nav-group-toggle" href="#">
            <svg class="nav-icon">
              <use xlink:href="/public/icons/sprites/free.svg#cil-puzzle"></use>
            </svg> Base</a>
          <ul class="nav-group-items compact">
            <li class="nav-item"><a class="nav-link" href="base/accordion.html"><span class="nav-icon"><span class="nav-icon-bullet"></span></span> Accordion</a></li>
            <li class="nav-item"><a class="nav-link" href="base/breadcrumb.html"><span class="nav-icon"><span class="nav-icon-bullet"></span></span> Breadcrumb</a></li>
            <li class="nav-item"><a class="nav-link" href="https://coreui.io/bootstrap/docs/components/calendar/" target="_blank"><span class="nav-icon"><span class="nav-icon-bullet"></span></span> Calendar
                <svg class="icon icon-sm ms-2">
                  <use xlink:href="/public/icons/sprites/free.svg#cil-external-link"></use>
                </svg><span class="badge badge-sm bg-danger ms-auto">PRO</span></a></li>
            <li class="nav-item"><a class="nav-link" href="base/cards.html"><span class="nav-icon"><span class="nav-icon-bullet"></span></span> Cards</a></li>
            <li class="nav-item"><a class="nav-link" href="base/carousel.html"><span class="nav-icon"><span class="nav-icon-bullet"></span></span> Carousel</a></li>
            <li class="nav-item"><a class="nav-link" href="base/collapse.html"><span class="nav-icon"><span class="nav-icon-bullet"></span></span> Collapse</a></li>
            <li class="nav-item"><a class="nav-link" href="base/list-group.html"><span class="nav-icon"><span class="nav-icon-bullet"></span></span> List group</a></li>
            <li class="nav-item"><a class="nav-link" href="base/navs-tabs.html"><span class="nav-icon"><span class="nav-icon-bullet"></span></span> Navs &amp; Tabs</a></li>
            <li class="nav-item"><a class="nav-link" href="base/pagination.html"><span class="nav-icon"><span class="nav-icon-bullet"></span></span> Pagination</a></li>
            <li class="nav-item"><a class="nav-link" href="base/placeholders.html"><span class="nav-icon"><span class="nav-icon-bullet"></span></span> Placeholders</a></li>
            <li class="nav-item"><a class="nav-link" href="base/popovers.html"><span class="nav-icon"><span class="nav-icon-bullet"></span></span> Popovers</a></li>
            <li class="nav-item"><a class="nav-link" href="base/progress.html"><span class="nav-icon"><span class="nav-icon-bullet"></span></span> Progress</a></li>
            <li class="nav-item"><a class="nav-link" href="base/spinners.html"><span class="nav-icon"><span class="nav-icon-bullet"></span></span> Spinners</a></li>
            <li class="nav-item"><a class="nav-link" href="base/tables.html"><span class="nav-icon"><span class="nav-icon-bullet"></span></span> Tables</a></li>
            <li class="nav-item"><a class="nav-link" href="base/tooltips.html"><span class="nav-icon"><span class="nav-icon-bullet"></span></span> Tooltips</a></li>
          </ul>
        </li>
        <li class="nav-group"><a class="nav-link nav-group-toggle" href="#">
            <svg class="nav-icon">
              <use xlink:href="/public/icons/sprites/free.svg#cil-cursor"></use>
            </svg> Buttons</a>
          <ul class="nav-group-items compact">
            <li class="nav-item"><a class="nav-link" href="buttons/buttons.html"><span class="nav-icon"><span class="nav-icon-bullet"></span></span> Buttons</a></li>
            <li class="nav-item"><a class="nav-link" href="buttons/button-group.html"><span class="nav-icon"><span class="nav-icon-bullet"></span></span> Buttons Group</a></li>
            <li class="nav-item"><a class="nav-link" href="buttons/dropdowns.html"><span class="nav-icon"><span class="nav-icon-bullet"></span></span> Dropdowns</a></li>
            <li class="nav-item"><a class="nav-link" href="https://coreui.io/bootstrap/docs/components/loading-buttons/" target="_blank"><span class="nav-icon"><span class="nav-icon-bullet"></span></span> Loading Buttons
                <svg class="icon icon-sm ms-2">
                  <use xlink:href="/public/icons/sprites/free.svg#cil-external-link"></use>
                </svg><span class="badge badge-sm bg-danger ms-auto">PRO</span></a></li>
          </ul>
        </li>
        <li class="nav-item"><a class="nav-link" href="charts.html">
            <svg class="nav-icon">
              <use xlink:href="/public/icons/sprites/free.svg#cil-chart-pie"></use>
            </svg> Charts</a></li>
        <li class="nav-group"><a class="nav-link nav-group-toggle" href="#">
            <svg class="nav-icon">
              <use xlink:href="/public/icons/sprites/free.svg#cil-notes"></use>
            </svg> Forms</a>
          <ul class="nav-group-items compact">
            <li class="nav-item"><a class="nav-link" href="forms/form-control.html"><span class="nav-icon"><span class="nav-icon-bullet"></span></span> Form Control</a></li>
            <li class="nav-item"><a class="nav-link" href="forms/select.html"><span class="nav-icon"><span class="nav-icon-bullet"></span></span> Select</a></li>
            <li class="nav-item"><a class="nav-link" href="https://coreui.io/bootstrap/docs/forms/multi-select/" target="_blank"><span class="nav-icon"><span class="nav-icon-bullet"></span></span> Multi Select
                <svg class="icon icon-sm ms-2">
                  <use xlink:href="/public/icons/sprites/free.svg#cil-external-link"></use>
                </svg><span class="badge badge-sm bg-danger ms-auto">PRO</span></a></li>
            <li class="nav-item"><a class="nav-link" href="forms/checks-radios.html"><span class="nav-icon"><span class="nav-icon-bullet"></span></span> Checks and radios</a></li>
            <li class="nav-item"><a class="nav-link" href="forms/range.html"><span class="nav-icon"><span class="nav-icon-bullet"></span></span> Range</a></li>
            <li class="nav-item"><a class="nav-link" href="https://coreui.io/bootstrap/docs/forms/range-slider/" target="_blank"><span class="nav-icon"><span class="nav-icon-bullet"></span></span> Range Slider
                <svg class="icon icon-sm ms-2">
                  <use xlink:href="/public/icons/sprites/free.svg#cil-external-link"></use>
                </svg><span class="badge badge-sm bg-danger ms-auto">PRO</span></a></li>
            <li class="nav-item"><a class="nav-link" href="forms/input-group.html"><span class="nav-icon"><span class="nav-icon-bullet"></span></span> Input group</a></li>
            <li class="nav-item"><a class="nav-link" href="forms/floating-labels.html"><span class="nav-icon"><span class="nav-icon-bullet"></span></span> Floating labels</a></li>
            <li class="nav-item"><a class="nav-link" href="https://coreui.io/bootstrap/docs/forms/date-picker/" target="_blank"><span class="nav-icon"><span class="nav-icon-bullet"></span></span> Date Picker
                <svg class="icon icon-sm ms-2">
                  <use xlink:href="/public/icons/sprites/free.svg#cil-external-link"></use>
                </svg><span class="badge badge-sm bg-danger ms-auto">PRO</span></a></li>
            <li class="nav-item"><a class="nav-link" href="https://coreui.io/bootstrap/docs/forms/date-range-picker/" target="_blank"><span class="nav-icon"><span class="nav-icon-bullet"></span></span> Date Range Picker<span class="badge badge-sm bg-danger ms-auto">PRO</span></a></li>
            <li class="nav-item"><a class="nav-link" href="https://coreui.io/bootstrap/docs/forms/rating/" target="_blank"><span class="nav-icon"><span class="nav-icon-bullet"></span></span> Rating
                <svg class="icon icon-sm ms-2">
                  <use xlink:href="/public/icons/sprites/free.svg#cil-external-link"></use>
                </svg><span class="badge badge-sm bg-danger ms-auto">PRO</span></a></li>
            <li class="nav-item"><a class="nav-link" href="https://coreui.io/bootstrap/docs/forms/time-picker/" target="_blank"><span class="nav-icon"><span class="nav-icon-bullet"></span></span> Time Picker
                <svg class="icon icon-sm ms-2">
                  <use xlink:href="/public/icons/sprites/free.svg#cil-external-link"></use>
                </svg><span class="badge badge-sm bg-danger ms-auto">PRO</span></a></li>
            <li class="nav-item"><a class="nav-link" href="forms/layout.html"><span class="nav-icon"><span class="nav-icon-bullet"></span></span> Layout</a></li>
            <li class="nav-item"><a class="nav-link" href="forms/validation.html"><span class="nav-icon"><span class="nav-icon-bullet"></span></span> Validation</a></li>
          </ul>
        </li>
        <li class="nav-group"><a class="nav-link nav-group-toggle" href="#">
            <svg class="nav-icon">
              <use xlink:href="/public/icons/sprites/free.svg#cil-star"></use>
            </svg> Icons</a>
          <ul class="nav-group-items compact">
            <li class="nav-item"><a class="nav-link" href="/public/icons/coreui-icons-free.html"><span class="nav-icon"><span class="nav-icon-bullet"></span></span> CoreUI Icons<span class="badge badge-sm bg-success ms-auto">Free</span></a></li>
            <li class="nav-item"><a class="nav-link" href="/public/icons/coreui-icons-brand.html"><span class="nav-icon"><span class="nav-icon-bullet"></span></span> CoreUI Icons - Brand</a></li>
            <li class="nav-item"><a class="nav-link" href="/public/icons/coreui-icons-flag.html"><span class="nav-icon"><span class="nav-icon-bullet"></span></span> CoreUI Icons - Flag</a></li>
          </ul>
        </li>
        <li class="nav-group"><a class="nav-link nav-group-toggle" href="#">
            <svg class="nav-icon">
              <use xlink:href="/public/icons/sprites/free.svg#cil-bell"></use>
            </svg> Notifications</a>
          <ul class="nav-group-items compact">
            <li class="nav-item"><a class="nav-link" href="notifications/alerts.html"><span class="nav-icon"><span class="nav-icon-bullet"></span></span> Alerts</a></li>
            <li class="nav-item"><a class="nav-link" href="notifications/badge.html"><span class="nav-icon"><span class="nav-icon-bullet"></span></span> Badge</a></li>
            <li class="nav-item"><a class="nav-link" href="notifications/modals.html"><span class="nav-icon"><span class="nav-icon-bullet"></span></span> Modals</a></li>
            <li class="nav-item"><a class="nav-link" href="notifications/toasts.html"><span class="nav-icon"><span class="nav-icon-bullet"></span></span> Toasts</a></li>
          </ul>
        </li>
        <li class="nav-item"><a class="nav-link" href="widgets.html">
            <svg class="nav-icon">
              <use xlink:href="/public/icons/sprites/free.svg#cil-calculator"></use>
            </svg> Widgets<span class="badge badge-sm bg-info ms-auto">NEW</span></a></li>
        <li class="nav-divider"></li>
        <li class="nav-title">Extras</li>
        <li class="nav-group"><a class="nav-link nav-group-toggle" href="#">
            <svg class="nav-icon">
              <use xlink:href="/public/icons/sprites/free.svg#cil-star"></use>
            </svg> Pages</a>
          <ul class="nav-group-items compact">
            <li class="nav-item"><a class="nav-link" href="login.html" target="_top">
                <svg class="nav-icon">
                  <use xlink:href="/public/icons/sprites/free.svg#cil-account-logout"></use>
                </svg> Login</a></li>
            <li class="nav-item"><a class="nav-link" href="register.html" target="_top">
                <svg class="nav-icon">
                  <use xlink:href="/public/icons/sprites/free.svg#cil-account-logout"></use>
                </svg> Register</a></li>
            <li class="nav-item"><a class="nav-link" href="404.html" target="_top">
                <svg class="nav-icon">
                  <use xlink:href="/public/icons/sprites/free.svg#cil-bug"></use>
                </svg> Error 404</a></li>
            <li class="nav-item"><a class="nav-link" href="500.html" target="_top">
                <svg class="nav-icon">
                  <use xlink:href="/public/icons/sprites/free.svg#cil-bug"></use>
                </svg> Error 500</a></li>
          </ul>
        </li>
        <li class="nav-item mt-auto"><a class="nav-link" href="https://coreui.io/docs/templates/installation/" target="_blank">
            <svg class="nav-icon">
              <use xlink:href="/public/icons/sprites/free.svg#cil-description"></use>
            </svg> Docs</a></li>
        <li class="nav-item"><a class="nav-link text-primary fw-semibold" href="https://coreui.io/product/bootstrap-dashboard-template/" target="_top">
            <svg class="nav-icon text-primary">
              <use xlink:href="/public/icons/sprites/free.svg#cil-layers"></use>
            </svg> Try CoreUI PRO</a></li>
      </ul>
      <div class="sidebar-footer border-top d-none d-md-flex">     
        <button class="sidebar-toggler" type="button" data-toggle="unfoldable"></button>
      </div>
    </div>
    <div class="wrapper d-flex flex-column min-vh-100">
      <header class="header header-sticky p-0 mb-4">
        <div class="container-fluid border-bottom px-4">
          <button class="header-toggler" type="button" onclick="coreui.Sidebar.getInstance(document.querySelector('#sidebar')).toggle()" style="margin-inline-start: -14px;">
            <svg class="icon icon-lg">
              <use xlink:href="/public/icons/sprites/free.svg#cil-menu"></use>
            </svg>
          </button>
          <ul class="header-nav d-none d-lg-flex">
            <li class="nav-item"><a class="nav-link" href="#">Dashboard</a></li>
            <li class="nav-item"><a class="nav-link" href="#">Users</a></li>
            <li class="nav-item"><a class="nav-link" href="#">Settings</a></li>
          </ul>
          <ul class="header-nav ms-auto">
            <li class="nav-item"><a class="nav-link" href="#">
                <svg class="icon icon-lg">
                  <use xlink:href="/public/icons/sprites/free.svg#cil-bell"></use>
                </svg></a></li>
            <li class="nav-item"><a class="nav-link" href="#">
                <svg class="icon icon-lg">
                  <use xlink:href="/public/icons/sprites/free.svg#cil-list-rich"></use>
                </svg></a></li>
            <li class="nav-item"><a class="nav-link" href="#">
                <svg class="icon icon-lg">
                  <use xlink:href="/public/icons/sprites/free.svg#cil-envelope-open"></use>
                </svg></a></li>
          </ul>
          <ul class="header-nav">
            <li class="nav-item py-1">
              <div class="vr h-100 mx-2 text-body text-opacity-75"></div>
            </li>
            <li class="nav-item dropdown">
              <button class="btn btn-link nav-link py-2 px-2 d-flex align-items-center" type="button" aria-expanded="false" data-toggle="dropdown">
                <svg class="icon icon-lg theme-icon-active">
                  <use xlink:href="/public/icons/sprites/free.svg#cil-contrast"></use>
                </svg>
              </button>
              <ul class="dropdown-menu dropdown-menu-end" style="--cui-dropdown-min-width: 8rem;">
                <li>
                  <button class="dropdown-item d-flex align-items-center" type="button" data-theme-value="light">
                    <svg class="icon icon-lg me-3">
                      <use xlink:href="/public/icons/sprites/free.svg#cil-sun"></use>
                    </svg>Light
                  </button>
                </li>
                <li>
                  <button class="dropdown-item d-flex align-items-center" type="button" data-theme-value="dark">
                    <svg class="icon icon-lg me-3">
                      <use xlink:href="/public/icons/sprites/free.svg#cil-moon"></use>
                    </svg>Dark
                  </button>
                </li>
                <li>
                  <button class="dropdown-item d-flex align-items-center active" type="button" data-theme-value="auto">
                    <svg class="icon icon-lg me-3">
                      <use xlink:href="/public/icons/sprites/free.svg#cil-contrast"></use>
                    </svg>Auto
                  </button>
                </li>
              </ul>
            </li>
            <li class="nav-item py-1">
              <div class="vr h-100 mx-2 text-body text-opacity-75"></div>
            </li>
            <li class="nav-item dropdown"><a class="nav-link py-0 pe-0" data-toggle="dropdown" href="#" role="button" aria-haspopup="true" aria-expanded="false">
                <div class="avatar avatar-md"><img class="avatar-img" src="/public/assets/img/avatars/8.jpg" alt="<EMAIL>"></div></a>
              <div class="dropdown-menu dropdown-menu-end pt-0">
                <div class="dropdown-header bg-body-tertiary text-body-secondary fw-semibold rounded-top mb-2">Account</div><a class="dropdown-item" href="#">
                  <svg class="icon me-2">
                    <use xlink:href="/public/icons/sprites/free.svg#cil-bell"></use>
                  </svg> Updates<span class="badge badge-sm bg-info ms-2">42</span></a><a class="dropdown-item" href="#">
                  <svg class="icon me-2">
                    <use xlink:href="/public/icons/sprites/free.svg#cil-envelope-open"></use>
                  </svg> Messages<span class="badge badge-sm bg-success ms-2">42</span></a><a class="dropdown-item" href="#">
                  <svg class="icon me-2">
                    <use xlink:href="/public/icons/sprites/free.svg#cil-task"></use>
                  </svg> Tasks<span class="badge badge-sm bg-danger ms-2">42</span></a><a class="dropdown-item" href="#">
                  <svg class="icon me-2">
                    <use xlink:href="/public/icons/sprites/free.svg#cil-comment-square"></use>
                  </svg> Comments<span class="badge badge-sm bg-warning ms-2">42</span></a>
                <div class="dropdown-header bg-body-tertiary text-body-secondary fw-semibold my-2">
                  <div class="fw-semibold">Settings</div>
                </div><a class="dropdown-item" href="#">
                  <svg class="icon me-2">
                    <use xlink:href="/public/icons/sprites/free.svg#cil-user"></use>
                  </svg> Profile</a><a class="dropdown-item" href="#">
                  <svg class="icon me-2">
                    <use xlink:href="/public/icons/sprites/free.svg#cil-settings"></use>
                  </svg> Settings</a><a class="dropdown-item" href="#">
                  <svg class="icon me-2">
                    <use xlink:href="/public/icons/sprites/free.svg#cil-credit-card"></use>
                  </svg> Payments<span class="badge badge-sm bg-secondary ms-2">42</span></a><a class="dropdown-item" href="#">
                  <svg class="icon me-2">
                    <use xlink:href="/public/icons/sprites/free.svg#cil-file"></use>
                  </svg> Projects<span class="badge badge-sm bg-primary ms-2">42</span></a>
                <div class="dropdown-divider"></div><a class="dropdown-item" href="#">
                  <svg class="icon me-2">
                    <use xlink:href="/public/icons/sprites/free.svg#cil-lock-locked"></use>
                  </svg> Lock Account</a><a class="dropdown-item" href="#">
                  <svg class="icon me-2">
                    <use xlink:href="/public/icons/sprites/free.svg#cil-account-logout"></use>
                  </svg> Logout</a>
              </div>
            </li>
          </ul>
        </div>
        <div class="container-fluid px-4">
          <nav aria-label="breadcrumb">
            <ol class="breadcrumb my-0">
              <li class="breadcrumb-item"><a href="#">Home</a>
              </li>
              <li class="breadcrumb-item"><span>Components</span>
              </li>
              <li class="breadcrumb-item active"><span>Widgets</span>
              </li>
            </ol>
          </nav>
        </div>
      </header>
      <div class="body flex-grow-1">
        <div class="container-lg px-4">
          <div class="card mb-4">
            <div class="card-header">
              <string>Widgets</string>
            </div>
            <div class="card-body">
              <div class="example">
                <ul class="nav nav-underline-border" role="tablist">
                  <li class="nav-item"><a class="nav-link active" data-toggle="tab" href="#preview-1000" role="tab">
                      <svg class="icon me-2">
                        <use xlink:href="/public/icons/sprites/free.svg#cil-media-play"></use>
                      </svg>Preview</a></li>
                </ul>
                <div class="tab-content rounded-bottom">
                  <div class="tab-pane p-3 active preview" role="tabpanel" id="preview-1000">
                    <div class="row g-4">
                      <div class="col-12 col-sm-6 col-xl-4 col-xxl-3">
                        <div class="card text-white bg-primary">
                          <div class="card-body pb-0 d-flex justify-content-between align-items-start">
                            <div>
                              <div class="fs-4 fw-semibold">26K <span class="fs-6 fw-normal">(-12.4%
                                  <svg class="icon">
                                    <use xlink:href="/public/icons/sprites/free.svg#cil-arrow-bottom"></use>
                                  </svg>)</span></div>
                              <div>Users</div>
                            </div>
                            <div class="dropdown">
                              <button class="btn btn-transparent text-white p-0" type="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                <svg class="icon">
                                  <use xlink:href="/public/icons/sprites/free.svg#cil-options"></use>
                                </svg>
                              </button>
                              <div class="dropdown-menu dropdown-menu-end"><a class="dropdown-item" href="#">Action</a><a class="dropdown-item" href="#">Another action</a><a class="dropdown-item" href="#">Something else here</a></div>
                            </div>
                          </div>
                          <div class="c-chart-wrapper mt-3 mx-3" style="height:70px;">
                            <canvas class="chart" id="card-chart1" height="70"></canvas>
                          </div>
                        </div>
                      </div>
                      <!-- /.col-->
                      <div class="col-12 col-sm-6 col-xl-4 col-xxl-3">
                        <div class="card text-white bg-info">
                          <div class="card-body pb-0 d-flex justify-content-between align-items-start">
                            <div>
                              <div class="fs-4 fw-semibold">$6.200 <span class="fs-6 fw-normal">(40.9%
                                  <svg class="icon">
                                    <use xlink:href="/public/icons/sprites/free.svg#cil-arrow-top"></use>
                                  </svg>)</span></div>
                              <div>Income</div>
                            </div>
                            <div class="dropdown">
                              <button class="btn btn-transparent text-white p-0" type="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                <svg class="icon">
                                  <use xlink:href="/public/icons/sprites/free.svg#cil-options"></use>
                                </svg>
                              </button>
                              <div class="dropdown-menu dropdown-menu-end"><a class="dropdown-item" href="#">Action</a><a class="dropdown-item" href="#">Another action</a><a class="dropdown-item" href="#">Something else here</a></div>
                            </div>
                          </div>
                          <div class="c-chart-wrapper mt-3 mx-3" style="height:70px;">
                            <canvas class="chart" id="card-chart2" height="70"></canvas>
                          </div>
                        </div>
                      </div>
                      <!-- /.col-->
                      <div class="col-12 col-sm-6 col-xl-4 col-xxl-3">
                        <div class="card text-white bg-warning">
                          <div class="card-body pb-0 d-flex justify-content-between align-items-start">
                            <div>
                              <div class="fs-4 fw-semibold">2.49% <span class="fs-6 fw-normal">(84.7%
                                  <svg class="icon">
                                    <use xlink:href="/public/icons/sprites/free.svg#cil-arrow-top"></use>
                                  </svg>)</span></div>
                              <div>Conversion Rate</div>
                            </div>
                            <div class="dropdown">
                              <button class="btn btn-transparent text-white p-0" type="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                <svg class="icon">
                                  <use xlink:href="/public/icons/sprites/free.svg#cil-options"></use>
                                </svg>
                              </button>
                              <div class="dropdown-menu dropdown-menu-end"><a class="dropdown-item" href="#">Action</a><a class="dropdown-item" href="#">Another action</a><a class="dropdown-item" href="#">Something else here</a></div>
                            </div>
                          </div>
                          <div class="c-chart-wrapper mt-3" style="height:70px;">
                            <canvas class="chart" id="card-chart3" height="70"></canvas>
                          </div>
                        </div>
                      </div>
                      <!-- /.col-->
                      <div class="col-12 col-sm-6 col-xl-4 col-xxl-3">
                        <div class="card text-white bg-danger">
                          <div class="card-body pb-0 d-flex justify-content-between align-items-start">
                            <div>
                              <div class="fs-4 fw-semibold">44K <span class="fs-6 fw-normal">(-23.6%
                                  <svg class="icon">
                                    <use xlink:href="/public/icons/sprites/free.svg#cil-arrow-bottom"></use>
                                  </svg>)</span></div>
                              <div>Sessions</div>
                            </div>
                            <div class="dropdown">
                              <button class="btn btn-transparent text-white p-0" type="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                <svg class="icon">
                                  <use xlink:href="/public/icons/sprites/free.svg#cil-options"></use>
                                </svg>
                              </button>
                              <div class="dropdown-menu dropdown-menu-end"><a class="dropdown-item" href="#">Action</a><a class="dropdown-item" href="#">Another action</a><a class="dropdown-item" href="#">Something else here</a></div>
                            </div>
                          </div>
                          <div class="c-chart-wrapper mt-3 mx-3" style="height:70px;">
                            <canvas class="chart" id="card-chart4" height="70"></canvas>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="example">
                <ul class="nav nav-underline-border" role="tablist">
                  <li class="nav-item"><a class="nav-link active" data-toggle="tab" href="#preview-1001" role="tab">
                      <svg class="icon me-2">
                        <use xlink:href="/public/icons/sprites/free.svg#cil-media-play"></use>
                      </svg>Preview</a></li>
                </ul>
                <div class="tab-content rounded-bottom">
                  <div class="tab-pane p-3 active preview" role="tabpanel" id="preview-1001"> 
                    <div class="row g-4">
                      <div class="col-12 col-sm-6 col-xl-4 col-xxl-3">
                        <div class="card">
                          <div class="card-body">
                            <div class="fs-4 fw-semibold">89.9%</div>
                            <div>Widget title</div>
                            <div class="progress progress-thin my-2">
                              <div class="progress-bar bg-success" role="progressbar" style="width: 25%" aria-valuenow="25" aria-valuemin="0" aria-valuemax="100"></div>
                            </div><small class="text-body-secondary">Widget helper text</small>
                          </div>
                        </div>
                      </div>
                      <!-- /.col-->
                      <div class="col-12 col-sm-6 col-xl-4 col-xxl-3">
                        <div class="card">
                          <div class="card-body">
                            <div class="fs-4 fw-semibold">12.124</div>
                            <div>Widget title</div>
                            <div class="progress progress-thin my-2">
                              <div class="progress-bar bg-info" role="progressbar" style="width: 25%" aria-valuenow="25" aria-valuemin="0" aria-valuemax="100"></div>
                            </div><small class="text-body-secondary">Widget helper text</small>
                          </div>
                        </div>
                      </div>
                      <!-- /.col-->
                      <div class="col-12 col-sm-6 col-xl-4 col-xxl-3">
                        <div class="card">
                          <div class="card-body">
                            <div class="fs-4 fw-semibold">$98.111,00</div>
                            <div>Widget title</div>
                            <div class="progress progress-thin my-2">
                              <div class="progress-bar bg-warning" role="progressbar" style="width: 25%" aria-valuenow="25" aria-valuemin="0" aria-valuemax="100"></div>
                            </div><small class="text-body-secondary">Widget helper text</small>
                          </div>
                        </div>
                      </div>
                      <!-- /.col-->
                      <div class="col-12 col-sm-6 col-xl-4 col-xxl-3">
                        <div class="card">
                          <div class="card-body">
                            <div class="fs-4 fw-semibold">2 TB</div>
                            <div>Widget title</div>
                            <div class="progress progress-thin my-2">
                              <div class="progress-bar bg-danger" role="progressbar" style="width: 25%" aria-valuenow="25" aria-valuemin="0" aria-valuemax="100"></div>
                            </div><small class="text-body-secondary">Widget helper text</small>
                          </div>
                        </div>
                      </div>
                      <!-- /.col-->
                    </div>
                    <!-- /.row.g-4-->
                  </div>
                </div>
              </div>
              <div class="example">
                <ul class="nav nav-underline-border" role="tablist">
                  <li class="nav-item"><a class="nav-link active" data-toggle="tab" href="#preview-1002" role="tab">
                      <svg class="icon me-2">
                        <use xlink:href="/public/icons/sprites/free.svg#cil-media-play"></use>
                      </svg>Preview</a></li>
                </ul>
                <div class="tab-content rounded-bottom">
                  <div class="tab-pane p-3 active preview" role="tabpanel" id="preview-1002">
                    <div class="row g-4">
                      <div class="col-12 col-sm-6 col-xl-4 col-xxl-3">
                        <div class="card text-white bg-primary">
                          <div class="card-body">
                            <div class="fs-4 fw-semibold">89.9%</div>
                            <div>Widget title</div>
                            <div class="progress progress-white progress-thin my-2">
                              <div class="progress-bar" role="progressbar" style="width: 25%" aria-valuenow="25" aria-valuemin="0" aria-valuemax="100"></div>
                            </div><small class="text-white text-opacity-75">Widget helper text</small>
                          </div>
                        </div>
                      </div>
                      <!-- /.col-->
                      <div class="col-12 col-sm-6 col-xl-4 col-xxl-3">
                        <div class="card text-white bg-warning">
                          <div class="card-body">
                            <div class="fs-4 fw-semibold">12.124</div>
                            <div>Widget title</div>
                            <div class="progress progress-white progress-thin my-2">
                              <div class="progress-bar" role="progressbar" style="width: 25%" aria-valuenow="25" aria-valuemin="0" aria-valuemax="100"></div>
                            </div><small class="text-white text-opacity-75">Widget helper text</small>
                          </div>
                        </div>
                      </div>
                      <!-- /.col-->
                      <div class="col-12 col-sm-6 col-xl-4 col-xxl-3">
                        <div class="card text-white bg-danger">
                          <div class="card-body">
                            <div class="fs-4 fw-semibold">$98.111,00</div>
                            <div>Widget title</div>
                            <div class="progress progress-white progress-thin my-2">
                              <div class="progress-bar" role="progressbar" style="width: 25%" aria-valuenow="25" aria-valuemin="0" aria-valuemax="100"></div>
                            </div><small class="text-white text-opacity-75">Widget helper text</small>
                          </div>
                        </div>
                      </div>
                      <!-- /.col-->
                      <div class="col-12 col-sm-6 col-xl-4 col-xxl-3">
                        <div class="card text-white bg-info">
                          <div class="card-body">
                            <div class="fs-4 fw-semibold">2 TB</div>
                            <div>Widget title</div>
                            <div class="progress progress-white progress-thin my-2">
                              <div class="progress-bar" role="progressbar" style="width: 25%" aria-valuenow="25" aria-valuemin="0" aria-valuemax="100"></div>
                            </div><small class="text-white text-opacity-75">Widget helper text</small>
                          </div>
                        </div>
                      </div>
                      <!-- /.col-->
                    </div>
                    <!-- /.row.g-4-->
                  </div>
                </div>
              </div>
              <div class="example">
                <ul class="nav nav-underline-border" role="tablist">
                  <li class="nav-item"><a class="nav-link active" data-toggle="tab" href="#preview-1003" role="tab">
                      <svg class="icon me-2">
                        <use xlink:href="/public/icons/sprites/free.svg#cil-media-play"></use>
                      </svg>Preview</a></li>
                </ul>
                <div class="tab-content rounded-bottom">
                  <div class="tab-pane p-3 active preview" role="tabpanel" id="preview-1003">
                    <div class="row g-4">
                      <div class="col-6 col-sm-4 col-xl-2">
                        <div class="card">
                          <div class="card-body text-center">
                            <div class="text-body-secondary small text-uppercase fw-semibold">Title</div>
                            <div class="fs-6 fw-semibold py-3">1,123</div>
                            <div class="c-chart-wrapper mx-auto" style="height:40px;width:80px">
                              <canvas class="chart chart-bar" id="sparkline-chart-1" height="40" width="80"></canvas>
                            </div>
                          </div>
                        </div>
                      </div>
                      <!-- /.col-->
                      <div class="col-6 col-sm-4 col-xl-2">
                        <div class="card">
                          <div class="card-body text-center">
                            <div class="text-body-secondary small text-uppercase fw-semibold">Title</div>
                            <div class="fs-6 fw-semibold py-3">1,123</div>
                            <div class="c-chart-wrapper mx-auto" style="height:40px;width:80px">
                              <canvas class="chart chart-bar" id="sparkline-chart-2" height="40" width="80"></canvas>
                            </div>
                          </div>
                        </div>
                      </div>
                      <!-- /.col-->
                      <div class="col-6 col-sm-4 col-xl-2">
                        <div class="card">
                          <div class="card-body text-center">
                            <div class="text-body-secondary small text-uppercase fw-semibold">Title</div>
                            <div class="fs-6 fw-semibold py-3">1,123</div>
                            <div class="c-chart-wrapper mx-auto" style="height:40px;width:80px">
                              <canvas class="chart chart-bar" id="sparkline-chart-3" height="40" width="80"></canvas>
                            </div>
                          </div>
                        </div>
                      </div>
                      <!-- /.col-->
                      <div class="col-6 col-sm-4 col-xl-2">
                        <div class="card">
                          <div class="card-body text-center">
                            <div class="text-body-secondary small text-uppercase fw-semibold">Title</div>
                            <div class="fs-6 fw-semibold py-3">1,123</div>
                            <div class="c-chart-wrapper mx-auto" style="height:40px;width:80px">
                              <canvas class="chart chart-line" id="sparkline-chart-4" height="40" width="100"></canvas>
                            </div>
                          </div>
                        </div>
                      </div>
                      <!-- /.col-->
                      <div class="col-6 col-sm-4 col-xl-2">
                        <div class="card">
                          <div class="card-body text-center">
                            <div class="text-body-secondary small text-uppercase fw-semibold">Title</div>
                            <div class="fs-6 fw-semibold py-3">1,123</div>
                            <div class="c-chart-wrapper mx-auto" style="height:40px;width:80px">
                              <canvas class="chart chart-line" id="sparkline-chart-5" height="40" width="100"></canvas>
                            </div>
                          </div>
                        </div>
                      </div>
                      <!-- /.col-->
                      <div class="col-6 col-sm-4 col-xl-2">
                        <div class="card">
                          <div class="card-body text-center">
                            <div class="text-body-secondary small text-uppercase fw-semibold">Title</div>
                            <div class="fs-6 fw-semibold py-3">1,123</div>
                            <div class="c-chart-wrapper mx-auto" style="height:40px;width:80px">
                              <canvas class="chart chart-line" id="sparkline-chart-6" height="40" width="100"></canvas>
                            </div>
                          </div>
                        </div>
                      </div>
                      <!-- /.col-->
                    </div>
                    <!-- /.row.g-4-->
                  </div>
                </div>
              </div>
              <div class="example">
                <ul class="nav nav-underline-border" role="tablist">
                  <li class="nav-item"><a class="nav-link active" data-toggle="tab" href="#preview-1004" role="tab">
                      <svg class="icon me-2">
                        <use xlink:href="/public/icons/sprites/free.svg#cil-media-play"></use>
                      </svg>Preview</a></li>
                </ul>
                <div class="tab-content rounded-bottom">
                  <div class="tab-pane p-3 active preview" role="tabpanel" id="preview-1004">
                    <div class="row g-4">
                      <div class="col-12 col-sm-6 col-xl-4 col-xxl-3">
                        <div class="card">
                          <div class="card-body p-3 d-flex align-items-center">
                            <div class="bg-primary text-white p-3 me-3">
                              <svg class="icon icon-xl">
                                <use xlink:href="/public/icons/sprites/free.svg#cil-settings"></use>
                              </svg>
                            </div>
                            <div>
                              <div class="fs-6 fw-semibold text-primary">$1.999,50</div>
                              <div class="text-body-secondary text-uppercase fw-semibold small">Widget title</div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <!-- /.col-->
                      <div class="col-12 col-sm-6 col-xl-4 col-xxl-3">
                        <div class="card">
                          <div class="card-body p-3 d-flex align-items-center">
                            <div class="bg-info text-white p-3 me-3">
                              <svg class="icon icon-xl">
                                <use xlink:href="/public/icons/sprites/free.svg#cil-laptop"></use>
                              </svg>
                            </div>
                            <div>
                              <div class="fs-6 fw-semibold text-info">$1.999,50</div>
                              <div class="text-body-secondary text-uppercase fw-semibold small">Widget title</div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <!-- /.col-->
                      <div class="col-12 col-sm-6 col-xl-4 col-xxl-3">
                        <div class="card">
                          <div class="card-body p-3 d-flex align-items-center">
                            <div class="bg-warning text-white p-3 me-3">
                              <svg class="icon icon-xl">
                                <use xlink:href="/public/icons/sprites/free.svg#cil-moon"></use>
                              </svg>
                            </div>
                            <div>
                              <div class="fs-6 fw-semibold text-warning">$1.999,50</div>
                              <div class="text-body-secondary text-uppercase fw-semibold small">Widget title</div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <!-- /.col-->
                      <div class="col-12 col-sm-6 col-xl-4 col-xxl-3">
                        <div class="card">
                          <div class="card-body p-3 d-flex align-items-center">
                            <div class="bg-danger text-white p-3 me-3">
                              <svg class="icon icon-xl">
                                <use xlink:href="/public/icons/sprites/free.svg#cil-bell"></use>
                              </svg>
                            </div>
                            <div>
                              <div class="fs-6 fw-semibold text-danger">$1.999,50</div>
                              <div class="text-body-secondary text-uppercase fw-semibold small">Widget title</div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <!-- /.col-->
                    </div>
                  </div>
                </div>
              </div>
              <div class="example">
                <ul class="nav nav-underline-border" role="tablist">
                  <li class="nav-item"><a class="nav-link active" data-toggle="tab" href="#preview-1005" role="tab">
                      <svg class="icon me-2">
                        <use xlink:href="/public/icons/sprites/free.svg#cil-media-play"></use>
                      </svg>Preview</a></li>
                </ul>
                <div class="tab-content rounded-bottom">
                  <div class="tab-pane p-3 active preview" role="tabpanel" id="preview-1005">
                    <div class="row g-4">
                      <div class="col-12 col-sm-6 col-xl-4 col-xxl-3">
                        <div class="card">
                          <div class="card-body p-3 d-flex align-items-center">
                            <div class="bg-primary text-white p-3 me-3">
                              <svg class="icon icon-xl">
                                <use xlink:href="/public/icons/sprites/free.svg#cil-settings"></use>
                              </svg>
                            </div>
                            <div>
                              <div class="fs-6 fw-semibold text-primary">$1.999,50</div>
                              <div class="text-body-secondary text-uppercase fw-semibold small">Widget title</div>
                            </div>
                          </div>
                          <div class="card-footer px-3 py-2"><a class="btn-block text-body-secondary d-flex justify-content-between align-items-center" href="#"><span class="small fw-semibold">View More</span>
                              <svg class="icon">
                                <use xlink:href="/public/icons/sprites/free.svg#cil-chevron-right"></use>
                              </svg></a></div>
                        </div>
                      </div>
                      <!-- /.col-->
                      <div class="col-12 col-sm-6 col-xl-4 col-xxl-3">
                        <div class="card">
                          <div class="card-body p-3 d-flex align-items-center">
                            <div class="bg-info text-white p-3 me-3">
                              <svg class="icon icon-xl">
                                <use xlink:href="/public/icons/sprites/free.svg#cil-laptop"></use>
                              </svg>
                            </div>
                            <div>
                              <div class="fs-6 fw-semibold text-info">$1.999,50</div>
                              <div class="text-body-secondary text-uppercase fw-semibold small">Widget title</div>
                            </div>
                          </div>
                          <div class="card-footer px-3 py-2"><a class="btn-block text-body-secondary d-flex justify-content-between align-items-center" href="#"><span class="small fw-semibold">View More</span>
                              <svg class="icon">
                                <use xlink:href="/public/icons/sprites/free.svg#cil-chevron-right"></use>
                              </svg></a></div>
                        </div>
                      </div>
                      <!-- /.col-->
                      <div class="col-12 col-sm-6 col-xl-4 col-xxl-3">
                        <div class="card">
                          <div class="card-body p-3 d-flex align-items-center">
                            <div class="bg-warning text-white p-3 me-3">
                              <svg class="icon icon-xl">
                                <use xlink:href="/public/icons/sprites/free.svg#cil-moon"></use>
                              </svg>
                            </div>
                            <div>
                              <div class="fs-6 fw-semibold text-warning">$1.999,50</div>
                              <div class="text-body-secondary text-uppercase fw-semibold small">Widget title</div>
                            </div>
                          </div>
                          <div class="card-footer px-3 py-2"><a class="btn-block text-body-secondary d-flex justify-content-between align-items-center" href="#"><span class="small fw-semibold">View More</span>
                              <svg class="icon">
                                <use xlink:href="/public/icons/sprites/free.svg#cil-chevron-right"></use>
                              </svg></a></div>
                        </div>
                      </div>
                      <!-- /.col-->
                      <div class="col-12 col-sm-6 col-xl-4 col-xxl-3">
                        <div class="card">
                          <div class="card-body p-3 d-flex align-items-center">
                            <div class="bg-danger text-white p-3 me-3">
                              <svg class="icon icon-xl">
                                <use xlink:href="/public/icons/sprites/free.svg#cil-bell"></use>
                              </svg>
                            </div>
                            <div>
                              <div class="fs-6 fw-semibold text-danger">$1.999,50</div>
                              <div class="text-body-secondary text-uppercase fw-semibold small">Widget title</div>
                            </div>
                          </div>
                          <div class="card-footer px-3 py-2"><a class="btn-block text-body-secondary d-flex justify-content-between align-items-center" href="#"><span class="small fw-semibold">View More</span>
                              <svg class="icon">
                                <use xlink:href="/public/icons/sprites/free.svg#cil-chevron-right"></use>
                              </svg></a></div>
                        </div>
                      </div>
                      <!-- /.col-->
                    </div>
                    <!-- /.row.g-4-->
                  </div>
                </div>
              </div>
              <div class="example">
                <ul class="nav nav-underline-border" role="tablist">
                  <li class="nav-item"><a class="nav-link active" data-toggle="tab" href="#preview-1006" role="tab">
                      <svg class="icon me-2">
                        <use xlink:href="/public/icons/sprites/free.svg#cil-media-play"></use>
                      </svg>Preview</a></li>
                </ul>
                <div class="tab-content rounded-bottom">
                  <div class="tab-pane p-3 active preview" role="tabpanel" id="preview-1006">
                    <div class="row g-4">
                      <div class="col-12 col-sm-6 col-xl-4 col-xxl-3">
                        <div class="card overflow-hidden">
                          <div class="card-body p-0 d-flex align-items-center">
                            <div class="bg-primary text-white p-4 me-3">
                              <svg class="icon icon-xl">
                                <use xlink:href="/public/icons/sprites/free.svg#cil-settings"></use>
                              </svg>
                            </div>
                            <div>
                              <div class="fs-6 fw-semibold text-primary">$1.999,50</div>
                              <div class="text-body-secondary text-uppercase fw-semibold small">Widget title</div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <!-- /.col-->
                      <div class="col-12 col-sm-6 col-xl-4 col-xxl-3">
                        <div class="card overflow-hidden">
                          <div class="card-body p-0 d-flex align-items-center">
                            <div class="bg-info text-white p-4 me-3">
                              <svg class="icon icon-xl">
                                <use xlink:href="/public/icons/sprites/free.svg#cil-laptop"></use>
                              </svg>
                            </div>
                            <div>
                              <div class="fs-6 fw-semibold text-info">$1.999,50</div>
                              <div class="text-body-secondary text-uppercase fw-semibold small">Widget title</div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <!-- /.col-->
                      <div class="col-12 col-sm-6 col-xl-4 col-xxl-3">
                        <div class="card overflow-hidden">
                          <div class="card-body p-0 d-flex align-items-center">
                            <div class="bg-warning text-white p-4 me-3">
                              <svg class="icon icon-xl">
                                <use xlink:href="/public/icons/sprites/free.svg#cil-moon"></use>
                              </svg>
                            </div>
                            <div>
                              <div class="fs-6 fw-semibold text-warning">$1.999,50</div>
                              <div class="text-body-secondary text-uppercase fw-semibold small">Widget title</div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <!-- /.col-->
                      <div class="col-12 col-sm-6 col-xl-4 col-xxl-3">
                        <div class="card overflow-hidden">
                          <div class="card-body p-0 d-flex align-items-center">
                            <div class="bg-danger text-white p-4 me-3">
                              <svg class="icon icon-xl">
                                <use xlink:href="/public/icons/sprites/free.svg#cil-bell"></use>
                              </svg>
                            </div>
                            <div>
                              <div class="fs-6 fw-semibold text-danger">$1.999,50</div>
                              <div class="text-body-secondary text-uppercase fw-semibold small">Widget title</div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <!-- /.col-->
                    </div>
                  </div>
                </div>
              </div>
              <div class="example">
                <ul class="nav nav-underline-border" role="tablist">
                  <li class="nav-item"><a class="nav-link active" data-toggle="tab" href="#preview-1007" role="tab">
                      <svg class="icon me-2">
                        <use xlink:href="/public/icons/sprites/free.svg#cil-media-play"></use>
                      </svg>Preview</a></li>
                </ul>
                <div class="tab-content rounded-bottom">
                  <div class="tab-pane p-3 active preview" role="tabpanel" id="preview-1007">
                    <div class="row g-4">  
                      <div class="col-12 col-sm-6 col-xl-4 col-xxl-3">
                        <div class="card overflow-hidden">
                          <div class="card-body p-0 d-flex align-items-center">
                            <div class="bg-primary text-white py-4 px-5 me-3">
                              <svg class="icon icon-xl">
                                <use xlink:href="/public/icons/sprites/free.svg#cil-settings"></use>
                              </svg>
                            </div>
                            <div>
                              <div class="fs-6 fw-semibold text-primary">$1.999,50</div>
                              <div class="text-body-secondary text-uppercase fw-semibold small">Widget title</div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <!-- /.col-->
                      <div class="col-12 col-sm-6 col-xl-4 col-xxl-3">
                        <div class="card overflow-hidden">
                          <div class="card-body p-0 d-flex align-items-center">
                            <div class="bg-info text-white py-4 px-5 me-3">
                              <svg class="icon icon-xl">
                                <use xlink:href="/public/icons/sprites/free.svg#cil-laptop"></use>
                              </svg>
                            </div>
                            <div>
                              <div class="fs-6 fw-semibold text-info">$1.999,50</div>
                              <div class="text-body-secondary text-uppercase fw-semibold small">Widget title</div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <!-- /.col-->
                      <div class="col-12 col-sm-6 col-xl-4 col-xxl-3">
                        <div class="card overflow-hidden">
                          <div class="card-body p-0 d-flex align-items-center">
                            <div class="bg-warning text-white py-4 px-5 me-3">
                              <svg class="icon icon-xl">
                                <use xlink:href="/public/icons/sprites/free.svg#cil-moon"></use>
                              </svg>
                            </div>
                            <div>
                              <div class="fs-6 fw-semibold text-warning">$1.999,50</div>
                              <div class="text-body-secondary text-uppercase fw-semibold small">Widget title</div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <!-- /.col-->
                      <div class="col-12 col-sm-6 col-xl-4 col-xxl-3">
                        <div class="card overflow-hidden">
                          <div class="card-body p-0 d-flex align-items-center">
                            <div class="bg-danger text-white py-4 px-5 me-3">
                              <svg class="icon icon-xl">
                                <use xlink:href="/public/icons/sprites/free.svg#cil-bell"></use>
                              </svg>
                            </div>
                            <div>
                              <div class="fs-6 fw-semibold text-danger">$1.999,50</div>
                              <div class="text-body-secondary text-uppercase fw-semibold small">Widget title</div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <!-- /.col-->
                    </div>
                    <!-- /.row.g-4-->
                  </div>
                </div>
              </div>
              <div class="example">
                <ul class="nav nav-underline-border" role="tablist">
                  <li class="nav-item"><a class="nav-link active" data-toggle="tab" href="#preview-1008" role="tab">
                      <svg class="icon me-2">
                        <use xlink:href="/public/icons/sprites/free.svg#cil-media-play"></use>
                      </svg>Preview</a></li>
                </ul>
                <div class="tab-content rounded-bottom">
                  <div class="tab-pane p-3 active preview" role="tabpanel" id="preview-1008">
                    <div class="row g-4">
                      <div class="col-12 col-sm-6 col-xl-4">
                        <div class="card" style="--cui-card-cap-bg: #3b5998">
                          <div class="card-header position-relative d-flex justify-content-center align-items-center">
                            <svg class="icon icon-3xl text-white my-4">
                              <use xlink:href="/public/icons/sprites/brand.svg#cib-facebook-f"></use>
                            </svg>
                            <div class="chart-wrapper position-absolute top-0 start-0 w-100 h-100">
                              <canvas id="social-box-chart-1" height="90"></canvas>
                            </div>
                          </div>
                          <div class="card-body row text-center">
                            <div class="col">
                              <div class="fs-5 fw-semibold">89k</div>
                              <div class="text-uppercase text-body-secondary small">friends</div>
                            </div>
                            <div class="vr"></div>
                            <div class="col">
                              <div class="fs-5 fw-semibold">459</div>
                              <div class="text-uppercase text-body-secondary small">feeds</div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <!-- /.col-->
                      <div class="col-12 col-sm-6 col-xl-4">
                        <div class="card" style="--cui-card-cap-bg: #00aced">
                          <div class="card-header position-relative d-flex justify-content-center align-items-center">
                            <svg class="icon icon-3xl text-white my-4">
                              <use xlink:href="/public/icons/sprites/brand.svg#cib-twitter"></use>
                            </svg>
                            <div class="chart-wrapper position-absolute top-0 start-0 w-100 h-100">
                              <canvas id="social-box-chart-2" height="90"></canvas>
                            </div>
                          </div>
                          <div class="card-body row text-center">
                            <div class="col">
                              <div class="fs-5 fw-semibold">973k</div>
                              <div class="text-uppercase text-body-secondary small">followers</div>
                            </div>
                            <div class="vr"></div>
                            <div class="col">
                              <div class="fs-5 fw-semibold">1.792</div>
                              <div class="text-uppercase text-body-secondary small">tweets</div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <!-- /.col-->
                      <div class="col-12 col-sm-6 col-xl-4">
                        <div class="card" style="--cui-card-cap-bg: #4875b4">
                          <div class="card-header position-relative d-flex justify-content-center align-items-center">
                            <svg class="icon icon-3xl text-white my-4">
                              <use xlink:href="/public/icons/sprites/brand.svg#cib-linkedin"></use>
                            </svg>
                            <div class="chart-wrapper position-absolute top-0 start-0 w-100 h-100">
                              <canvas id="social-box-chart-3" height="90"></canvas>
                            </div>
                          </div>
                          <div class="card-body row text-center">
                            <div class="col">
                              <div class="fs-5 fw-semibold">500+</div>
                              <div class="text-uppercase text-body-secondary small">contacts</div>
                            </div>
                            <div class="vr"></div>
                            <div class="col">
                              <div class="fs-5 fw-semibold">292</div>
                              <div class="text-uppercase text-body-secondary small">feeds</div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <!-- /.col-->
                    </div>
                    <!-- /.row.g-4-->
                  </div>
                </div>
              </div>
              <div class="example">
                <ul class="nav nav-underline-border" role="tablist">
                  <li class="nav-item"><a class="nav-link active" data-toggle="tab" href="#preview-1009" role="tab">
                      <svg class="icon me-2">
                        <use xlink:href="/public/icons/sprites/free.svg#cil-media-play"></use>
                      </svg>Preview</a></li>
                </ul>
                <div class="tab-content rounded-bottom">
                  <div class="tab-pane p-3 active preview" role="tabpanel" id="preview-1009">
                    <div class="card-group">
                      <div class="card">
                        <div class="card-body">
                          <div class="text-body-secondary text-end">
                            <svg class="icon icon-xxl">
                              <use xlink:href="/public/icons/sprites/free.svg#cil-people"></use>
                            </svg>
                          </div>
                          <div class="fs-4 fw-semibold">87.500</div>
                          <div class="small text-body-secondary text-uppercase fw-semibold text-truncate">Visitors</div>
                          <div class="progress progress-thin mt-3 mb-0">
                            <div class="progress-bar bg-info" role="progressbar" style="width: 25%" aria-valuenow="25" aria-valuemin="0" aria-valuemax="100"></div>
                          </div>
                        </div>
                      </div>
                      <div class="card">
                        <div class="card-body">
                          <div class="text-body-secondary text-end">
                            <svg class="icon icon-xxl">
                              <use xlink:href="/public/icons/sprites/free.svg#cil-user-follow"></use>
                            </svg>
                          </div>
                          <div class="fs-4 fw-semibold">385</div>
                          <div class="small text-body-secondary text-uppercase fw-semibold text-truncate">New Clients</div>
                          <div class="progress progress-thin mt-3 mb-0">
                            <div class="progress-bar bg-success" role="progressbar" style="width: 25%" aria-valuenow="25" aria-valuemin="0" aria-valuemax="100"></div>
                          </div>
                        </div>
                      </div>
                      <div class="card">
                        <div class="card-body">
                          <div class="text-body-secondary text-end">
                            <svg class="icon icon-xxl">
                              <use xlink:href="/public/icons/sprites/free.svg#cil-basket"></use>
                            </svg>
                          </div>
                          <div class="fs-4 fw-semibold">1238</div>
                          <div class="small text-body-secondary text-uppercase fw-semibold text-truncate">Products sold</div>
                          <div class="progress progress-thin mt-3 mb-0">
                            <div class="progress-bar bg-warning" role="progressbar" style="width: 25%" aria-valuenow="25" aria-valuemin="0" aria-valuemax="100"></div>
                          </div>
                        </div>
                      </div>
                      <div class="card">
                        <div class="card-body">
                          <div class="text-body-secondary text-end">
                            <svg class="icon icon-xxl">
                              <use xlink:href="/public/icons/sprites/free.svg#cil-chart-pie"></use>
                            </svg>
                          </div>
                          <div class="fs-4 fw-semibold">28%</div>
                          <div class="small text-body-secondary text-uppercase fw-semibold text-truncate">Returning Visitors</div>
                          <div class="progress progress-thin mt-3 mb-0">
                            <div class="progress-bar" role="progressbar" style="width: 25%" aria-valuenow="25" aria-valuemin="0" aria-valuemax="100"></div>
                          </div>
                        </div>
                      </div>
                      <div class="card">
                        <div class="card-body">
                          <div class="text-body-secondary text-end">
                            <svg class="icon icon-xxl">
                              <use xlink:href="/public/icons/sprites/free.svg#cil-speedometer"></use>
                            </svg>
                          </div>
                          <div class="fs-4 fw-semibold">5:34:11</div>
                          <div class="small text-body-secondary text-uppercase fw-semibold text-truncate">Avg. Time</div>
                          <div class="progress progress-thin mt-3 mb-0">
                            <div class="progress-bar bg-danger" role="progressbar" style="width: 25%" aria-valuenow="25" aria-valuemin="0" aria-valuemax="100"></div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="example">
                <ul class="nav nav-underline-border" role="tablist">
                  <li class="nav-item"><a class="nav-link active" data-toggle="tab" href="#preview-1010" role="tab">
                      <svg class="icon me-2">
                        <use xlink:href="/public/icons/sprites/free.svg#cil-media-play"></use>
                      </svg>Preview</a></li>
                </ul>
                <div class="tab-content rounded-bottom">
                  <div class="tab-pane p-3 active preview" role="tabpanel" id="preview-1010">
                    <div class="row g-4">
                      <div class="col-6 col-lg-4 col-xl-3 col-xxl-2">
                        <div class="card">
                          <div class="card-body">
                            <div class="text-body-secondary text-end">
                              <svg class="icon icon-xxl">
                                <use xlink:href="/public/icons/sprites/free.svg#cil-people"></use>
                              </svg>
                            </div>
                            <div class="fs-4 fw-semibold">87.500</div>
                            <div class="small text-body-secondary text-uppercase fw-semibold text-truncate">Visitors</div>
                            <div class="progress progress-thin mt-3 mb-0">
                              <div class="progress-bar bg-info" role="progressbar" style="width: 25%" aria-valuenow="25" aria-valuemin="0" aria-valuemax="100"></div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <!-- /.col-->
                      <div class="col-6 col-lg-4 col-xl-3 col-xxl-2">
                        <div class="card">
                          <div class="card-body">
                            <div class="text-body-secondary text-end">
                              <svg class="icon icon-xxl">
                                <use xlink:href="/public/icons/sprites/free.svg#cil-user-follow"></use>
                              </svg>
                            </div>
                            <div class="fs-4 fw-semibold">385</div>
                            <div class="small text-body-secondary text-uppercase fw-semibold text-truncate">New Clients</div>
                            <div class="progress progress-thin mt-3 mb-0">
                              <div class="progress-bar bg-success" role="progressbar" style="width: 25%" aria-valuenow="25" aria-valuemin="0" aria-valuemax="100"></div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <!-- /.col-->
                      <div class="col-6 col-lg-4 col-xl-3 col-xxl-2">
                        <div class="card">
                          <div class="card-body">
                            <div class="text-body-secondary text-end">
                              <svg class="icon icon-xxl">
                                <use xlink:href="/public/icons/sprites/free.svg#cil-basket"></use>
                              </svg>
                            </div>
                            <div class="fs-4 fw-semibold">1238</div>
                            <div class="small text-body-secondary text-uppercase fw-semibold text-truncate">Products sold</div>
                            <div class="progress progress-thin mt-3 mb-0">
                              <div class="progress-bar bg-warning" role="progressbar" style="width: 25%" aria-valuenow="25" aria-valuemin="0" aria-valuemax="100"></div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <!-- /.col-->
                      <div class="col-6 col-lg-4 col-xl-3 col-xxl-2">
                        <div class="card">
                          <div class="card-body">
                            <div class="text-body-secondary text-end">
                              <svg class="icon icon-xxl">
                                <use xlink:href="/public/icons/sprites/free.svg#cil-chart-pie"></use>
                              </svg>
                            </div>
                            <div class="fs-4 fw-semibold">28%</div>
                            <div class="small text-body-secondary text-uppercase fw-semibold text-truncate">Returning Visitors</div>
                            <div class="progress progress-thin mt-3 mb-0">
                              <div class="progress-bar" role="progressbar" style="width: 25%" aria-valuenow="25" aria-valuemin="0" aria-valuemax="100"></div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <!-- /.col-->
                      <div class="col-6 col-lg-4 col-xl-3 col-xxl-2">
                        <div class="card">
                          <div class="card-body">
                            <div class="text-body-secondary text-end">
                              <svg class="icon icon-xxl">
                                <use xlink:href="/public/icons/sprites/free.svg#cil-speedometer"></use>
                              </svg>
                            </div>
                            <div class="fs-4 fw-semibold">5:34:11</div>
                            <div class="small text-body-secondary text-uppercase fw-semibold text-truncate">Avg. Time</div>
                            <div class="progress progress-thin mt-3 mb-0">
                              <div class="progress-bar bg-danger" role="progressbar" style="width: 25%" aria-valuenow="25" aria-valuemin="0" aria-valuemax="100"></div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <!-- /.col-->
                      <div class="col-6 col-lg-4 col-xl-3 col-xxl-2">
                        <div class="card">
                          <div class="card-body">
                            <div class="text-body-secondary text-end">
                              <svg class="icon icon-xxl">
                                <use xlink:href="/public/icons/sprites/free.svg#cil-speech"></use>
                              </svg>
                            </div>
                            <div class="fs-4 fw-semibold">972</div>
                            <div class="small text-body-secondary text-uppercase fw-semibold text-truncate">Comments</div>
                            <div class="progress progress-thin mt-3 mb-0">
                              <div class="progress-bar bg-info" role="progressbar" style="width: 25%" aria-valuenow="25" aria-valuemin="0" aria-valuemax="100"></div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <!-- /.col-->
                    </div>
                    <!-- /.row.g-4-->
                  </div>
                </div>
              </div>
              <div class="example">
                <ul class="nav nav-underline-border" role="tablist">
                  <li class="nav-item"><a class="nav-link active" data-toggle="tab" href="#preview-1011" role="tab">
                      <svg class="icon me-2">
                        <use xlink:href="/public/icons/sprites/free.svg#cil-media-play"></use>
                      </svg>Preview</a></li>
                </ul>
                <div class="tab-content rounded-bottom">
                  <div class="tab-pane p-3 active preview" role="tabpanel" id="preview-1011">
                    <div class="row g-4">
                      <div class="col-6 col-lg-4 col-xl-3 col-xxl-2">
                        <div class="card text-white bg-info">
                          <div class="card-body">
                            <div class="text-white text-opacity-75 text-end">
                              <svg class="icon icon-xxl">
                                <use xlink:href="/public/icons/sprites/free.svg#cil-people"></use>
                              </svg>
                            </div>
                            <div class="fs-4 fw-semibold">87.500</div>
                            <div class="small text-white text-opacity-75 text-uppercase fw-semibold text-truncate">Visitors</div>
                            <div class="progress progress-white progress-thin mt-3">
                              <div class="progress-bar" role="progressbar" style="width: 25%" aria-valuenow="25" aria-valuemin="0" aria-valuemax="100"></div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <!-- /.col-->
                      <div class="col-6 col-lg-4 col-xl-3 col-xxl-2">
                        <div class="card text-white bg-success">
                          <div class="card-body">
                            <div class="text-white text-opacity-75 text-end">
                              <svg class="icon icon-xxl">
                                <use xlink:href="/public/icons/sprites/free.svg#cil-user-follow"></use>
                              </svg>
                            </div>
                            <div class="fs-4 fw-semibold">385</div>
                            <div class="small text-white text-opacity-75 text-uppercase fw-semibold text-truncate">New Clients</div>
                            <div class="progress progress-white progress-thin mt-3">
                              <div class="progress-bar" role="progressbar" style="width: 25%" aria-valuenow="25" aria-valuemin="0" aria-valuemax="100"></div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <!-- /.col-->
                      <div class="col-6 col-lg-4 col-xl-3 col-xxl-2">
                        <div class="card text-white bg-warning">
                          <div class="card-body">
                            <div class="text-white text-opacity-75 text-end">
                              <svg class="icon icon-xxl">
                                <use xlink:href="/public/icons/sprites/free.svg#cil-basket"></use>
                              </svg>
                            </div>
                            <div class="fs-4 fw-semibold">1238</div>
                            <div class="small text-white text-opacity-75 text-uppercase fw-semibold text-truncate">Products sold</div>
                            <div class="progress progress-white progress-thin mt-3">
                              <div class="progress-bar" role="progressbar" style="width: 25%" aria-valuenow="25" aria-valuemin="0" aria-valuemax="100"></div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <!-- /.col-->
                      <div class="col-6 col-lg-4 col-xl-3 col-xxl-2">
                        <div class="card text-white bg-primary">
                          <div class="card-body">
                            <div class="text-white text-opacity-75 text-end">
                              <svg class="icon icon-xxl">
                                <use xlink:href="/public/icons/sprites/free.svg#cil-chart-pie"></use>
                              </svg>
                            </div>
                            <div class="fs-4 fw-semibold">28%</div>
                            <div class="small text-white text-opacity-75 text-uppercase fw-semibold text-truncate">Returning Visitors</div>
                            <div class="progress progress-white progress-thin mt-3">
                              <div class="progress-bar" role="progressbar" style="width: 25%" aria-valuenow="25" aria-valuemin="0" aria-valuemax="100"></div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <!-- /.col-->
                      <div class="col-6 col-lg-4 col-xl-3 col-xxl-2">
                        <div class="card text-white bg-danger">
                          <div class="card-body">
                            <div class="text-white text-opacity-75 text-end">
                              <svg class="icon icon-xxl">
                                <use xlink:href="/public/icons/sprites/free.svg#cil-speedometer"></use>
                              </svg>
                            </div>
                            <div class="fs-4 fw-semibold">5:34:11</div>
                            <div class="small text-white text-opacity-75 text-uppercase fw-semibold text-truncate">Avg. Time</div>
                            <div class="progress progress-white progress-thin mt-3">
                              <div class="progress-bar" role="progressbar" style="width: 25%" aria-valuenow="25" aria-valuemin="0" aria-valuemax="100"></div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <!-- /.col-->
                      <div class="col-6 col-lg-4 col-xl-3 col-xxl-2">
                        <div class="card text-white bg-info">
                          <div class="card-body">
                            <div class="text-white text-opacity-75 text-end">
                              <svg class="icon icon-xxl">
                                <use xlink:href="/public/icons/sprites/free.svg#cil-speech"></use>
                              </svg>
                            </div>
                            <div class="fs-4 fw-semibold">972</div>
                            <div class="small text-white text-opacity-75 text-uppercase fw-semibold text-truncate">Comments</div>
                            <div class="progress progress-white progress-thin mt-3">
                              <div class="progress-bar" role="progressbar" style="width: 25%" aria-valuenow="25" aria-valuemin="0" aria-valuemax="100"></div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <!-- /.col-->
                    </div>
                    <!-- /.row.g-4-->
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <footer class="footer px-4">
        <div><a href="https://coreui.io">CoreUI </a><a href="https://coreui.io/product/free-bootstrap-admin-template/">Bootstrap Admin Template</a> &copy; 2025 creativeLabs.</div>
        <div class="ms-auto">Powered by&nbsp;<a href="https://coreui.io/docs/">CoreUI UI Components</a></div>
      </footer>
    </div>
    <!-- CoreUI and necessary plugins-->
    <script src="/public/js/utils/coreui.bundle.min.js"></script>
    <script src="/public/js/simplebar/simplebar.min.js"></script>
    <script>
      const header = document.querySelector('header.header');
      
      document.addEventListener('scroll', () => {
        if (header) {
          header.classList.toggle('shadow-sm', document.documentElement.scrollTop > 0);
        }
      });
      
    </script>
    <script src="/public/js/chart.umd.js"></script>
    <script src="/public/js/coreui-chartjs.js"></script>
    <script src="/public/js/utils/index.js"></script>
    <script src="/public/js/widgets.js"></script>

  </body>
</html>