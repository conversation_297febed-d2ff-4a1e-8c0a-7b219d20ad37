const db = require('../config/database');
const permissionService = require('../services/permissionService');

async function demoPermissionSystem() {
  try {
    console.log('🎬 DEMO: Dynamic Permission System như Laravel Voyager\n');
    console.log('='*60);

    // Initialize database connection
    db.connect('development');
    
    // 1. Show system overview
    console.log('📊 SYSTEM OVERVIEW');
    console.log('-'.repeat(30));
    
    const [permissions, roles, users, userRoles] = await Promise.all([
      db.query('SELECT COUNT(*) as count FROM permissions'),
      db.query('SELECT COUNT(*) as count FROM role'),
      db.query('SELECT COUNT(*) as count FROM user'),
      db.query('SELECT COUNT(*) as count FROM user_role')
    ]);
    
    console.log(`✅ Permissions: ${permissions[0].count}`);
    console.log(`✅ Roles: ${roles[0].count}`);
    console.log(`✅ Users: ${users[0].count}`);
    console.log(`✅ User-Role assignments: ${userRoles[0].count}`);
    
    // 2. Demo: Auto-create permissions for new table
    console.log('\n🔄 DEMO: Auto-create permissions cho bảng mới');
    console.log('-'.repeat(50));
    
    const newTableName = 'demo_products';
    console.log(`📝 Tạo permissions cho bảng: ${newTableName}`);
    
    const createdPermissions = await permissionService.createTablePermissions(newTableName);
    console.log(`✅ Đã tạo ${createdPermissions.length} permissions:`);
    createdPermissions.forEach(p => {
      console.log(`   - ${p.name} (${p.action})`);
    });
    
    // 3. Demo: Check user permissions
    console.log('\n👤 DEMO: Kiểm tra quyền user');
    console.log('-'.repeat(30));
    
    const testUserId = 1;
    const userPermissions = await permissionService.getUserPermissions(testUserId);
    console.log(`👤 User ID ${testUserId} có ${userPermissions.length} permissions`);
    
    if (userPermissions.length > 0) {
      console.log('📋 Top 5 permissions:');
      userPermissions.slice(0, 5).forEach(p => {
        console.log(`   ✓ ${p.name} (${p.table_name || 'system'})`);
      });
    }
    
    // 4. Demo: Grant permissions to role
    console.log('\n🎯 DEMO: Gán quyền cho role');
    console.log('-'.repeat(30));
    
    const rolesList = await db.query('SELECT id, name FROM role');
    const targetRole = rolesList.find(r => r.name === 'User');
    
    if (targetRole) {
      console.log(`🎯 Gán quyền browse cho role: ${targetRole.name}`);
      
      // Grant browse permissions to User role
      const browsePermissions = await db.query(`
        SELECT id FROM permissions WHERE action = 'browse'
      `);
      
      for (const perm of browsePermissions.slice(0, 3)) {
        await permissionService.grantPermissionToRole(targetRole.id, perm.id);
      }
      
      console.log(`✅ Đã gán ${browsePermissions.slice(0, 3).length} browse permissions`);
    }
    
    // 5. Demo: Permission middleware simulation
    console.log('\n🛡️ DEMO: Permission Middleware Check');
    console.log('-'.repeat(40));
    
    const testCases = [
      { action: 'browse', table: 'user' },
      { action: 'edit', table: 'role' },
      { action: 'delete', table: 'permissions' },
      { permission: 'browse_admin_menus' }
    ];
    
    for (const testCase of testCases) {
      if (testCase.permission) {
        const hasPermission = await permissionService.checkUserPermission(testUserId, testCase.permission);
        console.log(`${hasPermission ? '✅' : '❌'} User ${testUserId} - ${testCase.permission}: ${hasPermission ? 'ALLOWED' : 'DENIED'}`);
      } else {
        const permissionName = `${testCase.action}_${testCase.table}`;
        const hasPermission = await permissionService.checkUserPermission(testUserId, permissionName);
        console.log(`${hasPermission ? '✅' : '❌'} User ${testUserId} - ${testCase.action} ${testCase.table}: ${hasPermission ? 'ALLOWED' : 'DENIED'}`);
      }
    }
    
    // 6. Demo: Bulk role permissions
    console.log('\n📦 DEMO: Bulk Role Permission Management');
    console.log('-'.repeat(45));
    
    if (targetRole) {
      const rolePermsBefore = await permissionService.getRolePermissions(targetRole.id);
      console.log(`📊 Role "${targetRole.name}" trước: ${rolePermsBefore.length} permissions`);
      
      // Grant all browse permissions
      const allBrowsePerms = await db.query(`
        SELECT id FROM permissions WHERE action = 'browse'
      `);
      
      console.log(`🎯 Gán tất cả ${allBrowsePerms.length} browse permissions...`);
      
      for (const perm of allBrowsePerms) {
        await permissionService.grantPermissionToRole(targetRole.id, perm.id);
      }
      
      const rolePermsAfter = await permissionService.getRolePermissions(targetRole.id);
      console.log(`📊 Role "${targetRole.name}" sau: ${rolePermsAfter.length} permissions`);
      console.log(`✅ Tăng thêm ${rolePermsAfter.length - rolePermsBefore.length} permissions`);
    }
    
    // 7. Demo: Performance with caching
    console.log('\n⚡ DEMO: Performance với Caching');
    console.log('-'.repeat(35));
    
    // Test cache performance
    const iterations = 10;
    let totalTime = 0;
    
    for (let i = 0; i < iterations; i++) {
      const start = Date.now();
      await permissionService.getAllPermissions();
      const time = Date.now() - start;
      totalTime += time;
    }
    
    const avgTime = totalTime / iterations;
    console.log(`⚡ Average query time (${iterations} calls): ${avgTime.toFixed(2)}ms`);
    console.log(`🚀 Cache efficiency: ${avgTime < 5 ? 'EXCELLENT' : avgTime < 10 ? 'GOOD' : 'NEEDS IMPROVEMENT'}`);
    
    // 8. Show permission matrix
    console.log('\n🗂️ DEMO: Permission Matrix Overview');
    console.log('-'.repeat(40));
    
    const allPerms = await permissionService.getAllPermissions();
    const matrix = {};
    
    allPerms.forEach(p => {
      const table = p.table_name || 'system';
      if (!matrix[table]) matrix[table] = {};
      matrix[table][p.action] = (matrix[table][p.action] || 0) + 1;
    });
    
    console.log('📊 Permission matrix by table:');
    Object.keys(matrix).forEach(table => {
      const actions = Object.keys(matrix[table]);
      const total = Object.values(matrix[table]).reduce((a, b) => a + b, 0);
      console.log(`   📋 ${table}: ${total} permissions (${actions.join(', ')})`);
    });
    
    // 9. Clean up demo data
    console.log('\n🧹 CLEANUP: Xóa dữ liệu demo');
    console.log('-'.repeat(30));
    
    // Delete demo permissions
    await db.query(`DELETE FROM permissions WHERE table_name = ?`, [newTableName]);
    console.log(`✅ Đã xóa permissions cho bảng ${newTableName}`);
    
    // Final summary
    console.log('\n🎉 DEMO COMPLETED - Tóm tắt tính năng:');
    console.log('='*50);
    console.log('✅ Auto-create permissions cho bảng mới');
    console.log('✅ Kiểm tra quyền user với cache hiệu quả');
    console.log('✅ Gán/thu hồi quyền role (single & bulk)');
    console.log('✅ Permission middleware simulation');
    console.log('✅ Performance optimization với caching');
    console.log('✅ Permission matrix management');
    console.log('✅ CRUD operations đầy đủ');
    
    console.log('\n🚀 Hệ thống Permission sẵn sàng production!');
    console.log('   Routes: /admin/permissions/*');
    console.log('   Middleware: checkPermission(), requireAdmin()');
    console.log('   Service: permissionService.*');
    
  } catch (error) {
    console.error('❌ Demo failed:', error);
    console.error(error.stack);
  }
  
  process.exit(0);
}

demoPermissionSystem(); 