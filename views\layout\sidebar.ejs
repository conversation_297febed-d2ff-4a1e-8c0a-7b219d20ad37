<div class="sidebar sidebar-dark sidebar-fixed border-end" id="sidebar">
    <div class="sidebar-header border-bottom">
      <div class="sidebar-brand">
        <svg class="sidebar-brand-full" width="88" height="32" alt="CoreUI Logo">
          <use xlink:href="/assets/brand/coreui.svg#full"></use>
        </svg>
        <svg class="sidebar-brand-narrow" width="32" height="32" alt="CoreUI Logo">
          <use xlink:href="/assets/brand/coreui.svg#signet"></use>
        </svg>
      </div>
      <button class="btn-close d-lg-none" type="button" data-theme="dark" aria-label="Close" onclick="coreui.Sidebar.getInstance(document.querySelector('#sidebar')).toggle()"></button>
    </div>
    <ul class="sidebar-nav" data="navigation" data-simplebar>
      <% if (typeof sidebarMenus !== 'undefined' && sidebarMenus) { %>
        <% sidebarMenus.forEach((menu, index) => { %>
          <% if (menu.is_divider) { %>
            <li class="nav-divider"></li>
          <% } else if (menu.is_title) { %>
            <% if (menu.children && menu.children.length > 0) { %>
              <!-- Title menu with children as nav-group -->
              <li class="nav-group">
                <a class="nav-link nav-group-toggle" href="<%= menu.url || '#' %>">
                  <% if (menu.icon) { %>
                    <svg class="nav-icon">
                      <use xlink:href="/icons/sprites/free.svg#cil-<%= menu.icon %>"></use>
                    </svg>
                  <% } %>
                  <%= menu.title %>
                  <% if (menu.badge_text) { %>
                    <span class="badge badge-sm bg-<%= menu.badge_color || 'info' %> ms-auto"><%= menu.badge_text %></span>
                  <% } %>
                </a>
                <ul class="nav-group-items compact">
                  <% menu.children.forEach(child => { %>
                    <!-- Child menu under title: <%= child.title %> -->
                    <li class="nav-item">
                      <a class="nav-link" href="<%= child.url %>"<%= child.target ? ` target="${child.target}"` : '' %>>
                        <span class="nav-icon">
                          <% if (child.icon) { %>
                            <svg class="nav-icon">
                              <use xlink:href="/icons/sprites/free.svg#cil-<%= child.icon %>"></use>
                            </svg>
                          <% } else { %>
                            <span class="nav-icon-bullet"></span>
                          <% } %>
                        </span>
                        <%= child.title %>
                        <% if (child.badge_text) { %>
                          <span class="badge badge-sm bg-<%= child.badge_color || 'info' %> ms-auto"><%= child.badge_text %></span>
                        <% } %>
                      </a>
                    </li>
                  <% }) %>
                </ul>
              </li>
            <% } else { %>
              <!-- Title menu without children -->
              <li class="nav-title"><%= menu.title %></li>
            <% } %>
          <% } else { %>
            <% if (menu.children && menu.children.length > 0) { %>
              <li class="nav-group">
                <a class="nav-link nav-group-toggle" href="<%= menu.url || '#' %>">
                  <% if (menu.icon) { %>
                    <svg class="nav-icon">
                      <use xlink:href="/icons/sprites/free.svg#cil-<%= menu.icon %>"></use>
                    </svg>
                  <% } %>
                  <%= menu.title %>
                  <% if (menu.badge_text) { %>
                    <span class="badge badge-sm bg-<%= menu.badge_color || 'info' %> ms-auto"><%= menu.badge_text %></span>
                  <% } %>
                </a>
                <ul class="nav-group-items compact">
                  <% for(let child of menu.children){ %>
                    <li class="nav-item">
                      <a class="nav-link" href="<%= child.url %>"<%= child.target ? ` target="${child.target}"` : '' %>>
                        <span class="nav-icon">
                          <% if (child.icon) { %>
                            <svg class="nav-icon">
                              <use xlink:href="/icons/sprites/free.svg#cil-<%= child.icon %>"></use>
                            </svg>
                          <% } else { %>
                            <span class="nav-icon-bullet"></span>
                          <% } %>
                        </span>
                        <%= child.title %>
                        <% if (child.badge_text) { %>
                          <span class="badge badge-sm bg-<%= child.badge_color || 'info' %> ms-auto"><%= child.badge_text %></span>
                        <% } %>
                      </a>
                    </li>
                  <% } %>
                </ul>
              </li>
            <% } else { %>
              <!-- Single menu item -->
              <li class="nav-item">
                <a class="nav-link" href="<%= menu.url %>"<%= menu.target ? ` target="${menu.target}"` : '' %>>
                  <% if (menu.icon) { %>
                    <svg class="nav-icon">
                      <use xlink:href="/icons/sprites/free.svg#cil-<%= menu.icon %>"></use>
                    </svg>
                  <% } %>
                  <%= menu.title %>
                  <% if (menu.badge_text) { %>
                    <span class="badge badge-sm bg-<%= menu.badge_color || 'info' %> ms-auto"><%= menu.badge_text %></span>
                  <% } %>
                </a>
              </li>
            <% } %>
          <% } %>
        <% }) %>
        <!-- <li class="nav-item">
          <a class="nav-link" href="/user">
            <svg class="nav-icon">
              <use xlink:href="/icons/sprites/free.svg#cil-people"></use>
            </svg> User</a>
        </li> -->
      <% } else { %>
        <!-- Fallback static menu -->
        <li class="nav-item"><a class="nav-link" href="/">
            <svg class="nav-icon">
              <use xlink:href="/icons/sprites/free.svg#cil-speedometer"></use>
            </svg> Dashboard<span class="badge badge-sm bg-info ms-auto">NEW</span></a></li>
        <li class="nav-title">Database Admin</li>
        <li class="nav-item">
          <a class="nav-link" href="/admin">
            <svg class="nav-icon">
              <use xlink:href="/icons/sprites/free.svg#cil-storage"></use>
            </svg> Dashboard</a>
        </li>
        <li class="nav-item">
          <a class="nav-link" href="/admin/tables">
            <svg class="nav-icon">
              <use xlink:href="/icons/sprites/free.svg#cil-grid"></use>
            </svg> Manage Tables</a>
        </li>
        <li class="nav-item">
          <a class="nav-link" href="/admin/menus">
            <svg class="nav-icon">
              <use xlink:href="/icons/sprites/free.svg#cil-menu"></use>
            </svg> Menu Management</a>
        </li>
      <% } %>
    </ul>
    <div class="sidebar-footer border-top d-none d-md-flex">     
      <button class="sidebar-toggler" type="button" data-toggle="unfoldable"></button>
    </div>
  </div>