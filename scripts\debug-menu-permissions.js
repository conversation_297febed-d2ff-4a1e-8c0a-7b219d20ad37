const db = require('../config/database');
const permissionService = require('../services/permissionService');

async function debugMenuPermissions() {
  try {
    // Connect to database first
    await db.connect();
    console.log('🔍 Debug Menu Permissions Issue\n');

    // 1. <PERSON><PERSON><PERSON> tra permissions có sẵn cho admin_menus
    console.log('1. Checking available admin_menus permissions:');
    const adminMenusPermissions = await db.query(`
      SELECT * FROM permissions WHERE table_name = 'admin_menus' ORDER BY action
    `);
    
    if (adminMenusPermissions.length > 0) {
      console.log(`✅ Found ${adminMenusPermissions.length} admin_menus permissions:`);
      adminMenusPermissions.forEach(perm => {
        console.log(`   - ${perm.action}: ${perm.name} (${perm.display_name})`);
      });
    } else {
      console.log('❌ No admin_menus permissions found');
    }

    // 2. <PERSON><PERSON><PERSON> tra admin có permissions này không
    console.log('\n2. Checking admin role permissions for admin_menus:');
    const adminRolePermissions = await db.query(`
      SELECT p.name, p.display_name, p.action, p.table_name
      FROM role_permissions rp
      INNER JOIN permissions p ON rp.permission_id = p.id
      INNER JOIN role r ON rp.role_id = r.id
      WHERE r.name = 'Admin' AND p.table_name = 'admin_menus'
      ORDER BY p.action
    `);

    if (adminRolePermissions.length > 0) {
      console.log(`✅ Admin has ${adminRolePermissions.length} admin_menus permissions:`);
      adminRolePermissions.forEach(perm => {
        console.log(`   - ${perm.action}: ${perm.name} (${perm.display_name})`);
      });
    } else {
      console.log('❌ Admin does NOT have admin_menus permissions');
    }

    // 3. Test permission checks với các actions khác nhau
    console.log('\n3. Testing permission service checks:');
    const userId = 1; // Assuming admin user id = 1
    
    const actions = ['browse', 'read', 'add', 'edit', 'delete', 'create', 'update'];
    
    for (const action of actions) {
      try {
        const hasPermission = await permissionService.checkUserPermission(userId, null, 'admin_menus', action);
        console.log(`   - ${action}: ${hasPermission ? '✅' : '❌'}`);
      } catch (error) {
        console.log(`   - ${action}: ❌ (Error: ${error.message})`);
      }
    }

    // 4. Check admin status
    console.log('\n4. Checking admin status:');
    const isAdmin = await permissionService.isAdmin(userId);
    console.log(`   - User ${userId} is admin: ${isAdmin ? '✅' : '❌'}`);

    console.log('\n📝 Analysis:');
    console.log('- If admin has "add", "edit", "delete" but controller checks "create", "update", "delete"');
    console.log('- This mismatch causes permission denied for create/update actions');
    console.log('- Solution: Either update controller to use "add"/"edit" or update permissions to use "create"/"update"');

  } catch (error) {
    console.error('❌ Error debugging menu permissions:', error);
  } finally {
    process.exit();
  }
}

debugMenuPermissions(); 