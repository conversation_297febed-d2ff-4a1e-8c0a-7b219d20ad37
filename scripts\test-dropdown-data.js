const db = require('../config/database');
const dynamicCrudService = require('../services/dynamicCrudService');

async function testDropdownData() {
  try {
    await db.connect();
    console.log('🧪 Testing dropdown data functionality...\n');

    // Test auto-detection of display columns
    console.log('📋 Testing display column auto-detection:');
    
    const tables = ['user', 'role'];
    
    for (const tableName of tables) {
      try {
        console.log(`\n🔍 Testing table: ${tableName}`);
        
        // Test display column detection
        const displayColumn = await dynamicCrudService.getDisplayColumnForTable(tableName);
        console.log(`  Auto-detected display column: ${displayColumn}`);
        
        // Test dropdown data
        const dropdownData = await dynamicCrudService.getDropdownData(tableName);
        console.log(`  Dropdown data count: ${dropdownData.length}`);
        
        if (dropdownData.length > 0) {
          console.log(`  Sample data:`, dropdownData.slice(0, 3));
        }
        
      } catch (error) {
        console.log(`  ❌ Error testing ${tableName}:`, error.message);
      }
    }

    // Test specific dropdown calls
    console.log('\n🔧 Testing specific dropdown calls:');
    
    // Test user dropdown with auto-detection
    try {
      const userData = await dynamicCrudService.getDropdownData('user');
      console.log('✅ User dropdown (auto-detect):', userData.length, 'items');
    } catch (error) {
      console.log('❌ User dropdown failed:', error.message);
    }

    // Test user dropdown with specific column
    try {
      const userDataSpecific = await dynamicCrudService.getDropdownData('user', 'id', 'fullname');
      console.log('✅ User dropdown (specific):', userDataSpecific.length, 'items');
    } catch (error) {
      console.log('❌ User dropdown (specific) failed:', error.message);
    }

    // Test role dropdown
    try {
      const roleData = await dynamicCrudService.getDropdownData('role');
      console.log('✅ Role dropdown:', roleData.length, 'items');
    } catch (error) {
      console.log('❌ Role dropdown failed:', error.message);
    }

    console.log('\n✅ Dropdown testing completed!');

  } catch (error) {
    console.error('❌ Test script error:', error);
  }
  
  process.exit(0);
}

testDropdownData();
