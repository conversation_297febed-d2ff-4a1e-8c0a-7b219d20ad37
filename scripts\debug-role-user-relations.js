const db = require('../config/database');
const adminService = require('../services/adminService');
const dynamicCrudService = require('../services/dynamicCrudService');

async function debugRoleUserRelations() {
  try {
    console.log('🔍 Debugging role_user table relations...\n');

    // 1. Check if role_user table exists in admin system
    const roleUserTable = await adminService.getAdminTableByName('role_user');
    
    if (!roleUserTable) {
      console.log('❌ role_user table not found in admin system');
      return;
    }

    console.log('✅ role_user table found:', {
      id: roleUserTable.id,
      name: roleUserTable.name,
      display_name: roleUserTable.display_name
    });

    // 2. Check columns
    console.log('\n📋 Columns:');
    roleUserTable.columns.forEach(col => {
      console.log(`- ${col.name} (${col.type}) - visible_list: ${col.is_visible_list}, visible_form: ${col.is_visible_form}`);
    });

    // 3. Check relations
    console.log('\n🔗 Relations:');
    if (roleUserTable.relations && roleUserTable.relations.length > 0) {
      roleUserTable.relations.forEach(rel => {
        console.log(`- ${rel.column_name || rel.column?.name} -> ${rel.foreign_table_name || rel.foreign_table?.name}.${rel.foreign_column}`);
        console.log(`  Display column: ${rel.display_column}`);
        console.log(`  Relation type: ${rel.relation_type}`);
        console.log('  Full relation object:', JSON.stringify(rel, null, 2));
      });
    } else {
      console.log('❌ No relations found');
    }

    // 4. Check actual data in role_user table
    console.log('\n📊 Sample data from role_user table:');
    const sampleData = await db.query('SELECT * FROM role_user LIMIT 5');
    console.log('Sample records:', sampleData);

    // 5. Test the getTableData function
    console.log('\n🧪 Testing getTableData function:');
    try {
      const result = await dynamicCrudService.getTableData('role_user', {
        page: 1,
        perPage: 5,
        search: '',
        orderBy: 'id',
        orderDirection: 'desc'
      });
      
      console.log('✅ getTableData result:');
      console.log('Total records:', result.total);
      console.log('Data sample:', result.data);
    } catch (error) {
      console.error('❌ Error in getTableData:', error);
    }

    // 6. Check foreign table data
    console.log('\n👥 Checking foreign table data:');
    
    // Check users table
    try {
      const users = await db.query('SELECT id, fullname FROM users LIMIT 3');
      console.log('Users sample:', users);
    } catch (error) {
      console.log('❌ Error fetching users:', error.message);
    }

    // Check roles table
    try {
      const roles = await db.query('SELECT id, name FROM roles LIMIT 3');
      console.log('Roles sample:', roles);
    } catch (error) {
      console.log('❌ Error fetching roles:', error.message);
    }

    // 7. Test manual relation query
    console.log('\n🔧 Testing manual relation queries:');
    
    if (sampleData.length > 0) {
      const firstRecord = sampleData[0];
      
      // Test user relation
      if (firstRecord.user_id) {
        try {
          const userResult = await db.query('SELECT fullname FROM users WHERE id = ?', [firstRecord.user_id]);
          console.log(`User for ID ${firstRecord.user_id}:`, userResult);
        } catch (error) {
          console.log('❌ Error fetching user relation:', error.message);
        }
      }

      // Test role relation
      if (firstRecord.role_id) {
        try {
          const roleResult = await db.query('SELECT name FROM roles WHERE id = ?', [firstRecord.role_id]);
          console.log(`Role for ID ${firstRecord.role_id}:`, roleResult);
        } catch (error) {
          console.log('❌ Error fetching role relation:', error.message);
        }
      }
    }

    // 8. Test processRowsWithRelations function directly
    console.log('\n⚙️ Testing processRowsWithRelations function:');
    if (sampleData.length > 0) {
      try {
        const processedData = await dynamicCrudService.processRowsWithRelations(sampleData, roleUserTable);
        console.log('✅ Processed data:', processedData);
      } catch (error) {
        console.error('❌ Error in processRowsWithRelations:', error);
      }
    }

  } catch (error) {
    console.error('❌ Debug script error:', error);
  } finally {
    process.exit(0);
  }
}

// Run the debug script
debugRoleUserRelations();
