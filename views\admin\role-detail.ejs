<style>
  .role-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
  }
  
  .stats-card {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    border: 1px solid #e3f2fd;
    margin-bottom: 1.5rem;
  }
  
  .permission-badge {
    background: linear-gradient(45deg, #2196F3, #21CBF3);
    color: white;
    border-radius: 20px;
    padding: 0.25rem 0.75rem;
    font-size: 0.75rem;
    font-weight: 600;
    display: inline-block;
    margin: 0.25rem;
  }
  
  .action-badge {
    font-size: 0.7rem;
    padding: 0.2rem 0.5rem;
    border-radius: 10px;
    font-weight: 600;
  }
  
  .action-read { background-color: #e3f2fd; color: #1976d2; }
  .action-add { background-color: #e8f5e8; color: #388e3c; }
  .action-edit { background-color: #fff3e0; color: #f57c00; }
  .action-delete { background-color: #ffebee; color: #d32f2f; }
  
  .user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(45deg, #667eea, #764ba2);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    margin-right: 1rem;
  }
  
  .table-responsive {
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  }
</style>

<div class="container-fluid">
  <!-- Role Header -->
  <div class="role-header">
    <div class="d-flex justify-content-between align-items-start">
      <div>
        <h1 class="mb-2">
          <i class="fas fa-user-tag me-2"></i>
          <%= role.name %>
        </h1>
        <p class="mb-0 opacity-75">
          Chi tiết role và phân quyền trong hệ thống
        </p>
      </div>
      
      <div class="btn-group" role="group">
        <a href="/admin/roles" class="btn btn-outline-light">
          <i class="fas fa-arrow-left me-1"></i>Quay lại
        </a>
        <a href="/admin/roles/<%= role.id %>/edit" class="btn btn-light">
          <i class="fas fa-edit me-1"></i>Chỉnh sửa
        </a>
        <a href="/admin/permissions/roles/<%= role.id %>" class="btn btn-success">
          <i class="fas fa-shield-alt me-1"></i>Quản lý quyền
        </a>
      </div>
    </div>
  </div>

  <!-- Stats Row -->
  <div class="row mb-4">
    <div class="col-md-6">
      <div class="stats-card">
        <div class="d-flex align-items-center">
          <div class="flex-shrink-0">
            <div class="bg-primary text-white rounded-circle p-3">
              <i class="fas fa-shield-alt fa-2x"></i>
            </div>
          </div>
          <div class="flex-grow-1 ms-3">
            <h3 class="mb-0"><%= stats.permissions_count %></h3>
            <p class="text-muted mb-0">Permissions</p>
            <small class="text-success">Đã được gán cho role này</small>
          </div>
        </div>
      </div>
    </div>
    
    <div class="col-md-6">
      <div class="stats-card">
        <div class="d-flex align-items-center">
          <div class="flex-shrink-0">
            <div class="bg-success text-white rounded-circle p-3">
              <i class="fas fa-users fa-2x"></i>
            </div>
          </div>
          <div class="flex-grow-1 ms-3">
            <h3 class="mb-0"><%= stats.users_count %></h3>
            <p class="text-muted mb-0">Users</p>
            <small class="text-info">Đang sử dụng role này</small>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Content Tabs -->
  <div class="row">
    <div class="col-12">
      <ul class="nav nav-tabs" id="roleDetailTabs" role="tablist">
        <li class="nav-item" role="presentation">
          <button class="nav-link active" id="permissions-tab" data-bs-toggle="tab" 
                  data-bs-target="#permissions" type="button" role="tab">
            <i class="fas fa-shield-alt me-1"></i>
            Permissions (<%= stats.permissions_count %>)
          </button>
        </li>
        <li class="nav-item" role="presentation">
          <button class="nav-link" id="users-tab" data-bs-toggle="tab" 
                  data-bs-target="#users" type="button" role="tab">
            <i class="fas fa-users me-1"></i>
            Users (<%= stats.users_count %>)
          </button>
        </li>
      </ul>
    </div>
  </div>

  <!-- Tab Content -->
  <div class="tab-content mt-3" id="roleDetailTabsContent">
    <!-- Permissions Tab -->
    <div class="tab-pane fade show active" id="permissions" role="tabpanel">
      <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
          <h5 class="mb-0">
            <i class="fas fa-shield-alt text-primary me-2"></i>
            Danh sách Permissions
          </h5>
          <a href="/admin/permissions/roles/<%= role.id %>" class="btn btn-primary btn-sm">
            <i class="fas fa-cog me-1"></i>Quản lý quyền
          </a>
        </div>
        <div class="card-body">
          <% if (permissions.length > 0) { %>
            <!-- Group permissions by table -->
            <%
              const groupedPermissions = {};
              permissions.forEach(perm => {
                const table = perm.table_name || 'system';
                if (!groupedPermissions[table]) {
                  groupedPermissions[table] = [];
                }
                groupedPermissions[table].push(perm);
              });
            %>
            
            <% Object.keys(groupedPermissions).forEach(tableName => { %>
              <div class="mb-4">
                <h6 class="text-muted mb-3">
                  <i class="fas fa-table me-1"></i>
                  <%= tableName.charAt(0).toUpperCase() + tableName.slice(1) %>
                </h6>
                
                <div class="table-responsive">
                  <table class="table table-sm table-hover">
                    <thead class="table-light">
                      <tr>
                        <th>Permission</th>
                        <th>Action</th>
                        <th>Mô tả</th>
                        <th>Ngày gán</th>
                      </tr>
                    </thead>
                    <tbody>
                      <% groupedPermissions[tableName].forEach(permission => { %>
                        <tr>
                          <td>
                            <strong><%= permission.display_name %></strong>
                            <br>
                            <small class="text-muted"><%= permission.name %></small>
                          </td>
                          <td>
                            <span class="action-badge action-<%= permission.action %>">
                              <%= permission.action %>
                            </span>
                          </td>
                          <td>
                            <small class="text-muted">
                              <%= permission.description || 'Không có mô tả' %>
                            </small>
                          </td>
                          <td>
                            <small class="text-muted">
                              <%= permission.granted_at ? new Date(permission.granted_at).toLocaleDateString('vi-VN') : '-' %>
                            </small>
                          </td>
                        </tr>
                      <% }); %>
                    </tbody>
                  </table>
                </div>
              </div>
            <% }); %>
          <% } else { %>
            <div class="text-center py-5">
              <i class="fas fa-shield-alt text-muted mb-3" style="font-size: 4rem;"></i>
              <h5 class="text-muted">Chưa có permissions nào</h5>
              <p class="text-muted mb-3">Role này chưa được gán bất kỳ quyền nào.</p>
              <a href="/admin/permissions/roles/<%= role.id %>" class="btn btn-primary">
                <i class="fas fa-plus me-1"></i>Gán quyền ngay
              </a>
            </div>
          <% } %>
        </div>
      </div>
    </div>

    <!-- Users Tab -->
    <div class="tab-pane fade" id="users" role="tabpanel">
      <div class="card">
        <div class="card-header">
          <h5 class="mb-0">
            <i class="fas fa-users text-success me-2"></i>
            Users với Role này
          </h5>
        </div>
        <div class="card-body">
          <% if (users.length > 0) { %>
            <div class="table-responsive">
              <table class="table table-hover">
                <thead class="table-light">
                  <tr>
                    <th>User</th>
                    <th>Email</th>
                    <th>Ngày gán</th>
                    <th>Thao tác</th>
                  </tr>
                </thead>
                <tbody>
                  <% users.forEach(user => { %>
                    <tr>
                      <td>
                        <div class="d-flex align-items-center">
                          <div class="user-avatar">
                            <%= user.fullname ? user.fullname.charAt(0).toUpperCase() : user.email.charAt(0).toUpperCase() %>
                          </div>
                          <div>
                            <strong><%= user.fullname || 'N/A' %></strong>
                            <br>
                            <small class="text-muted">ID: <%= user.id %></small>
                          </div>
                        </div>
                      </td>
                      <td>
                        <span class="text-primary"><%= user.email %></span>
                      </td>
                      <td>
                        <small class="text-muted">
                          <%= user.assigned_at ? new Date(user.assigned_at).toLocaleDateString('vi-VN') : '-' %>
                        </small>
                      </td>
                      <td>
                        <div class="btn-group btn-group-sm">
                          <a href="/admin/users/<%= user.id %>" class="btn btn-outline-info" title="Xem user">
                            <i class="fas fa-eye"></i>
                          </a>
                          <button type="button" class="btn btn-outline-warning" 
                                  onclick="removeUserFromRole(<%= user.id %>, '<%= user.fullname || user.email %>')" 
                                  title="Gỡ role">
                            <i class="fas fa-user-minus"></i>
                          </button>
                        </div>
                      </td>
                    </tr>
                  <% }); %>
                </tbody>
              </table>
            </div>
          <% } else { %>
            <div class="text-center py-5">
              <i class="fas fa-users text-muted mb-3" style="font-size: 4rem;"></i>
              <h5 class="text-muted">Chưa có user nào</h5>
              <p class="text-muted mb-3">Chưa có user nào được gán role này.</p>
              <a href="/admin/users" class="btn btn-success">
                <i class="fas fa-users me-1"></i>Quản lý Users
              </a>
            </div>
          <% } %>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Remove User Modal -->
<div class="modal fade" id="removeUserModal" tabindex="-1">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">
          <i class="fas fa-user-minus text-warning me-2"></i>
          Gỡ Role khỏi User
        </h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
      </div>
      <div class="modal-body">
        <p class="mb-0">
          Bạn có chắc chắn muốn gỡ role <strong><%= role.name %></strong> 
          khỏi user <strong id="removeUserName"></strong>?
        </p>
        <div class="alert alert-warning mt-3 mb-0">
          <i class="fas fa-exclamation-triangle me-2"></i>
          User sẽ mất tất cả quyền từ role này!
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Hủy</button>
        <button type="button" class="btn btn-warning" id="confirmRemoveUser">
          <i class="fas fa-user-minus me-1"></i>Gỡ Role
        </button>
      </div>
    </div>
  </div>
</div>

<script>
// Remove user from role
let removeUserId = null;

function removeUserFromRole(userId, userName) {
  removeUserId = userId;
  document.getElementById('removeUserName').textContent = userName;
  $('#removeUserModal').modal('show');
  //new bootstrap.Modal(document.getElementById('removeUserModal')).show();
}

document.getElementById('confirmRemoveUser').addEventListener('click', function() {
  if (removeUserId) {
    fetch(`/admin/users/${removeUserId}/roles/<%= role.id %>`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
      }
    })
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        showAlert('success', data.message);
        setTimeout(() => location.reload(), 1500);
      } else {
        showAlert('danger', data.message);
      }
    })
    .catch(error => {
      showAlert('danger', 'Có lỗi xảy ra khi gỡ role');
    });
    $('#removeUserModal').modal('hide');
    //bootstrap.Modal.getInstance(document.getElementById('removeUserModal')).hide();
  }
});

// Show alert function
function showAlert(type, message) {
  const alertDiv = document.createElement('div');
  alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
  alertDiv.style.top = '20px';
  alertDiv.style.right = '20px';
  alertDiv.style.zIndex = '9999';
  alertDiv.style.minWidth = '300px';
  alertDiv.innerHTML = `
    <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-triangle'} me-2"></i>
    ${message}
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
  `;
  document.body.appendChild(alertDiv);
  
  setTimeout(() => {
    if (alertDiv.parentNode) {
      alertDiv.parentNode.removeChild(alertDiv);
    }
  }, 5000);
}
</script> 