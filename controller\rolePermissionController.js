const permissionService = require('../services/permissionService');
const db = require('../config/database');

class RolePermissionController {
  /**
   * Hi<PERSON>n thị trang quản lý quyền của role
   */
  async index(req, res) {
    try {
      const { roleId } = req.params;
      const { table_name = '', action = '' } = req.query;

      // Lấy thông tin role
      const role = await db.queryOne(`
        SELECT id, name FROM role WHERE id = ?
      `, [roleId]);

      if (!role) {
        return res.status(404).render('404', { message: 'Role không tồn tại' });
      }

      // Xây dựng điều kiện WHERE cho filter
      let whereConditions = [];
      let queryParams = [];

      if (table_name) {
        whereConditions.push('p.table_name = ?');
        queryParams.push(table_name);
      }

      if (action) {
        whereConditions.push('p.action = ?');
        queryParams.push(action);
      }

      const whereClause = whereConditions.length > 0 ? ` AND ${whereConditions.join(' AND ')}` : '';

      // Lấy tất cả permissions với trạng thái có được gán cho role này không
      const permissions = await db.query(`
        SELECT 
          p.id,
          p.name,
          p.display_name,
          p.description,
          p.table_name,
          p.action,
          CASE WHEN rp.permission_id IS NOT NULL THEN 1 ELSE 0 END as is_granted,
          rp.granted_at
        FROM permissions p
        LEFT JOIN role_permissions rp ON p.id = rp.permission_id AND rp.role_id = ?
        WHERE 1=1 ${whereClause}
        ORDER BY p.table_name, 
                 CASE p.action
                   WHEN 'read' THEN 1
                   WHEN 'edit' THEN 2
                   WHEN 'add' THEN 3
                   WHEN 'delete' THEN 4
                   ELSE 5
                 END
      `, [roleId, ...queryParams]);

      // Nhóm permissions theo table
      const groupedPermissions = {};
      permissions.forEach(permission => {
        const tableName = permission.table_name || 'system';
        if (!groupedPermissions[tableName]) {
          groupedPermissions[tableName] = [];
        }
        groupedPermissions[tableName].push(permission);
      });

      // Lấy danh sách tables và actions cho filter
      const [tables, actions] = await Promise.all([
        db.query('SELECT DISTINCT table_name FROM permissions WHERE table_name IS NOT NULL ORDER BY table_name'),
        db.query('SELECT DISTINCT action FROM permissions WHERE action IS NOT NULL ORDER BY action')
      ]);

      // Thống kê quyền
      const stats = {
        total: permissions.length,
        granted: permissions.filter(p => p.is_granted).length,
        denied: permissions.filter(p => !p.is_granted).length
      };

      const breadcrumb = [
        {name: 'Home', url:'/', active: false},
        {name: 'Roles', url:'/admin/roles', active: false},
        {name: 'Detail', url:'/admin/roles', active: true},
      ];
      res.render('admin/role-permissions', {
        title: `Quản lý quyền - ${role.name}`,
        role,
        permissions,
        groupedPermissions,
        tables,
        actions,
        stats,
        filters: { table_name, action },
        user: req.user,
        breadcrumb: breadcrumb
      });
    } catch (error) {
      console.error('Error in role permissions index:', error);
      res.status(500).render('500', { error: error.message, title: 'Error' });
    }
  }

  /**
   * Gán quyền cho role
   */
  async grant(req, res) {
    try {
      const { roleId } = req.params;
      const { permissionId } = req.body;

      if (!permissionId) {
        return res.status(400).json({
          success: false,
          message: 'Permission ID là bắt buộc'
        });
      }

      // Kiểm tra role và permission có tồn tại không
      const [role, permission] = await Promise.all([
        db.queryOne('SELECT id, name FROM role WHERE id = ?', [roleId]),
        db.queryOne('SELECT id, name FROM permissions WHERE id = ?', [permissionId])
      ]);

      if (!role) {
        return res.status(404).json({
          success: false,
          message: 'Role không tồn tại'
        });
      }

      if (!permission) {
        return res.status(404).json({
          success: false,
          message: 'Permission không tồn tại'
        });
      }

      // Gán quyền
      await permissionService.grantPermissionToRole(roleId, permissionId, req.user.id);

      res.json({
        success: true,
        message: `Đã gán quyền "${permission.name}" cho role "${role.name}"`
      });
    } catch (error) {
      console.error('Error granting permission:', error);
      res.status(500).json({
        success: false,
        message: 'Lỗi gán quyền: ' + error.message
      });
    }
  }

  /**
   * Thu hồi quyền từ role
   */
  async revoke(req, res) {
    try {
      const { roleId } = req.params;
      const { permissionId } = req.body;

      if (!permissionId) {
        return res.status(400).json({
          success: false,
          message: 'Permission ID là bắt buộc'
        });
      }

      // Kiểm tra role và permission có tồn tại không
      const [role, permission] = await Promise.all([
        db.queryOne('SELECT id, name FROM role WHERE id = ?', [roleId]),
        db.queryOne('SELECT id, name FROM permissions WHERE id = ?', [permissionId])
      ]);

      if (!role) {
        return res.status(404).json({
          success: false,
          message: 'Role không tồn tại'
        });
      }

      if (!permission) {
        return res.status(404).json({
          success: false,
          message: 'Permission không tồn tại'
        });
      }

      // Thu hồi quyền
      await permissionService.revokePermissionFromRole(roleId, permissionId);

      res.json({
        success: true,
        message: `Đã thu hồi quyền "${permission.name}" từ role "${role.name}"`
      });
    } catch (error) {
      console.error('Error revoking permission:', error);
      res.status(500).json({
        success: false,
        message: 'Lỗi thu hồi quyền: ' + error.message
      });
    }
  }

  /**
   * Gán hàng loạt quyền cho role
   */
  async grantBulk(req, res) {
    try {
      const { roleId } = req.params;
      const { permissionIds } = req.body;

      if (!Array.isArray(permissionIds) || permissionIds.length === 0) {
        return res.status(400).json({
          success: false,
          message: 'Danh sách permission IDs là bắt buộc'
        });
      }

      // Kiểm tra role có tồn tại không
      const role = await db.queryOne('SELECT id, name FROM role WHERE id = ?', [roleId]);
      if (!role) {
        return res.status(404).json({
          success: false,
          message: 'Role không tồn tại'
        });
      }

      let successCount = 0;
      let errorCount = 0;

      // Gán từng quyền
      for (const permissionId of permissionIds) {
        try {
          await permissionService.grantPermissionToRole(roleId, permissionId, req.user.id);
          successCount++;
        } catch (error) {
          console.error(`Error granting permission ${permissionId}:`, error);
          errorCount++;
        }
      }

      res.json({
        success: true,
        message: `Đã gán ${successCount} quyền thành công${errorCount > 0 ? `, ${errorCount} quyền thất bại` : ''}`,
        data: { successCount, errorCount }
      });
    } catch (error) {
      console.error('Error in bulk grant:', error);
      res.status(500).json({
        success: false,
        message: 'Lỗi gán hàng loạt quyền: ' + error.message
      });
    }
  }

  /**
   * Thu hồi hàng loạt quyền từ role
   */
  async revokeBulk(req, res) {
    try {
      const { roleId } = req.params;
      const { permissionIds } = req.body;

      if (!Array.isArray(permissionIds) || permissionIds.length === 0) {
        return res.status(400).json({
          success: false,
          message: 'Danh sách permission IDs là bắt buộc'
        });
      }

      // Kiểm tra role có tồn tại không
      const role = await db.queryOne('SELECT id, name FROM role WHERE id = ?', [roleId]);
      if (!role) {
        return res.status(404).json({
          success: false,
          message: 'Role không tồn tại'
        });
      }

      let successCount = 0;
      let errorCount = 0;

      // Thu hồi từng quyền
      for (const permissionId of permissionIds) {
        try {
          await permissionService.revokePermissionFromRole(roleId, permissionId);
          successCount++;
        } catch (error) {
          console.error(`Error revoking permission ${permissionId}:`, error);
          errorCount++;
        }
      }

      res.json({
        success: true,
        message: `Đã thu hồi ${successCount} quyền thành công${errorCount > 0 ? `, ${errorCount} quyền thất bại` : ''}`,
        data: { successCount, errorCount }
      });
    } catch (error) {
      console.error('Error in bulk revoke:', error);
      res.status(500).json({
        success: false,
        message: 'Lỗi thu hồi hàng loạt quyền: ' + error.message
      });
    }
  }

  /**
   * Gán tất cả quyền của một bảng cho role
   */
  async grantTablePermissions(req, res) {
    try {
      const { roleId } = req.params;
      const { tableName } = req.body;

      if (!tableName) {
        return res.status(400).json({
          success: false,
          message: 'Tên bảng là bắt buộc'
        });
      }

      // Kiểm tra role có tồn tại không
      const role = await db.queryOne('SELECT id, name FROM role WHERE id = ?', [roleId]);
      if (!role) {
        return res.status(404).json({
          success: false,
          message: 'Role không tồn tại'
        });
      }

      // Lấy tất cả quyền của bảng
      const permissions = await db.query(`
        SELECT id FROM permissions WHERE table_name = ?
      `, [tableName]);

      if (permissions.length === 0) {
        return res.status(404).json({
          success: false,
          message: 'Không tìm thấy quyền nào cho bảng này'
        });
      }

      let successCount = 0;
      let errorCount = 0;

      // Gán từng quyền
      for (const permission of permissions) {
        try {
          await permissionService.grantPermissionToRole(roleId, permission.id, req.user.id);
          successCount++;
        } catch (error) {
          console.error(`Error granting permission ${permission.id}:`, error);
          errorCount++;
        }
      }

      res.json({
        success: true,
        message: `Đã gán ${successCount}/${permissions.length} quyền của bảng "${tableName}" cho role "${role.name}"`,
        data: { successCount, errorCount, total: permissions.length }
      });
    } catch (error) {
      console.error('Error granting table permissions:', error);
      res.status(500).json({
        success: false,
        message: 'Lỗi gán quyền bảng: ' + error.message
      });
    }
  }

  /**
   * Thu hồi tất cả quyền của một bảng từ role
   */
  async revokeTablePermissions(req, res) {
    try {
      const { roleId } = req.params;
      const { tableName } = req.body;

      if (!tableName) {
        return res.status(400).json({
          success: false,
          message: 'Tên bảng là bắt buộc'
        });
      }

      // Kiểm tra role có tồn tại không
      const role = await db.queryOne('SELECT id, name FROM role WHERE id = ?', [roleId]);
      if (!role) {
        return res.status(404).json({
          success: false,
          message: 'Role không tồn tại'
        });
      }

      // Thu hồi tất cả quyền của bảng
      const result = await db.query(`
        DELETE rp FROM role_permissions rp
        INNER JOIN permissions p ON rp.permission_id = p.id
        WHERE rp.role_id = ? AND p.table_name = ?
      `, [roleId, tableName]);

      res.json({
        success: true,
        message: `Đã thu hồi ${result.affectedRows} quyền của bảng "${tableName}" từ role "${role.name}"`,
        data: { revokedCount: result.affectedRows }
      });
    } catch (error) {
      console.error('Error revoking table permissions:', error);
      res.status(500).json({
        success: false,
        message: 'Lỗi thu hồi quyền bảng: ' + error.message
      });
    }
  }

  /**
   * Lấy danh sách roles và permissions (API)
   */
  async getRolesList(req, res) {
    try {
      const roles = await db.query(`
        SELECT 
          r.id,
          r.name,
          r.name as role_display_name,
          COUNT(rp.permission_id) as permissions_count
        FROM role r
        LEFT JOIN role_permissions rp ON r.id = rp.role_id
        GROUP BY r.id, r.name
        ORDER BY r.name
      `);

      res.json({
        success: true,
        data: roles
      });
    } catch (error) {
      console.error('Error getting roles list:', error);
      res.status(500).json({
        success: false,
        message: 'Lỗi lấy danh sách roles: ' + error.message
      });
    }
  }

  /**
   * Lấy permissions của role (API)
   */
  async getRolePermissions(req, res) {
    try {
      const { roleId } = req.params;

      const permissions = await permissionService.getRolePermissions(roleId);

      res.json({
        success: true,
        data: permissions
      });
    } catch (error) {
      console.error('Error getting role permissions:', error);
      res.status(500).json({
        success: false,
        message: 'Lỗi lấy permissions của role: ' + error.message
      });
    }
  }
}

module.exports = new RolePermissionController(); 