const db = require('../config/database');

async function testPermissionAPI() {
  try {
    console.log('🔌 Connecting to database...');
    await db.connect();
    
    console.log('📊 Testing permission filter options...');
    
    // Test 1: <PERSON><PERSON><PERSON> danh sách tables từ admintable
    console.log('\n1. Testing tables from admintable:');
    const tables = await db.query('SELECT name as table_name, display_name FROM admintable WHERE is_active = 1 ORDER BY display_name');
    console.log(`   Found ${tables.length} active tables:`);
    tables.forEach(table => {
      console.log(`   - ${table.display_name} (${table.table_name})`);
    });
    
    // Test 2: <PERSON><PERSON><PERSON> danh sách actions từ permissions
    console.log('\n2. Testing actions from permissions:');
    const actions = await db.query('SELECT DISTINCT action FROM permissions WHERE action IS NOT NULL ORDER BY action');
    console.log(`   Found ${actions.length} unique actions:`);
    actions.forEach(action => {
      console.log(`   - ${action.action}`);
    });
    
    // Test 3: <PERSON><PERSON><PERSON> tra permissions hiện tại
    console.log('\n3. Testing current permissions:');
    const permissionStats = await db.query(`
      SELECT 
        table_name,
        COUNT(*) as permission_count,
        GROUP_CONCAT(DISTINCT action ORDER BY action) as actions
      FROM permissions 
      WHERE table_name IS NOT NULL 
      GROUP BY table_name 
      ORDER BY table_name
    `);
    
    console.log(`   Found permissions for ${permissionStats.length} tables:`);
    permissionStats.forEach(stat => {
      console.log(`   - ${stat.table_name}: ${stat.permission_count} permissions (${stat.actions})`);
    });
    
    // Test 4: Kiểm tra permissions thiếu
    console.log('\n4. Checking missing permissions:');
    const tablesWithoutPermissions = await db.query(`
      SELECT at.name, at.display_name
      FROM admintable at
      LEFT JOIN permissions p ON at.name = p.table_name
      WHERE at.is_active = 1 AND p.table_name IS NULL
    `);
    
    if (tablesWithoutPermissions.length > 0) {
      console.log(`   ⚠️  Found ${tablesWithoutPermissions.length} tables without permissions:`);
      tablesWithoutPermissions.forEach(table => {
        console.log(`   - ${table.display_name} (${table.name})`);
      });
    } else {
      console.log('   ✅ All active tables have permissions');
    }
    
    console.log('\n✅ Test completed successfully!');
    process.exit(0);
  } catch (error) {
    console.error('❌ Error testing permission API:', error);
    process.exit(1);
  }
}

// Chạy test nếu được gọi trực tiếp
if (require.main === module) {
  testPermissionAPI();
}

module.exports = { testPermissionAPI };
