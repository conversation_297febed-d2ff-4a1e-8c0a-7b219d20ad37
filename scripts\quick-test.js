const db = require('../config/database');

async function quickTest() {
  try {
    console.log('🧪 Quick test of device functionality...\n');
    
    // Connect to database
    db.connect('development');
    console.log('✅ Connected to database');

    // Test basic queries
    console.log('\n📋 Testing basic database queries...');
    
    const users = await db.query('SELECT id, fullname, email FROM user LIMIT 1');
    console.log(`✅ Found ${users.length} users`);
    
    const sessions = await db.query('SELECT COUNT(*) as count FROM user_sessions');
    console.log(`✅ Found ${sessions[0].count} sessions`);
    
    const settings = await db.query('SELECT COUNT(*) as count FROM user_session_settings');
    console.log(`✅ Found ${settings[0].count} session settings`);

    // Test multiDeviceService import
    console.log('\n📋 Testing service imports...');
    
    try {
      const multiDeviceService = require('../services/multiDeviceService');
      console.log('✅ multiDeviceService imported successfully');
      
      const jwtService = require('../services/jwtService');
      console.log('✅ jwtService imported successfully');
      
      // Test basic functionality
      const testUser = { id: 1, email: '<EMAIL>', fullname: 'Admin' };
      const { token, tokenId } = jwtService.createToken(testUser);
      console.log('✅ JWT token created successfully');
      
      const deviceInfo = multiDeviceService.detectDeviceInfo('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');
      console.log(`✅ Device detected: ${deviceInfo.deviceName}`);
      
    } catch (error) {
      console.log(`❌ Service import error: ${error.message}`);
    }

    console.log('\n🎉 Quick test completed successfully!');

  } catch (error) {
    console.error('💥 Error in quick test:', error);
  } finally {
    try {
      const pool = db.get();
      if (pool) {
        pool.end();
        console.log('🔌 Database connection closed');
      }
    } catch (error) {
      console.log('⚠️  Database connection already closed');
    }
  }
}

quickTest();
