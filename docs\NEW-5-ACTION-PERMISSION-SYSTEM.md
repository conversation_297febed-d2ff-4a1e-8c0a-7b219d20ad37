# New 5-Action Permission System

## Overview
Hệ thống phân quyền đã được refactor hoàn toàn với **5 actions granular** và **flash message notifications** cho better user experience.

## 🎯 New Permission Actions

### 1. **browse** - View List/Table Data
- **Replaces**: Old "read" permission for lists  
- **Purpose**: <PERSON>em danh s<PERSON>ch, access table/DataTable
- **Routes**: `GET /admin/menus`, `GET /admin/menus/data`
- **Example**: `browse_admin_menus`

### 2. **read** - View Single Record Details  
- **Purpose**: Xem chi tiết 1 record cụ thể
- **Routes**: `GET /admin/menus/:id`
- **Frontend**: View details button trong table
- **Example**: `read_admin_menus`

### 3. **create** - Add New Records
- **Replaces**: Old "add" permission
- **Purpose**: Tạo record mới
- **Routes**: `POST /admin/menus`
- **Frontend**: "Add Menu" button
- **Example**: `create_admin_menus`

### 4. **update** - Edit Existing Records
- **Replaces**: Old "edit" permission  
- **Purpose**: Sửa record có sẵn
- **Routes**: `PUT /admin/menus/:id`
- **Frontend**: Edit buttons trong table actions
- **Example**: `update_admin_menus`

### 5. **delete** - Remove Records
- **Purpose**: Xóa records
- **Routes**: `DELETE /admin/menus/:id`
- **Frontend**: Delete buttons trong table actions
- **Example**: `delete_admin_menus`

## 🔄 Migration Changes

### Database Updates
```sql
-- OLD ACTIONS (removed)
read, add, edit, browse

-- NEW ACTIONS (current)
browse, read, create, update, delete
```

### Permission Names Updated
```sql
-- For admin_menus table:
browse_admin_menus    (View list)
read_admin_menus      (View details)
create_admin_menus    (Add new)
update_admin_menus    (Edit existing)  
delete_admin_menus    (Remove)
```

### Route Protection Updated
```javascript
// OLD
router.get('/menus', checkPermission('read', 'admin_menus'), ...);

// NEW  
router.get('/menus', checkPermission('browse', 'admin_menus'), ...);
router.get('/menus/data', checkPermission('browse', 'admin_menus'), ...);
router.get('/menus/:id', checkPermission('read', 'admin_menus'), ...);
router.post('/menus', checkPermission('create', 'admin_menus'), ...);
router.put('/menus/:id', checkPermission('update', 'admin_menus'), ...);
router.delete('/menus/:id', checkPermission('delete', 'admin_menus'), ...);
```

## 💬 Flash Message System

### No Browse Permission Behavior
```javascript
// Controller: menuController.index()
if (!userPermissions.canBrowse) {
  req.flash('error', 'Bạn không có quyền xem danh sách Admin Menus');
  return res.redirect('/');
}
```

### Flash Message Display
```html
<!-- layout.ejs after header -->
<div class="container-fluid mt-3">
  <% if (messages.error) { %>
    <div class="alert alert-danger alert-dismissible fade show">
      <i class="fas fa-exclamation-triangle me-2"></i>
      <%= messages.error %>
      <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
  <% } %>
</div>
```

## 🎮 User Experience Changes

### Browse Permission Removed
- **OLD**: Lock icon + permission message in table
- **NEW**: Flash message on home page + redirect
- **Message**: "Bạn không có quyền xem danh sách [Table Display Name]"

### Granular UI Control
```javascript
// Frontend permission object
const userPermissions = {
  canBrowse: true/false,   // List access
  canRead: true/false,     // View details button
  canCreate: true/false,   // Add button
  canUpdate: true/false,   // Edit buttons  
  canDelete: true/false    // Delete buttons
};
```

## 🧪 Test Scenarios

### Scenario A: Remove `browse_admin_menus`
- **Access**: `/admin/menus`
- **Expected**: Flash message + redirect to `/`
- **Message**: "Bạn không có quyền xem danh sách Admin Menus"

### Scenario B: Remove `read_admin_menus`
- **Access**: Can browse list
- **Expected**: No "View Details" button in table actions
- **Effect**: Can see list but not view individual records

### Scenario C: Remove `create_admin_menus`  
- **Access**: Can browse/read
- **Expected**: "Add Menu" button hidden
- **Effect**: Can view data but not create new

### Scenario D: Remove `update_admin_menus`
- **Access**: Can browse/read/create
- **Expected**: Edit buttons disappear from table
- **Effect**: Can add new but not modify existing

### Scenario E: Remove `delete_admin_menus`
- **Access**: Can browse/read/create/update  
- **Expected**: Delete buttons disappear from table
- **Effect**: Full access except deletion

## 🔧 Implementation Details

### Controller Updates
```javascript
// New permission checking pattern
const userPermissions = {
  canBrowse: await permissionService.checkUserPermission(req.user.id, null, 'admin_menus', 'browse'),
  canRead: await permissionService.checkUserPermission(req.user.id, null, 'admin_menus', 'read'), 
  canCreate: await permissionService.checkUserPermission(req.user.id, null, 'admin_menus', 'create'),
  canUpdate: await permissionService.checkUserPermission(req.user.id, null, 'admin_menus', 'update'),
  canDelete: await permissionService.checkUserPermission(req.user.id, null, 'admin_menus', 'delete')
};
```

### Frontend Updates
```html
<!-- Create button -->
<% if (userPermissions && userPermissions.canCreate) { %>
  <button class="btn btn-primary">Add Menu</button>
<% } %>
```

```javascript
// Action buttons generation
if (userPermissions.canRead) {
  actions.push('<button class="btn btn-info view-menu">View</button>');
}
if (userPermissions.canUpdate) {
  actions.push('<button class="btn btn-warning edit-menu">Edit</button>');  
}
if (userPermissions.canDelete) {
  actions.push('<button class="btn btn-danger delete-menu">Delete</button>');
}
```

### API Error Responses
```javascript
// For browse permission
if (!canBrowse) {
  return res.status(403).json({
    success: false,
    message: 'Bạn không có quyền xem danh sách Admin Menus'
  });
}
```

## 📊 Database Structure

### Permissions Table
```sql
SELECT name, action, table_name FROM permissions WHERE table_name = 'admin_menus';

browse_admin_menus    browse    admin_menus
read_admin_menus      read      admin_menus  
create_admin_menus    create    admin_menus
update_admin_menus    update    admin_menus
delete_admin_menus    delete    admin_menus
```

### Role Assignments
```sql
-- Admin role has all 5 permissions by default
SELECT COUNT(*) FROM role_permissions rp
JOIN permissions p ON rp.permission_id = p.id  
WHERE rp.role_id = 1 AND p.table_name = 'admin_menus';
-- Result: 5
```

## 🎯 Benefits

1. **🔍 Granular Control**: 5 distinct actions for precise permissions
2. **👀 Clear Separation**: Browse (lists) vs Read (details)  
3. **💬 Better UX**: Flash messages instead of lock icons
4. **🏠 Home Redirect**: Clean user experience for no access
5. **🎮 Dynamic UI**: Buttons show/hide based on permissions
6. **📱 Consistent**: Same pattern for all tables
7. **🛡️ Multi-layer**: Route + Controller + Frontend protection

## 🚀 Usage Examples

### For Developers
```javascript
// Route protection
router.get('/resource', checkPermission('browse', 'table_name'), controller.index);

// Controller permission check  
const canBrowse = await permissionService.checkUserPermission(userId, null, 'table_name', 'browse');

// Frontend conditional rendering
<% if (userPermissions && userPermissions.canCreate) { %>
  <!-- Show create button -->
<% } %>
```

### For Admins
1. Go to `/admin/roles/[role_id]/permissions`
2. Grant/revoke specific actions:
   - `browse`: Access to lists
   - `read`: View record details
   - `create`: Add new records
   - `update`: Modify existing
   - `delete`: Remove records

## ✅ Final Status

- **✅ 5-Action Model**: browse, read, create, update, delete
- **✅ Flash Messages**: User-friendly notifications
- **✅ Home Redirect**: Clean no-access experience  
- **✅ Granular UI Control**: Permission-based buttons
- **✅ Table Consistency**: Same pattern across all tables
- **✅ Multi-layer Security**: Route + Controller + Frontend
- **✅ Admin Role**: Has all permissions by default

System is **production-ready** with enterprise-level permission granularity and excellent user experience! 🎉 