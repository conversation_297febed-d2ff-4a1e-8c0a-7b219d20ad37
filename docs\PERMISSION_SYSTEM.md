# Permission System Documentation

## Tổng quan

Hệ thống quyền của ứng dụng được thiết kế theo mô hình <PERSON>, cung cấp kiểm soát truy cập chi tiết cho tất cả các tính năng của admin panel.

## Kiến trúc hệ thống

### 1. <PERSON><PERSON><PERSON> trúc Database

```
user (người dùng)
├── role_user (liên kết user-role)
│   └── role (vai trò)
│       └── role_permissions (liên kết role-permission)
│           └── permissions (quyền)
```

### 2. <PERSON><PERSON><PERSON> bảng ch<PERSON>h

- **`user`**: Thông tin người dùng
- **`role`**: <PERSON><PERSON><PERSON><PERSON> (Admin, Manager, Editor, User)
- **`role_user`**: Liên kết nhiều-nhiều giữa user và role
- **`permissions`**: <PERSON><PERSON> sách tất cả quyền trong hệ thống
- **`role_permissions`**: <PERSON><PERSON><PERSON> kết nhiều-nhiều giữa role và permission

## Hệ thống 5 quyền c<PERSON> bản

Mỗi bảng trong hệ thống có 5 quyền cơ bản:

1. **Browse** (`browse_tablename`): Xem danh sách records
2. **Read** (`read_tablename`): Xem chi tiết một record
3. **Edit** (`edit_tablename`): Chỉnh sửa record
4. **Add** (`add_tablename`): Tạo record mới
5. **Delete** (`delete_tablename`): Xóa record

## Cài đặt và Khởi tạo

### 1. Chạy Database Setup

```bash
node scripts/run-database-setup.js
```

Script này sẽ:
- Tạo tất cả bảng cần thiết
- Tạo dữ liệu mẫu (users, roles, permissions)
- Thiết lập quyền mặc định

### 2. Test hệ thống

```bash
node scripts/test-permission-system.js
```

### 3. Tài khoản mặc định

| Email | Password | Role | Mô tả |
|-------|----------|------|-------|
| <EMAIL> | password123 | Admin | Toàn quyền |
| <EMAIL> | password123 | Manager | Quyền quản lý hạn chế |
| <EMAIL> | password123 | Editor | Quyền chỉnh sửa cơ bản |
| <EMAIL> | password123 | User | Không có quyền admin |

## Sử dụng trong Code

### 1. Permission Service

```javascript
const permissionService = require('../services/permissionService');

// Kiểm tra quyền của user
const hasPermission = await permissionService.checkUserPermission(
  userId, 
  null, 
  'user', 
  'edit'
);

// Lấy tất cả quyền của user
const permissions = await permissionService.getUserPermissions(userId);

// Kiểm tra user có phải Admin không
const isAdmin = await permissionService.isAdmin(userId);

// Tạo quyền cho bảng mới
await permissionService.createPermissionsForTable('products', 'Products');
```

### 2. Permission Middleware

```javascript
const { checkPermission, requireAdmin } = require('../middleware/permissionMiddleware');

// Kiểm tra quyền cụ thể
router.get('/users', checkPermission('browse', 'user'), userController.index);
router.post('/users', checkPermission('add', 'user'), userController.store);
router.put('/users/:id', checkPermission('edit', 'user'), userController.update);
router.delete('/users/:id', checkPermission('delete', 'user'), userController.destroy);

// Chỉ Admin
router.post('/sync-tables', requireAdmin(), adminController.syncTables);
```

### 3. Trong Controller

```javascript
// Kiểm tra quyền trong controller
async function updateUser(req, res) {
  // Middleware đã kiểm tra quyền, có thể thực hiện logic
  const userId = req.params.id;
  // ... update logic
}

// Lấy thông tin quyền của user hiện tại
async function getMyPermissions(req, res) {
  const permissions = await permissionService.getUserPermissions(req.user.id);
  res.json({ permissions });
}
```

## Quản lý quyền qua Web Interface

### 1. Truy cập Permission Management

- URL: `/admin/permissions`
- Yêu cầu quyền: `browse_permissions`

### 2. Quản lý quyền của Role

- URL: `/admin/permissions/role/{roleId}/permissions`
- Yêu cầu quyền: `edit_role`

### 3. Tính năng chính

- **Xem danh sách permissions**: Lọc theo table, action
- **Tạo permission mới**: Cho các module tùy chỉnh
- **Sync permissions**: Tự động tạo quyền cho tất cả bảng
- **Quản lý quyền role**: Gán/thu hồi quyền cho role

## Tự động tạo quyền

Khi tạo bảng mới qua Admin Panel, hệ thống sẽ tự động:

1. Tạo 5 quyền cơ bản cho bảng
2. Gán tất cả quyền cho role Admin
3. Cập nhật cache permissions

```javascript
// Trong adminService.js
async createAdminTableMetadataOnly(data) {
  // ... tạo bảng metadata
  
  // Tự động tạo permissions
  const permissionService = require('./permissionService');
  await permissionService.createPermissionsForTable(
    tableData.name, 
    tableData.display_name
  );
}
```

## Best Practices

### 1. Naming Convention

- Permission names: `{action}_{table_name}`
- Ví dụ: `browse_users`, `edit_products`, `delete_orders`

### 2. Role Hierarchy

```
Admin (toàn quyền)
├── Manager (quản lý users, limited admin)
├── Editor (chỉnh sửa content)
└── User (không có quyền admin)
```

### 3. Security Guidelines

- Luôn kiểm tra quyền ở middleware level
- Không tin tưởng client-side permission checks
- Admin role bypass tất cả permission checks
- Log các hành động quan trọng

### 4. Performance

- Permission service có cache tích hợp
- Cache timeout: 5 phút
- Clear cache khi có thay đổi permissions

## Troubleshooting

### 1. User không có quyền truy cập

```javascript
// Kiểm tra user có role không
const userRoles = await db.query(`
  SELECT r.name 
  FROM role r 
  INNER JOIN role_user ru ON r.id = ru.role_id 
  WHERE ru.user_id = ?
`, [userId]);

// Kiểm tra role có permissions không
const rolePermissions = await permissionService.getRolePermissions(roleId);
```

### 2. Permission không được tạo tự động

```javascript
// Sync permissions manually
await permissionService.syncAllTablePermissions();
```

### 3. Cache issues

```javascript
// Clear permission cache
permissionService.clearCache();
```

## API Endpoints

### Permission Management

- `GET /admin/permissions` - Danh sách permissions
- `POST /admin/permissions` - Tạo permission mới
- `PUT /admin/permissions/:id` - Cập nhật permission
- `DELETE /admin/permissions/:id` - Xóa permission
- `POST /admin/permissions/sync` - Sync permissions

### Role Permission Management

- `GET /admin/permissions/role/:roleId/permissions` - Quản lý quyền role
- `POST /admin/permissions/role/:roleId/permissions` - Cập nhật quyền role

### User Permissions

- `GET /admin/api/my-permissions` - Lấy quyền của user hiện tại

## Mở rộng hệ thống

### 1. Thêm quyền tùy chỉnh

```javascript
await permissionService.createPermission({
  name: 'export_reports',
  display_name: 'Export Reports',
  description: 'Quyền xuất báo cáo',
  table_name: 'reports',
  action: 'export'
});
```

### 2. Tạo middleware tùy chỉnh

```javascript
const checkExportPermission = checkCustomPermission('export_reports');
router.get('/reports/export', checkExportPermission, reportController.export);
```

### 3. Permission groups

Có thể nhóm permissions theo module:

```javascript
const modulePermissions = [
  'browse_products',
  'edit_products', 
  'add_products',
  'delete_products'
];

router.use('/products', checkAnyPermission(modulePermissions));
```
