<style>
  .form-card {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    border: 1px solid #e3f2fd;
  }
  
  .form-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px 15px 0 0;
    padding: 1.5rem;
    margin: -2rem -2rem 2rem -2rem;
  }
  
  .btn-group-custom {
    gap: 10px;
  }
  
  .required-field::after {
    content: " *";
    color: #dc3545;
  }
  
  .form-floating .form-control:focus ~ label,
  .form-floating .form-control:not(:placeholder-shown) ~ label {
    color: #667eea;
  }
</style>

<div class="container-fluid px-4">
  <div class="row justify-content-center">
    <div class="col-lg-8">
      <!-- Form Card -->
      <div class="form-card">
        <!-- Header -->
        <div class="form-header">
          <h3 class="mb-0">
            <i class="fas fa-user-tag me-2"></i>
            <%= role ? 'Chỉnh sửa Role' : 'Tạo Role Mới' %>
          </h3>
          <p class="mb-0 mt-2">
            <%= role ? 'Cập nhật thông tin role trong hệ thống' : 'Thêm role mới vào hệ thống' %>
          </p>
        </div>

        <!-- Form -->
        <form id="roleForm" onsubmit="return handleSubmit(event)">
          <div class="row">
            <!-- Role Name -->
            <div class="col-12 mb-4">
              <div class="form-floating">
                <input type="text" 
                       class="form-control" 
                       id="name" 
                       name="name"
                       placeholder="Nhập tên role..."
                       value="<%= role ? role.name : '' %>"
                       required>
                <label for="name" class="required-field">Tên Role</label>
              </div>
              <div class="form-text">
                <i class="fas fa-info-circle me-1"></i>
                Tên role phải là duy nhất trong hệ thống (ví dụ: admin, editor, viewer)
              </div>
              <div class="invalid-feedback" id="name-error"></div>
            </div>

            <!-- Info Card -->
            <div class="col-12 mb-4">
              <div class="alert alert-info">
                <h6 class="alert-heading">
                  <i class="fas fa-lightbulb me-2"></i>Lưu ý về Roles
                </h6>
                <ul class="mb-0">
                  <li>Role là vai trò để phân quyền cho người dùng</li>
                  <li>Sau khi tạo role, bạn có thể gán permissions cho role này</li>
                  <li>Một user có thể có nhiều roles khác nhau</li>
                  <li>Tên role nên ngắn gọn, dễ hiểu (ví dụ: admin, editor, viewer)</li>
                </ul>
              </div>
            </div>
          </div>

          <!-- Action Buttons -->
          <div class="d-flex justify-content-between">
            <div class="btn-group-custom d-flex">
              <a href="/admin/roles" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-1"></i>Quay lại
              </a>
              
              <% if (role) { %>
                <a href="/admin/roles/<%= role.id %>" class="btn btn-outline-info">
                  <i class="fas fa-eye me-1"></i>Xem chi tiết
                </a>
                <a href="/admin/permissions/roles/<%= role.id %>" class="btn btn-outline-success">
                  <i class="fas fa-shield-alt me-1"></i>Quản lý quyền
                </a>
              <% } %>
            </div>

            <div class="btn-group-custom d-flex">
              <button type="button" class="btn btn-secondary" onclick="resetForm()">
                <i class="fas fa-undo me-1"></i>Reset
              </button>
              
              <button type="submit" class="btn btn-primary" id="submitBtn">
                <i class="fas fa-save me-1"></i>
                <%= role ? 'Cập nhật Role' : 'Tạo Role' %>
              </button>
            </div>
          </div>
        </form>
      </div>

      <!-- Additional Info -->
      <% if (role) { %>
        <div class="form-card mt-4">
          <h5 class="mb-3">
            <i class="fas fa-info-circle text-info me-2"></i>
            Thông tin bổ sung
          </h5>
          
          <div class="row">
            <div class="col-md-6">
              <div class="card border-primary">
                <div class="card-body text-center">
                  <i class="fas fa-shield-alt text-primary mb-2" style="font-size: 2rem;"></i>
                  <h6 class="card-title">Permissions</h6>
                  <p class="card-text">
                    <a href="/admin/permissions/roles/<%= role.id %>" class="btn btn-outline-primary btn-sm">
                      Quản lý quyền
                    </a>
                  </p>
                </div>
              </div>
            </div>
            
            <div class="col-md-6">
              <div class="card border-success">
                <div class="card-body text-center">
                  <i class="fas fa-users text-success mb-2" style="font-size: 2rem;"></i>
                  <h6 class="card-title">Users</h6>
                  <p class="card-text">
                    <a href="/admin/roles/<%= role.id %>" class="btn btn-outline-success btn-sm">
                      Xem users
                    </a>
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      <% } %>
    </div>
  </div>
</div>

<script>
const isEdit = <%= role ? 'true' : 'false' %>;
const roleId = <%= role ? role.id : 'null' %>;

// Handle form submission
async function handleSubmit(event) {
  event.preventDefault();
  
  const submitBtn = document.getElementById('submitBtn');
  const originalText = submitBtn.innerHTML;
  
  try {
    // Show loading
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Đang xử lý...';
    
    // Clear previous errors
    clearErrors();
    
    // Get form data
    const formData = new FormData(document.getElementById('roleForm'));
    const data = Object.fromEntries(formData);
    
    // Validate
    if (!data.name.trim()) {
      showFieldError('name', 'Tên role là bắt buộc');
      return false;
    }
    
    if (data.name.length < 2) {
      showFieldError('name', 'Tên role phải có ít nhất 2 ký tự');
      return false;
    }
    
    if (data.name.length > 50) {
      showFieldError('name', 'Tên role không được vượt quá 50 ký tự');
      return false;
    }
    
    // Submit
    const url = isEdit ? `/admin/roles/${roleId}` : '/admin/roles';
    const method = isEdit ? 'PUT' : 'POST';
    
    const response = await fetch(url, {
      method: method,
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data)
    });
    
    const result = await response.json();
    
    if (result.success) {
      showAlert('success', result.message);
      
      // Redirect after success
      setTimeout(() => {
        if (isEdit) {
          window.location.href = `/admin/roles/${roleId}`;
        } else {
          window.location.href = `/admin/roles/${result.data.id}`;
        }
      }, 1500);
    } else {
      if (result.message.includes('đã tồn tại')) {
        showFieldError('name', result.message);
      } else {
        showAlert('danger', result.message);
      }
    }
    
  } catch (error) {
    console.error('Error:', error);
    showAlert('danger', 'Có lỗi xảy ra khi xử lý yêu cầu');
  } finally {
    // Restore button
    submitBtn.disabled = false;
    submitBtn.innerHTML = originalText;
  }
  
  return false;
}

// Reset form
function resetForm() {
  if (isEdit) {
    // For edit form, restore original values
    location.reload();
  } else {
    // For create form, clear all fields
    document.getElementById('roleForm').reset();
    clearErrors();
  }
}

// Show field error
function showFieldError(fieldName, message) {
  const field = document.getElementById(fieldName);
  const errorDiv = document.getElementById(`${fieldName}-error`);
  
  field.classList.add('is-invalid');
  errorDiv.textContent = message;
}

// Clear all errors
function clearErrors() {
  document.querySelectorAll('.is-invalid').forEach(field => {
    field.classList.remove('is-invalid');
  });
  
  document.querySelectorAll('.invalid-feedback').forEach(errorDiv => {
    errorDiv.textContent = '';
  });
}

// Show alert
function showAlert(type, message) {
  const alertDiv = document.createElement('div');
  alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
  alertDiv.style.top = '20px';
  alertDiv.style.right = '20px';
  alertDiv.style.zIndex = '9999';
  alertDiv.style.minWidth = '300px';
  alertDiv.innerHTML = `
    <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-triangle'} me-2"></i>
    ${message}
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
  `;
  document.body.appendChild(alertDiv);
  
  setTimeout(() => {
    if (alertDiv.parentNode) {
      alertDiv.parentNode.removeChild(alertDiv);
    }
  }, 5000);
}

// Real-time validation
document.getElementById('name').addEventListener('input', function() {
  const value = this.value.trim();
  
  if (value && value.length >= 2) {
    this.classList.remove('is-invalid');
    document.getElementById('name-error').textContent = '';
  }
});
</script> 