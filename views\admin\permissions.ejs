<style>
  .permission-badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
  }
  .permission-action {
    display: inline-block;
    min-width: 60px;
  }
  .table-actions {
    white-space: nowrap;
  }
  .filter-card {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    padding: 1rem;
    margin-bottom: 1rem;
  }
  .stats-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 0.5rem;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
  }
</style>

<div class="container-fluid">
  <!-- Header -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="d-flex justify-content-between align-items-center">
        <div>
          <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-shield-alt text-primary me-2"></i>
            Quản lý Permissions
          </h1>
          <p class="text-muted mb-0"><PERSON><PERSON><PERSON><PERSON> lý quyền truy cập hệ thống</p>
        </div>
        <div>
          <a href="/admin/permissions/create" class="btn btn-primary">
            <i class="fas fa-plus me-1"></i>Thêm Permission
          </a>
          <button type="button" class="btn btn-info ms-2" onclick="syncPermissions()">
            <i class="fas fa-sync me-1"></i>Đồng bộ
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Stats -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="stats-card">
        <div class="row">
          <div class="col-md-3 text-center">
            <h3 class="mb-1" id="statTotal">-</h3>
            <p class="mb-0">Tổng Permissions</p>
          </div>
          <div class="col-md-3 text-center">
            <h3 class="mb-1" id="statTables">-</h3>
            <p class="mb-0">Bảng có Permissions</p>
          </div>
          <div class="col-md-3 text-center">
            <h3 class="mb-1" id="statActions">-</h3>
            <p class="mb-0">Loại Actions</p>
          </div>
          <div class="col-md-3 text-center">
            <h3 class="mb-1" id="statAvg">-</h3>
            <p class="mb-0">Avg per Table</p>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Filters -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="filter-card">
        <div class="row">
          <div class="col-md-4 mb-3">
            <label for="search" class="form-label">Tìm kiếm</label>
            <input type="text" class="form-control" id="search" name="search" 
                   placeholder="Tìm theo tên, mô tả...">
          </div>
          <div class="col-md-3 mb-3">
            <label for="table_name" class="form-label">Bảng</label>
            <select class="form-select" id="table_name" name="table_name">
              <option value="">Tất cả bảng</option>
            </select>
          </div>
          <div class="col-md-3 mb-3">
            <label for="action" class="form-label">Action</label>
            <select class="form-select" id="action" name="action">
              <option value="">Tất cả actions</option>
            </select>
          </div>
          <div class="col-md-2 mb-3">
            <label class="form-label">&nbsp;</label>
            <div class="d-grid">
              <button type="button" class="btn btn-secondary" onclick="clearFilters()">
                <i class="fas fa-times me-1"></i>Clear
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Permissions Table -->
  <div class="row">
    <div class="col-12">
      <div class="card shadow">
        <div class="card-header py-3">
          <h6 class="m-0 font-weight-bold text-primary">
            Danh sách Permissions
            <span class="badge bg-secondary ms-2" id="totalCount">-</span>
          </h6>
        </div>
        <div class="card-body">
          <div class="table-responsive">
            <table id="permissions-table" class="table table-bordered table-hover" style="width:100%">
              <thead class="table-light">
                <tr>
                  <th>ID</th>
                  <th>Tên</th>
                  <th>Tên hiển thị</th>
                  <th>Bảng</th>
                  <th>Action</th>
                  <th>Mô tả</th>
                  <th>Thao tác</th>
                </tr>
              </thead>
              <tbody>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Xác nhận xóa</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
      </div>
      <div class="modal-body">
        <p>Bạn có chắc chắn muốn xóa permission <strong id="deletePermissionName"></strong>?</p>
        <p class="text-danger"><small>Hành động này không thể hoàn tác.</small></p>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Hủy</button>
        <button type="button" class="btn btn-danger" id="confirmDelete">Xóa</button>
      </div>
    </div>
  </div>
</div>

<!-- DataTables CSS & JS -->
<script src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.13.7/js/dataTables.bootstrap5.min.js"></script>
<link rel="stylesheet" href="https://cdn.datatables.net/1.13.7/css/dataTables.bootstrap5.min.css">

<script>
let permissionsTable;

// Helper function to get action badge class
function getActionBadgeClass(action) {
  switch(action) {
    case 'read': return 'info';
    case 'edit': return 'warning';
    case 'add': return 'success';
    case 'delete': return 'danger';
    default: return 'secondary';
  }
}

// Initialize page
$(document).ready(function() {
  // Initialize DataTable
  permissionsTable = $('#permissions-table').DataTable({
    processing: true,
    serverSide: true,
    ajax: {
      url: '/admin/permissions/api/data',
      type: 'GET',
      data: function(d) {
        // Add custom filters
        d.search_custom = $('#search').val();
        d.table_name = $('#table_name').val();
        d.action = $('#action').val();
      }
    },
    columns: [
      {
        data: 'id',
        name: 'id',
        title: 'ID',
        width: '60px'
      },
      {
        data: 'name',
        name: 'name',
        title: 'Tên',
        render: function(data, type, row) {
          return `<code class="text-primary">${data}</code>`;
        }
      },
      {
        data: 'display_name',
        name: 'display_name',
        title: 'Tên hiển thị',
        render: function(data, type, row) {
          return `<strong>${data}</strong>`;
        }
      },
      {
        data: 'table_name',
        name: 'table_name',
        title: 'Bảng',
        render: function(data, type, row) {
          if (data) {
            return `<span class="badge bg-info">${data}</span>`;
          } else {
            return `<span class="text-muted">System</span>`;
          }
        }
      },
      {
        data: 'action',
        name: 'action',
        title: 'Action',
        render: function(data, type, row) {
          const badgeClass = getActionBadgeClass(data);
          return `<span class="badge bg-${badgeClass} permission-action">${data}</span>`;
        }
      },
      {
        data: 'description',
        name: 'description',
        title: 'Mô tả',
        render: function(data, type, row) {
          return `<small class="text-muted">${data || 'Không có mô tả'}</small>`;
        }
      },
      {
        data: 'actions',
        name: 'actions',
        title: 'Thao tác',
        orderable: false,
        searchable: false,
        render: function(data, type, row) {
          return `
            <div class="btn-group btn-group-sm" role="group">
              <a href="/admin/permissions/${row.id}" 
                 class="btn btn-outline-info" title="Xem chi tiết">
                <i class="fas fa-eye"></i>
              </a>
              <a href="/admin/permissions/${row.id}/edit" 
                 class="btn btn-outline-warning" title="Chỉnh sửa">
                <i class="fas fa-edit"></i>
              </a>
              <button type="button" class="btn btn-outline-danger" 
                      onclick="deletePermission(${row.id}, '${row.name}')" 
                      title="Xóa">
                <i class="fas fa-trash"></i>
              </button>
            </div>
          `;
        }
      }
    ],
    order: [[0, 'desc']],
    pageLength: 25,
    language: {
      processing: "Đang xử lý...",
      search: "Tìm kiếm:",
      lengthMenu: "Hiển thị _MENU_ mục",
      info: "Hiển thị _START_ đến _END_ trong tổng số _TOTAL_ mục",
      infoEmpty: "Hiển thị 0 đến 0 trong tổng số 0 mục",
      infoFiltered: "(lọc từ _MAX_ mục)",
      paginate: {
        first: "Đầu",
        last: "Cuối",
        next: "Sau",
        previous: "Trước"
      },
      emptyTable: "Không có dữ liệu"
    },
    drawCallback: function(settings) {
      // Update stats after each draw
      const info = this.api().page.info();
      document.getElementById('totalCount').textContent = info.recordsTotal;
      updateStats();
    }
  });

  // Load filter options
  loadFilterOptions();

  // Setup filter event listeners
  $('#search').on('keyup', debounce(function() {
    permissionsTable.ajax.reload();
  }, 500));

  $('#table_name, #action').on('change', function() {
    permissionsTable.ajax.reload();
  });
});

// Load filter options from API
async function loadFilterOptions() {
  try {
    const response = await fetch('/admin/permissions/api/filter-options');
    const result = await response.json();
    
    if (result.success) {
      // Populate table options
      const tableSelect = document.getElementById('table_name');
      result.tables.forEach(table => {
        const option = document.createElement('option');
        option.value = table.table_name;
        option.textContent = table.table_name;
        tableSelect.appendChild(option);
      });

      // Populate action options
      const actionSelect = document.getElementById('action');
      result.actions.forEach(action => {
        const option = document.createElement('option');
        option.value = action.action;
        option.textContent = action.action;
        actionSelect.appendChild(option);
      });

      // Update initial stats
      document.getElementById('statTables').textContent = result.tables.length;
      document.getElementById('statActions').textContent = result.actions.length;
    }
  } catch (error) {
    console.error('Error loading filter options:', error);
  }
}

// Update stats
async function updateStats() {
  try {
    const info = permissionsTable.page.info();
    const total = info.recordsTotal;
    
    document.getElementById('statTotal').textContent = total;
    
    // Calculate average (assuming 5 permissions per table)
    const tablesCount = parseInt(document.getElementById('statTables').textContent) || 1;
    const avg = Math.ceil(total / tablesCount);
    document.getElementById('statAvg').textContent = avg;
  } catch (error) {
    console.error('Error updating stats:', error);
  }
}

// Clear filters
function clearFilters() {
  document.getElementById('search').value = '';
  document.getElementById('table_name').value = '';
  document.getElementById('action').value = '';
  permissionsTable.ajax.reload();
}

// Debounce function
function debounce(func, wait) {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}

// Delete permission function
let deletePermissionId = null;

function deletePermission(id, name) {
  deletePermissionId = id;
  document.getElementById('deletePermissionName').textContent = name;
  $('#deleteModal').modal('show');
  // new bootstrap.Modal(document.getElementById('deleteModal')).show();
}

document.getElementById('confirmDelete').addEventListener('click', function() {
  if (deletePermissionId) {
    fetch(`/admin/permissions/${deletePermissionId}`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
      }
    })
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        showAlert('success', data.message);
        permissionsTable.ajax.reload();
      } else {
        showAlert('danger', data.message);
      }
    })
    .catch(error => {
      showAlert('danger', 'Có lỗi xảy ra khi xóa permission');
    });
    $('#deleteModal').modal('hide');
    //bootstrap.Modal.getInstance(document.getElementById('deleteModal')).hide();
  }
});

// Sync permissions function
function syncPermissions() {
  const btn = event.target.closest('button');
  const originalText = btn.innerHTML;
  btn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Đang đồng bộ...';
  btn.disabled = true;

  fetch('/admin/permissions/sync', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    }
  })
  .then(response => response.json())
  .then(data => {
    if (data.success) {
      showAlert('success', data.message);
      permissionsTable.ajax.reload();
      loadFilterOptions(); // Refresh filter options
    } else {
      showAlert('danger', data.message);
    }
  })
  .catch(error => {
    showAlert('danger', 'Có lỗi xảy ra khi đồng bộ permissions');
  })
  .finally(() => {
    btn.innerHTML = originalText;
    btn.disabled = false;
  });
}

// Alert function
function showAlert(type, message) {
  const alertDiv = document.createElement('div');
  alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
  alertDiv.style.top = '20px';
  alertDiv.style.right = '20px';
  alertDiv.style.zIndex = '9999';
  alertDiv.innerHTML = `
    ${message}
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
  `;
  document.body.appendChild(alertDiv);
  
  setTimeout(() => {
    if (alertDiv.parentNode) {
      alertDiv.parentNode.removeChild(alertDiv);
    }
  }, 5000);
}
</script> 