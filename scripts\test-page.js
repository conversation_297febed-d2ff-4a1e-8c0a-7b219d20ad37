// Test script để kiểm tra trang table structure
const http = require('http');

function testPage() {
  const options = {
    hostname: 'localhost',
    port: 3000,
    path: '/admin/tables/1/structure',
    method: 'GET',
    headers: {
      'User-Agent': 'Node.js Test'
    }
  };

  const req = http.request(options, (res) => {
    console.log(`Status: ${res.statusCode}`);
    console.log(`Headers:`, res.headers);

    let data = '';
    res.on('data', (chunk) => {
      data += chunk;
    });

    res.on('end', () => {
      if (res.statusCode === 200) {
        console.log('✅ Page loaded successfully!');
        console.log('Response length:', data.length);
        
        // Check for JavaScript errors
        if (data.includes('SyntaxError') || data.includes('Unexpected token')) {
          console.log('❌ JavaScript syntax error detected in response');
          const lines = data.split('\n');
          lines.forEach((line, index) => {
            if (line.includes('SyntaxError') || line.includes('Unexpected token')) {
              console.log(`Error at line ${index + 1}: ${line.trim()}`);
            }
          });
        } else {
          console.log('✅ No JavaScript syntax errors detected');
        }

        // Check for specific content
        if (data.includes('Table Structure:')) {
          console.log('✅ Table structure content found');
        } else {
          console.log('❌ Table structure content not found');
        }

        if (data.includes('Add Column')) {
          console.log('✅ Add Column button found');
        } else {
          console.log('❌ Add Column button not found');
        }

      } else {
        console.log('❌ Page failed to load');
        console.log('Response:', data.substring(0, 500));
      }
    });
  });

  req.on('error', (err) => {
    console.error('❌ Request failed:', err.message);
  });

  req.setTimeout(5000, () => {
    console.error('❌ Request timeout');
    req.destroy();
  });

  req.end();
}

// Chạy test
console.log('🧪 Testing table structure page...');
testPage();
