<style>
  .detail-card {
    border-radius: 0.5rem;
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
  }
  .detail-header {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
    color: white;
    border-radius: 0.5rem 0.5rem 0 0;
    padding: 1.5rem;
  }
  .info-row {
    padding: 1rem 0;
    border-bottom: 1px solid #dee2e6;
  }
  .info-row:last-child {
    border-bottom: none;
  }
  .info-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.25rem;
  }
  .info-value {
    color: #6c757d;
  }
  .permission-code {
    font-family: 'Courier New', monospace;
    background: #f8f9fa;
    padding: 0.5rem;
    border-radius: 0.25rem;
    border: 1px solid #dee2e6;
  }
  .roles-list {
    max-height: 300px;
    overflow-y: auto;
  }
  .role-item {
    padding: 0.75rem;
    border: 1px solid #dee2e6;
    border-radius: 0.25rem;
    margin-bottom: 0.5rem;
    background: white;
  }
  .role-item:hover {
    background: #f8f9fa;
  }
</style>

<div class="container-fluid">
  <!-- Header -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="d-flex justify-content-between align-items-center">
        <div>
          <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-shield-alt text-info me-2"></i>
            Chi tiết Permission
          </h1>
          <p class="text-muted mb-0">Thông tin chi tiết về permission và các role được gán</p>
        </div>
        <div>
          <a href="/admin/permissions" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-1"></i>Quay lại
          </a>
          <a href="/admin/permissions/<%= permission.id %>/edit" class="btn btn-warning">
            <i class="fas fa-edit me-1"></i>Chỉnh sửa
          </a>
        </div>
      </div>
    </div>
  </div>

  <!-- Permission Details -->
  <div class="row">
    <div class="col-lg-8">
      <div class="card detail-card">
        <div class="detail-header">
          <h5 class="mb-0">
            <i class="fas fa-info-circle me-2"></i>
            Thông tin Permission
          </h5>
        </div>
        <div class="card-body">
          <div class="info-row">
            <div class="info-label">
              <i class="fas fa-tag text-primary me-1"></i>
              Tên Permission
            </div>
            <div class="info-value permission-code">
              <%= permission.name %>
            </div>
          </div>

          <div class="info-row">
            <div class="info-label">
              <i class="fas fa-eye text-info me-1"></i>
              Tên hiển thị
            </div>
            <div class="info-value">
              <strong><%= permission.display_name %></strong>
            </div>
          </div>

          <div class="info-row">
            <div class="info-label">
              <i class="fas fa-table text-success me-1"></i>
              Bảng
            </div>
            <div class="info-value">
              <% if (permission.table_name) { %>
                <span class="badge bg-info"><%= permission.table_name %></span>
              <% } else { %>
                <span class="text-muted">System Permission</span>
              <% } %>
            </div>
          </div>

          <div class="info-row">
            <div class="info-label">
              <i class="fas fa-bolt text-warning me-1"></i>
              Action
            </div>
            <div class="info-value">
              <% 
                let actionClass = 'secondary';
                switch(permission.action) {
                  case 'read': actionClass = 'info'; break;
                  case 'edit': actionClass = 'warning'; break;
                  case 'add': actionClass = 'success'; break;
                  case 'delete': actionClass = 'danger'; break;
                }
              %>
              <span class="badge bg-<%= actionClass %>"><%= permission.action %></span>
            </div>
          </div>

          <div class="info-row">
            <div class="info-label">
              <i class="fas fa-align-left text-secondary me-1"></i>
              Mô tả
            </div>
            <div class="info-value">
              <%= permission.description || 'Không có mô tả' %>
            </div>
          </div>

          <div class="info-row">
            <div class="info-label">
              <i class="fas fa-calendar text-muted me-1"></i>
              Ngày tạo
            </div>
            <div class="info-value">
              <%= new Date(permission.created_at).toLocaleString('vi-VN') %>
            </div>
          </div>

          <div class="info-row">
            <div class="info-label">
              <i class="fas fa-calendar-alt text-muted me-1"></i>
              Ngày cập nhật
            </div>
            <div class="info-value">
              <%= new Date(permission.updated_at).toLocaleString('vi-VN') %>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Roles using this permission -->
    <div class="col-lg-4">
      <div class="card detail-card">
        <div class="card-header bg-success text-white">
          <h6 class="mb-0">
            <i class="fas fa-users me-2"></i>
            Roles sử dụng permission này
            <span class="badge bg-light text-dark ms-2"><%= roles.length %></span>
          </h6>
        </div>
        <div class="card-body">
          <% if (roles.length === 0) { %>
            <div class="text-center py-4">
              <i class="fas fa-users-slash fa-2x text-muted mb-2"></i>
              <p class="text-muted mb-0">Chưa có role nào sử dụng permission này</p>
            </div>
          <% } else { %>
            <div class="roles-list">
              <% roles.forEach(role => { %>
                <div class="role-item">
                  <div class="d-flex justify-content-between align-items-start">
                    <div>
                      <h6 class="mb-1">
                        <i class="fas fa-user-tag text-primary me-1"></i>
                        <%= role.name %>
                      </h6>
                      <p class="text-muted mb-1 small">
                        <%= role.name %>
                      </p>
                      <small class="text-muted">
                        <i class="fas fa-clock me-1"></i>
                        Gán lúc: <%= new Date(role.granted_at).toLocaleString('vi-VN') %>
                      </small>
                    </div>
                    <a href="/admin/permissions/roles/<%= role.id %>" 
                       class="btn btn-sm btn-outline-primary" 
                       title="Quản lý quyền role">
                      <i class="fas fa-cog"></i>
                    </a>
                  </div>
                </div>
              <% }) %>
            </div>
          <% } %>
        </div>
      </div>

      <!-- Actions -->
      <div class="card detail-card mt-3">
        <div class="card-header bg-dark text-white">
          <h6 class="mb-0">
            <i class="fas fa-tools me-2"></i>
            Thao tác
          </h6>
        </div>
        <div class="card-body">
          <div class="d-grid gap-2">
            <a href="/admin/permissions/<%= permission.id %>/edit" class="btn btn-warning">
              <i class="fas fa-edit me-1"></i>Chỉnh sửa Permission
            </a>
            <button type="button" class="btn btn-danger" 
                    onclick="deletePermission(<%= permission.id %>, '<%= permission.name %>')">
              <i class="fas fa-trash me-1"></i>Xóa Permission
            </button>
            <hr>
            <a href="/admin/permissions" class="btn btn-secondary">
              <i class="fas fa-list me-1"></i>Danh sách Permissions
            </a>
            <a href="/admin/permissions/create" class="btn btn-success">
              <i class="fas fa-plus me-1"></i>Tạo Permission mới
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Xác nhận xóa Permission</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
      </div>
      <div class="modal-body">
        <div class="alert alert-warning">
          <i class="fas fa-exclamation-triangle me-2"></i>
          <strong>Cảnh báo!</strong> Bạn có chắc chắn muốn xóa permission này?
        </div>
        <p>Permission: <strong id="deletePermissionName"></strong></p>
        <p class="text-danger">
          <small>
            <i class="fas fa-info-circle me-1"></i>
            Hành động này sẽ:
          </small>
        </p>
        <ul class="text-danger small">
          <li>Xóa hoàn toàn permission khỏi hệ thống</li>
          <li>Thu hồi permission từ tất cả roles đang sử dụng</li>
          <li>Không thể hoàn tác</li>
        </ul>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Hủy</button>
        <button type="button" class="btn btn-danger" id="confirmDelete">
          <i class="fas fa-trash me-1"></i>Xóa Permission
        </button>
      </div>
    </div>
  </div>
</div>

<script>
// Delete permission function
let deletePermissionId = null;

function deletePermission(id, name) {
  deletePermissionId = id;
  document.getElementById('deletePermissionName').textContent = name;
  $('#deleteModal').modal('show');
//   new bootstrap.Modal(document.getElementById('deleteModal')).show();
}

document.getElementById('confirmDelete').addEventListener('click', function() {
  if (deletePermissionId) {
    const btn = this;
    const originalText = btn.innerHTML;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Đang xóa...';
    btn.disabled = true;

    fetch(`/admin/permissions/${deletePermissionId}`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
      }
    })
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        showAlert('success', data.message);
        setTimeout(() => {
          window.location.href = '/admin/permissions';
        }, 1500);
      } else {
        showAlert('danger', data.message);
        btn.innerHTML = originalText;
        btn.disabled = false;
      }
    })
    .catch(error => {
      showAlert('danger', 'Có lỗi xảy ra khi xóa permission');
      btn.innerHTML = originalText;
      btn.disabled = false;
    });
    $('#deleteModal').modal('hide');
    //bootstrap.Modal.getInstance(document.getElementById('deleteModal')).hide();
  }
});

// Alert function
function showAlert(type, message) {
  const alertDiv = document.createElement('div');
  alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
  alertDiv.style.top = '20px';
  alertDiv.style.right = '20px';
  alertDiv.style.zIndex = '9999';
  alertDiv.innerHTML = `
    ${message}
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
  `;
  document.body.appendChild(alertDiv);
  
  setTimeout(() => {
    if (alertDiv.parentNode) {
      alertDiv.parentNode.removeChild(alertDiv);
    }
  }, 5000);
}
</script> 