
<style>
  .permission-matrix {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    padding: 1rem;
  }
  .table-header {
    background: #6c757d;
    color: white;
    font-weight: bold;
    text-align: center;
    padding: 0.75rem;
    border-radius: 0.25rem;
    margin-bottom: 0.5rem;
  }
  .permission-row {
    display: flex;
    align-items: center;
    padding: 0.5rem;
    margin-bottom: 0.25rem;
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 0.25rem;
  }
  .permission-row:hover {
    background: #f1f3f4;
  }
  .permission-checkbox {
    transform: scale(1.2);
    margin-right: 0.5rem;
  }
  .permission-name {
    flex: 1;
    font-weight: 500;
  }
  .permission-action-badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    margin-left: 0.5rem;
  }
  .stats-card {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    border-radius: 0.5rem;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
  }
  .table-actions-header {
    position: sticky;
    top: 0;
    background: #6c757d;
    z-index: 10;
    padding: 0.5rem;
    margin-bottom: 1rem;
    border-radius: 0.25rem;
  }
  .table-actions-header .btn {
    margin: 0 0.25rem;
  }
  .filter-section {
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 0.375rem;
    margin-bottom: 1rem;
  }
</style>

<div class="container-fluid">
  <!-- Header -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="d-flex justify-content-between align-items-center">
        <div>
          <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-user-shield text-success me-2"></i>
            Quản lý quyền - <%= role.name %>
          </h1>
          <p class="text-muted mb-0">Gán và quản lý quyền cho role</p>
        </div>
        <div>
          <a href="/admin/permissions" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-1"></i>Quay lại
          </a>
        </div>
      </div>
    </div>
  </div>

  <!-- Stats -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="stats-card">
        <div class="row">
          <div class="col-md-3 text-center">
            <h3 class="mb-1"><%= stats.total %></h3>
            <p class="mb-0">Tổng Permissions</p>
          </div>
          <div class="col-md-3 text-center">
            <h3 class="mb-1"><%= stats.granted %></h3>
            <p class="mb-0">Đã cấp quyền</p>
          </div>
          <div class="col-md-3 text-center">
            <h3 class="mb-1"><%= stats.denied %></h3>
            <p class="mb-0">Chưa cấp quyền</p>
          </div>
          <div class="col-md-3 text-center">
            <h3 class="mb-1"><%= Math.round((stats.granted / stats.total) * 100) %>%</h3>
            <p class="mb-0">Tỷ lệ cấp quyền</p>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Filters -->
  <div class="filter-section">
    <form method="GET" id="filterForm">
      <div class="row align-items-end">
        <div class="col-md-4">
          <label for="table_name" class="form-label">Lọc theo bảng</label>
          <select class="form-select" id="table_name" name="table_name">
            <option value="">Tất cả bảng</option>
            <% tables.forEach(table => { %>
              <option value="<%= table.table_name %>" 
                      <%= filters.table_name === table.table_name ? 'selected' : '' %>>
                <%= table.table_name %>
              </option>
            <% }) %>
          </select>
        </div>
        <div class="col-md-3">
          <label for="action" class="form-label">Lọc theo action</label>
          <select class="form-select" id="action" name="action">
            <option value="">Tất cả actions</option>
            <% actions.forEach(action => { %>
              <option value="<%= action.action %>" 
                      <%= filters.action === action.action ? 'selected' : '' %>>
                <%= action.action %>
              </option>
            <% }) %>
          </select>
        </div>
        <div class="col-md-5">
          <div class="btn-group w-100" role="group">
            <button type="button" class="btn btn-outline-success" onclick="selectAll()">
              <i class="fas fa-check-square me-1"></i>Chọn tất cả
            </button>
            <button type="button" class="btn btn-outline-warning" onclick="deselectAll()">
              <i class="fas fa-square me-1"></i>Bỏ chọn tất cả
            </button>
            <button type="button" class="btn btn-success" onclick="savePermissions()">
              <i class="fas fa-save me-1"></i>Lưu thay đổi
            </button>
          </div>
        </div>
      </div>
    </form>
  </div>

  <!-- Permission Matrix -->
  <div class="row">
    <div class="col-12">
      <div class="card shadow">
        <div class="card-header py-3">
          <h6 class="m-0 font-weight-bold text-primary">
            Permission Matrix - <%= role.name %>
            <span class="badge bg-success ms-2"><%= stats.granted %></span>
            <span class="badge bg-secondary ms-1"><%= stats.denied %></span>
          </h6>
        </div>
        <div class="card-body">
          <% if (Object.keys(groupedPermissions).length === 0) { %>
            <div class="text-center py-5">
              <i class="fas fa-shield-alt fa-3x text-muted mb-3"></i>
              <p class="text-muted">Không có permission nào phù hợp với bộ lọc hiện tại</p>
            </div>
          <% } else { %>
            <% Object.keys(groupedPermissions).forEach(tableName => { %>
              <div class="permission-matrix mb-4">
                <div class="table-header">
                  <div class="row">
                    <div class="col-md-8">
                      <i class="fas fa-table me-2"></i>
                      <%= tableName === 'null' ? 'System Permissions' : tableName %>
                      <span class="badge bg-light text-dark ms-2">
                        <%= groupedPermissions[tableName].length %> permissions
                      </span>
                    </div>
                    <div class="col-md-4 text-end">
                      <button type="button" class="btn btn-sm btn-outline-light me-1" 
                              onclick="grantTablePermissions('<%= tableName %>')">
                        <i class="fas fa-plus me-1"></i>Cấp tất cả
                      </button>
                      <button type="button" class="btn btn-sm btn-outline-light" 
                              onclick="revokeTablePermissions('<%= tableName %>')">
                        <i class="fas fa-minus me-1"></i>Thu hồi tất cả
                      </button>
                    </div>
                  </div>
                </div>

                <% groupedPermissions[tableName].forEach(permission => { %>
                  <div class="permission-row">
                    <input type="checkbox" 
                           class="form-check-input permission-checkbox" 
                           id="permission_<%= permission.id %>"
                           data-permission-id="<%= permission.id %>"
                           data-table-name="<%= permission.table_name %>"
                           <%= permission.is_granted ? 'checked' : '' %>>
                    
                    <label for="permission_<%= permission.id %>" class="permission-name">
                      <%= permission.display_name %>
                      <br>
                      <small class="text-muted"><%= permission.description %></small>
                    </label>

                    <% 
                      let actionClass = 'secondary';
                      switch(permission.action) {
                        case 'read': actionClass = 'info'; break;
                        case 'edit': actionClass = 'warning'; break;
                        case 'add': actionClass = 'success'; break;
                        case 'delete': actionClass = 'danger'; break;
                      }
                    %>
                    <span class="badge bg-<%= actionClass %> permission-action-badge">
                      <%= permission.action %>
                    </span>
                  </div>
                <% }) %>
              </div>
            <% }) %>
          <% } %>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Loading Modal -->
<div class="modal fade" id="loadingModal" tabindex="-1" data-bs-backdrop="static" data-bs-keyboard="false">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-body text-center py-4">
        <div class="spinner-border text-primary mb-3" role="status">
          <span class="visually-hidden">Loading...</span>
        </div>
        <p class="mb-0">Đang xử lý...</p>
      </div>
    </div>
  </div>
</div>

<script>
let hasChanges = false;
const roleId = <%= role.id %>;

// Auto-submit form when filter changes
document.getElementById('table_name').addEventListener('change', function() {
  document.getElementById('filterForm').submit();
});

document.getElementById('action').addEventListener('change', function() {
  document.getElementById('filterForm').submit();
});

// Track changes
document.querySelectorAll('.permission-checkbox').forEach(checkbox => {
  checkbox.addEventListener('change', function() {
    hasChanges = true;
    updateSaveButtonState();
  });
});

function updateSaveButtonState() {
  const saveBtn = document.querySelector('button[onclick="savePermissions()"]');
  if (hasChanges) {
    saveBtn.classList.remove('btn-success');
    saveBtn.classList.add('btn-warning');
    saveBtn.innerHTML = '<i class="fas fa-exclamation-triangle me-1"></i>Có thay đổi - Lưu ngay';
  } else {
    saveBtn.classList.remove('btn-warning');
    saveBtn.classList.add('btn-success');
    saveBtn.innerHTML = '<i class="fas fa-save me-1"></i>Lưu thay đổi';
  }
}

// Select all permissions
function selectAll() {
  document.querySelectorAll('.permission-checkbox').forEach(checkbox => {
    if (!checkbox.checked) {
      checkbox.checked = true;
      hasChanges = true;
    }
  });
  updateSaveButtonState();
}

// Deselect all permissions
function deselectAll() {
  document.querySelectorAll('.permission-checkbox').forEach(checkbox => {
    if (checkbox.checked) {
      checkbox.checked = false;
      hasChanges = true;
    }
  });
  updateSaveButtonState();
}

// Grant all permissions for a table
function grantTablePermissions(tableName) {
  showLoadingModal();
  
  fetch(`/admin/permissions/roles/${roleId}/grant-table`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ tableName: tableName === 'null' ? null : tableName })
  })
  .then(response => response.json())
  .then(data => {
    hideLoadingModal();
    if (data.success) {
      showAlert('success', data.message);
      setTimeout(() => location.reload(), 1500);
    } else {
      showAlert('danger', data.message);
    }
  })
  .catch(error => {
    hideLoadingModal();
    showAlert('danger', 'Có lỗi xảy ra khi cấp quyền bảng');
  });
}

// Revoke all permissions for a table
function revokeTablePermissions(tableName) {
  showLoadingModal();
  
  fetch(`/admin/permissions/roles/${roleId}/revoke-table`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ tableName: tableName === 'null' ? null : tableName })
  })
  .then(response => response.json())
  .then(data => {
    hideLoadingModal();
    if (data.success) {
      showAlert('success', data.message);
      setTimeout(() => location.reload(), 1500);
    } else {
      showAlert('danger', data.message);
    }
  })
  .catch(error => {
    hideLoadingModal();
    showAlert('danger', 'Có lỗi xảy ra khi thu hồi quyền bảng');
  });
}

// Save permissions
function savePermissions() {
  if (!hasChanges) {
    showAlert('info', 'Không có thay đổi nào để lưu');
    return;
  }

  showLoadingModal();

  const permissionsToGrant = [];
  const permissionsToRevoke = [];

  document.querySelectorAll('.permission-checkbox').forEach(checkbox => {
    const permissionId = checkbox.dataset.permissionId;
    const isChecked = checkbox.checked;
    const wasChecked = checkbox.defaultChecked;

    if (isChecked && !wasChecked) {
      permissionsToGrant.push(permissionId);
    } else if (!isChecked && wasChecked) {
      permissionsToRevoke.push(permissionId);
    }
  });

  const promises = [];

  // Grant permissions
  if (permissionsToGrant.length > 0) {
    promises.push(
      fetch(`/admin/permissions/roles/${roleId}/grant-bulk`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ permissionIds: permissionsToGrant })
      })
    );
  }

  // Revoke permissions
  if (permissionsToRevoke.length > 0) {
    promises.push(
      fetch(`/admin/permissions/roles/${roleId}/revoke-bulk`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ permissionIds: permissionsToRevoke })
      })
    );
  }

  Promise.all(promises)
    .then(responses => Promise.all(responses.map(r => r.json())))
    .then(results => {
      hideLoadingModal();
      
      const errors = results.filter(r => !r.success);
      if (errors.length === 0) {
        showAlert('success', `Đã cập nhật thành công: +${permissionsToGrant.length} quyền, -${permissionsToRevoke.length} quyền`);
        hasChanges = false;
        updateSaveButtonState();
        
        // Update checkbox default states
        document.querySelectorAll('.permission-checkbox').forEach(checkbox => {
          checkbox.defaultChecked = checkbox.checked;
        });
      } else {
        showAlert('danger', 'Có lỗi xảy ra trong quá trình cập nhật quyền');
      }
    })
    .catch(error => {
      hideLoadingModal();
      showAlert('danger', 'Có lỗi xảy ra khi lưu thay đổi');
    });
}

// Modal functions
function showLoadingModal() {
  $('#loadingModal').modal('show');
  //new bootstrap.Modal(document.getElementById('loadingModal')).show();
}

function hideLoadingModal() {
  $('#loadingModal').modal('hide');
}

// Alert function
function showAlert(type, message) {
  const alertDiv = document.createElement('div');
  alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
  alertDiv.style.top = '20px';
  alertDiv.style.right = '20px';
  alertDiv.style.zIndex = '9999';
  alertDiv.innerHTML = `
    ${message}
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
  `;
  document.body.appendChild(alertDiv);
  
  setTimeout(() => {
    if (alertDiv.parentNode) {
      alertDiv.parentNode.removeChild(alertDiv);
    }
  }, 5000);
}

// Warn before leaving if there are unsaved changes
window.addEventListener('beforeunload', function(e) {
  if (hasChanges) {
    e.preventDefault();
    e.returnValue = '';
  }
});
</script> 