const db = require('../config/database');

async function fixColumns() {
  try {
    // Update id column to not be visible in form
    const result = await db.query(`
      UPDATE admincolumn 
      SET is_visible_form = false 
      WHERE name = 'id' AND is_primary = true
    `);
    console.log('Fixed id columns:', result.affectedRows);
    
    // Check role_user columns
    const roleUserTable = await db.queryOne(`
      SELECT * FROM admintable WHERE name = 'role_user'
    `);
    
    if (roleUserTable) {
      const columns = await db.query(`
        SELECT * FROM admincolumn 
        WHERE table_id = ? AND is_visible_form = true 
        ORDER BY order_index ASC
      `, [roleUserTable.id]);
      
      console.log('role_user form columns:');
      columns.forEach(col => {
        console.log(`- ${col.name} (${col.display_name})`);
      });
    }
    
  } catch (error) {
    console.error('Error:', error);
  }
}

fixColumns();
