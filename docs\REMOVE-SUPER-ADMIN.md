# Loại bỏ Super Admin - Thiết lập Hệ thống Role Đơn giản

## Tổng quan

Đã loại bỏ Super Admin khỏi hệ thống và thiết lập hệ thống role đơn giản hơn chỉ với:
- **Admin**: Quyền quản lý toàn bộ hệ thống
- **User**: <PERSON>uy<PERSON><PERSON> cơ bản
- **Custom Roles**: Các role tùy chỉnh với quyền cụ thể

## Thay đổi thực hiện

### 1. Files đã cập nhật

#### Services
- `services/permissionService.js`:
  - Đổi `isSuperAdmin()` → `isAdmin()`
  - Cập nhật query để kiểm tra role 'Admin' thay vì 'Super Admin'

#### Middleware  
- `middleware/permissionMiddleware.js`:
  - Cập nhật tất cả references từ `isSuperAdmin` → `isAdmin`
  - Đ<PERSON>i `requireAdmin()` → `requireAdmin()`
  - Cập nhật error messages

#### Routes
- `routes/roles.js`: Cập nhật import `requireAdmin` → `requireAdmin`
- `routes/permissions.js`: Cập nhật import `requireAdmin` → `requireAdmin`

#### Scripts
- `scripts/fix-user-role-table.js`: Loại bỏ 'Super Admin' khỏi sample roles
- `scripts/test-permission-system.js`: Cập nhật test từ super admin → admin

### 2. Scripts mới tạo

#### `scripts/remove-super-admin.js`
Script để loại bỏ Super Admin:
- Tìm và xóa Super Admin role
- Migrate users từ Super Admin → Admin
- Grant tất cả permissions cho Admin role
- Cleanup database

#### `scripts/setup-simple-roles.js`
Script thiết lập hệ thống role đơn giản:
- Tạo Admin và User roles
- Grant tất cả permissions cho Admin
- Grant permissions cơ bản cho User
- Verify setup

## Cấu trúc Role mới

### Admin Role
- **Quyền**: Tất cả 65 permissions
- **Mô tả**: Quản trị viên có toàn quyền
- **Capabilities**:
  - Quản lý tables, menus, roles, permissions
  - CRUD trên tất cả dữ liệu
  - Quản lý users và roles

### User Role
- **Quyền**: 15 permissions cơ bản
- **Mô tả**: User thông thường
- **Capabilities**:
  - Browse, read, edit user (profile cá nhân)
  - Các quyền cơ bản khác

### Custom Roles
- Có thể tạo thêm roles với permissions cụ thể
- Flexible theo nhu cầu business

## Permission System

### Format Permission
Mỗi bảng có 5 loại quyền:
- `browse_{table}` - Xem danh sách
- `read_{table}` - Xem chi tiết  
- `edit_{table}` - Chỉnh sửa
- `add_{table}` - Thêm mới
- `delete_{table}` - Xóa

### System Permissions
- `browse_tables` - Quản lý cấu trúc bảng
- `browse_menus` - Quản lý menu
- `browse_roles` - Quản lý roles
- `browse_permissions` - Quản lý permissions

## Usage trong Code

### Middleware
```javascript
// Check admin role
const { requireAdmin } = require('../middleware/permissionMiddleware');
router.post('/admin-only', requireAdmin(), controller.adminFunction);

// Check specific permission  
const { checkPermission } = require('../middleware/permissionMiddleware');
router.get('/users', checkPermission('read', 'user'), controller.getUsers);
```

### Service
```javascript
const permissionService = require('../services/permissionService');

// Check if user is admin
const isAdmin = await permissionService.isAdmin(userId);

// Check specific permission
const hasPermission = await permissionService.checkUserPermission(
  userId, 'browse_user'
);
```

## Current Status

### Database State
- ✅ Super Admin role removed
- ✅ Admin role có 65 permissions  
- ✅ User role có 15 permissions
- ✅ Users đã được assign roles:
  - `<EMAIL>` → Admin
  - `<EMAIL>` → User

### Code State
- ✅ Tất cả references đến Super Admin đã được loại bỏ
- ✅ Middleware và services đã cập nhật
- ✅ Routes đã cập nhật
- ✅ Tests đã cập nhật

## Lợi ích

1. **Đơn giản hóa**: Hệ thống role đơn giản hơn, dễ hiểu
2. **Flexible**: Có thể tạo custom roles theo nhu cầu
3. **Security**: Admin có full access, User có access hạn chế
4. **Maintainable**: Code sạch hơn, ít complexity

## Next Steps

1. **Tạo custom roles** theo business requirements
2. **Assign users** vào appropriate roles
3. **Apply permission middleware** cho tất cả routes cần bảo vệ
4. **Test thoroughly** permission system
5. **Create role management UI** cho admin

## Commands để chạy

```bash
# Loại bỏ Super Admin (nếu cần)
node scripts/remove-super-admin.js

# Setup simple roles
node scripts/setup-simple-roles.js

# Test permission system
node scripts/test-permission-system.js
``` 