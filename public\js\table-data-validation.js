// Frontend validation for table data (CRUD operations)

class TableDataValidator {
    static validateField(value, column) {
        const errors = [];
        
        // Check required fields
        if (!column.is_nullable && !column.is_auto_increment && this.isEmpty(value)) {
            errors.push(`${column.display_name} is required`);
            return errors;
        }
        
        // Skip validation if field is empty and nullable
        if (this.isEmpty(value) && column.is_nullable) {
            return errors;
        }
        
        // Skip auto increment fields
        if (column.is_auto_increment) {
            return errors;
        }
        
        const { type, length } = column;
        const stringValue = String(value).trim();
        
        try {
            this.validateByType(stringValue, type, length, column.display_name);
        } catch (error) {
            errors.push(error.message);
        }
        
        return errors;
    }
    
    static isEmpty(value) {
        return value === null || value === undefined || value === '';
    }
    
    static validateByType(value, type, length, displayName) {
        const numericTypes = ['int', 'bigint', 'smallint', 'tinyint', 'mediumint'];
        const floatTypes = ['decimal', 'float', 'double'];
        const stringTypes = ['varchar', 'char', 'text', 'mediumtext', 'longtext'];
        const dateTypes = ['date', 'datetime', 'timestamp'];
        
        if (numericTypes.includes(type)) {
            const num = parseInt(value);
            if (isNaN(num)) {
                throw new Error(`${displayName} must be a valid integer`);
            }
            
            // Check range based on type
            const ranges = {
                'tinyint': [-128, 127],
                'smallint': [-32768, 32767],
                'mediumint': [-8388608, 8388607],
                'int': [-2147483648, 2147483647],
                'bigint': [-9223372036854775808, 9223372036854775807]
            };
            
            if (ranges[type]) {
                const [min, max] = ranges[type];
                if (num < min || num > max) {
                    throw new Error(`${displayName} must be between ${min} and ${max}`);
                }
            }
        }
        
        if (floatTypes.includes(type)) {
            const num = parseFloat(value);
            if (isNaN(num)) {
                throw new Error(`${displayName} must be a valid number`);
            }
        }
        
        if (stringTypes.includes(type)) {
            if (length && value.length > length) {
                throw new Error(`${displayName} cannot exceed ${length} characters`);
            }
            
            // Check for potentially dangerous characters
            if (this.containsSqlInjection(value)) {
                throw new Error(`${displayName} contains invalid characters`);
            }
        }
        
        if (dateTypes.includes(type)) {
            if (!this.isValidDate(value)) {
                throw new Error(`${displayName} must be a valid date`);
            }
        }
        
        if (type === 'boolean' || type === 'tinyint(1)') {
            const validBooleans = ['true', 'false', '1', '0', 'yes', 'no', 'on', 'off'];
            if (!validBooleans.includes(value.toLowerCase())) {
                throw new Error(`${displayName} must be true or false`);
            }
        }
        
        if (type === 'json') {
            try {
                JSON.parse(value);
            } catch (e) {
                throw new Error(`${displayName} must be valid JSON`);
            }
        }
    }
    
    static containsSqlInjection(value) {
        const sqlPatterns = [
            /['";\\|*%<>{}[\\\]^$()]/i,
            /(union|select|insert|update|delete|drop|create|alter|exec|execute|script|javascript|vbscript)/i
        ];

        return sqlPatterns.some(pattern => pattern.test(value));
    }
    
    static isValidDate(value) {
        // Try different date formats
        const date = new Date(value);
        if (!isNaN(date.getTime())) {
            return true;
        }
        
        // Try MySQL date format YYYY-MM-DD
        if (/^\d{4}-\d{2}-\d{2}$/.test(value)) {
            const [year, month, day] = value.split('-').map(Number);
            const testDate = new Date(year, month - 1, day);
            return testDate.getFullYear() === year && 
                   testDate.getMonth() === month - 1 && 
                   testDate.getDate() === day;
        }
        
        // Try MySQL datetime format YYYY-MM-DD HH:MM:SS
        if (/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/.test(value)) {
            const date = new Date(value.replace(' ', 'T'));
            return !isNaN(date.getTime());
        }
        
        return false;
    }
    
    static validateForm(formData, columns) {
        const errors = {};
        let hasErrors = false;
        
        columns.forEach(column => {
            if (column.is_visible_form) {
                const value = formData[column.name];
                const fieldErrors = this.validateField(value, column);
                
                if (fieldErrors.length > 0) {
                    errors[column.name] = fieldErrors;
                    hasErrors = true;
                }
            }
        });
        
        return { hasErrors, errors };
    }
    
    static displayErrors(errors) {
        // Clear previous errors
        $('.is-invalid').removeClass('is-invalid');
        $('.invalid-feedback').remove();
        
        // Display new errors
        Object.keys(errors).forEach(fieldName => {
            const field = $(`[name="${fieldName}"]`);
            if (field.length > 0) {
                field.addClass('is-invalid');
                const errorMessages = errors[fieldName].join(', ');
                field.after(`<div class="invalid-feedback">${errorMessages}</div>`);
            }
        });
    }
    
    static clearErrors() {
        $('.is-invalid').removeClass('is-invalid');
        $('.invalid-feedback').remove();
    }
    
    // Real-time validation for input fields
    static setupRealTimeValidation(columns) {
        columns.forEach(column => {
            if (column.is_visible_form) {
                $(document).on('input change', `[name="${column.name}"]`, function() {
                    const value = $(this).val();
                    const errors = TableDataValidator.validateField(value, column);
                    
                    if (errors.length > 0) {
                        $(this).addClass('is-invalid');
                        $(this).siblings('.invalid-feedback').remove();
                        $(this).after(`<div class="invalid-feedback">${errors.join(', ')}</div>`);
                    } else {
                        $(this).removeClass('is-invalid');
                        $(this).siblings('.invalid-feedback').remove();
                    }
                });
            }
        });
    }
    
    // Sanitize input to prevent XSS
    static sanitizeInput(value) {
        if (typeof value !== 'string') return value;
        
        return value
            .replace(/&/g, '&amp;')
            .replace(/</g, '&lt;')
            .replace(/>/g, '&gt;')
            .replace(/"/g, '&quot;')
            .replace(/'/g, '&#x27;')
            .replace(/\//g, '&#x2F;');
    }
    
    // Format value for display
    static formatValue(value, column) {
        if (value === null || value === undefined) {
            return '';
        }
        
        const { type } = column;
        
        if (type === 'boolean' || type === 'tinyint(1)') {
            return value ? 'Yes' : 'No';
        }
        
        if (['date', 'datetime', 'timestamp'].includes(type)) {
            try {
                const date = new Date(value);
                if (type === 'date') {
                    return date.toLocaleDateString();
                } else {
                    return date.toLocaleString();
                }
            } catch (e) {
                return value;
            }
        }
        
        if (type === 'json') {
            try {
                return JSON.stringify(JSON.parse(value), null, 2);
            } catch (e) {
                return value;
            }
        }
        
        return value;
    }
}

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = TableDataValidator;
} else {
    window.TableDataValidator = TableDataValidator;
}
