const db = require('../config/database');

async function checkPermissions() {
  try {
    db.connect('development');
    console.log('🔍 Checking admin permissions...\n');
    
    // Check admin user permissions for admintable
    const adminPermissions = await db.query(`
      SELECT p.name, p.display_name, p.table_name, p.action
      FROM permissions p
      JOIN role_permissions rp ON p.id = rp.permission_id
      JOIN role_user ru ON rp.role_id = ru.role_id
      WHERE ru.user_id = 1 AND p.table_name = 'admintable'
      ORDER BY p.name
    `);
    
    console.log('📋 Admin user permissions for admintable:');
    if (adminPermissions.length > 0) {
      adminPermissions.forEach(p => {
        console.log(`✅ ${p.name}: ${p.display_name}`);
      });
    } else {
      console.log('❌ No permissions found for admintable');
    }
    
    // Check if read_admintable exists
    const readPermission = await db.query(`
      SELECT * FROM permissions WHERE name = 'read_admintable'
    `);
    
    console.log(`\n🔍 read_admintable permission exists: ${readPermission.length > 0 ? '✅ YES' : '❌ NO'}`);
    
    if (readPermission.length > 0) {
      console.log('📋 Permission details:', readPermission[0]);
    }
    
    // Check admin role permissions count
    const adminRolePermissions = await db.query(`
      SELECT COUNT(*) as count
      FROM role_permissions rp
      JOIN role_user ru ON rp.role_id = ru.role_id
      WHERE ru.user_id = 1
    `);
    
    console.log(`\n📊 Total permissions for admin user: ${adminRolePermissions[0].count}`);
    
    // Check all admintable permissions
    console.log('\n📋 All admintable permissions in database:');
    const allAdminTablePermissions = await db.query(`
      SELECT name, display_name FROM permissions WHERE table_name = 'admintable' ORDER BY name
    `);
    
    allAdminTablePermissions.forEach(p => {
      console.log(`- ${p.name}: ${p.display_name}`);
    });
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    try {
      db.get().end();
      console.log('\n🔌 Database connection closed');
    } catch (e) {
      console.log('⚠️  Database already closed');
    }
  }
}

checkPermissions();
