// Test script để kiểm tra chức năng sửa cấu trúc table
const adminService = require('../services/adminService');
const schemaService = require('../services/schemaService');

async function testTableStructure() {
  try {
    console.log('🧪 Testing Table Structure Management...\n');

    // 1. L<PERSON>y danh sách bảng admin hiện có
    console.log('1. Getting existing admin tables...');
    const tables = await adminService.getAllAdminTables();
    console.log(`Found ${tables.length} admin tables:`);
    tables.forEach(table => {
      console.log(`   - ${table.name} (${table.display_name}) - ${table.columns.length} columns`);
    });

    if (tables.length === 0) {
      console.log('❌ No admin tables found. Please sync tables first.');
      return;
    }

    // 2. L<PERSON>y bảng đầu tiên để test
    const testTable = tables[0];
    console.log(`\n2. Testing with table: ${testTable.name}`);

    // 3. <PERSON><PERSON><PERSON> thông tin chi tiết bảng
    console.log('3. Getting table details...');
    const tableDetails = await adminService.getAdminTableById(testTable.id);
    console.log(`   Table: ${tableDetails.display_name}`);
    console.log(`   Columns: ${tableDetails.columns.length}`);
    console.log(`   Relations: ${tableDetails.relations.length}`);

    // 4. Hiển thị cấu trúc cột
    console.log('\n4. Current columns:');
    tableDetails.columns.forEach(col => {
      console.log(`   - ${col.name} (${col.type}) - List: ${col.is_visible_list}, Form: ${col.is_visible_form}`);
    });

    // 5. Test thêm cột mới (chỉ simulation, không thực sự thêm)
    console.log('\n5. Testing add column simulation...');
    const newColumnData = {
      name: 'test_column',
      display_name: 'Test Column',
      type: 'varchar',
      length: 255,
      is_nullable: true,
      is_visible_list: false,
      is_visible_form: true,
      is_searchable: false,
      is_sortable: true,
      form_type: 'input'
    };
    console.log(`   Would add column: ${newColumnData.name} (${newColumnData.type})`);

    // 6. Lấy cấu trúc database thực tế
    console.log('\n6. Getting actual database structure...');
    const dbStructure = await schemaService.getTableStructure(testTable.name);
    console.log(`   Database columns: ${dbStructure.columns.length}`);
    console.log(`   Indexes: ${dbStructure.indexes.length}`);
    console.log(`   Foreign keys: ${dbStructure.foreignKeys.length}`);

    // 7. So sánh admin config vs database structure
    console.log('\n7. Comparing admin config vs database structure...');
    const adminColumnNames = tableDetails.columns.map(col => col.name);
    const dbColumnNames = dbStructure.columns.map(col => col.Field);
    
    const missingInAdmin = dbColumnNames.filter(name => !adminColumnNames.includes(name));
    const missingInDb = adminColumnNames.filter(name => !dbColumnNames.includes(name));

    if (missingInAdmin.length > 0) {
      console.log(`   ⚠️  Columns in DB but not in admin config: ${missingInAdmin.join(', ')}`);
    }
    if (missingInDb.length > 0) {
      console.log(`   ⚠️  Columns in admin config but not in DB: ${missingInDb.join(', ')}`);
    }
    if (missingInAdmin.length === 0 && missingInDb.length === 0) {
      console.log('   ✅ Admin config and database structure are in sync');
    }

    console.log('\n✅ Table structure test completed successfully!');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error(error.stack);
  }
}

// Chạy test
if (require.main === module) {
  testTableStructure().then(() => {
    console.log('\n🎉 Test finished!');
    process.exit(0);
  }).catch(error => {
    console.error('💥 Test crashed:', error);
    process.exit(1);
  });
}

module.exports = { testTableStructure };
