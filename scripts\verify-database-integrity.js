const db = require('../config/database');

/**
 * <PERSON><PERSON>t kiểm tra tính toàn vẹn của database
 * Verify all tables, relationships, and data integrity
 */

async function verifyDatabaseIntegrity() {
  try {
    console.log('🔍 Starting comprehensive database integrity check...\n');
    
    // Connect to database
    db.connect('development');
    console.log('✅ Connected to database\n');

    // 1. Check all required tables exist
    await checkRequiredTables();
    
    // 2. Check table structures
    await checkTableStructures();
    
    // 3. Check foreign key constraints
    await checkForeignKeyConstraints();
    
    // 4. Check data integrity
    await checkDataIntegrity();
    
    // 5. Check admin system configuration
    await checkAdminSystemConfig();
    
    // 6. Check permissions and roles
    await checkPermissionsAndRoles();
    
    console.log('\n🎉 Database integrity check completed!');
    
  } catch (error) {
    console.error('💥 Database integrity check failed:', error);
    process.exit(1);
  } finally {
    try {
      const pool = db.get();
      if (pool) {
        pool.end();
        console.log('🔌 Database connection closed');
      }
    } catch (error) {
      console.log('⚠️  Database connection already closed');
    }
  }
}

async function checkRequiredTables() {
  console.log('📋 Checking required tables...');
  
  const requiredTables = [
    'user', 'role', 'permissions', 'role_user', 'role_permissions',
    'user_sessions', 'admintable', 'admincolumn', 'adminrelation', 'admin_menus'
  ];
  
  const tables = await db.query('SHOW TABLES');
  const existingTables = tables.map(row => Object.values(row)[0]);
  
  let allTablesExist = true;
  
  for (const table of requiredTables) {
    if (existingTables.includes(table)) {
      console.log(`✅ Table '${table}' exists`);
    } else {
      console.log(`❌ Table '${table}' missing`);
      allTablesExist = false;
    }
  }
  
  if (allTablesExist) {
    console.log('✅ All required tables exist\n');
  } else {
    console.log('❌ Some required tables are missing\n');
  }
}

async function checkTableStructures() {
  console.log('🏗️  Checking table structures...');
  
  const tableChecks = [
    {
      table: 'user',
      requiredColumns: ['id', 'fullname', 'email', 'password', 'active', 'created_at']
    },
    {
      table: 'role',
      requiredColumns: ['id', 'name', 'display_name', 'description']
    },
    {
      table: 'permissions',
      requiredColumns: ['id', 'name', 'display_name', 'table_name', 'action']
    },
    {
      table: 'admintable',
      requiredColumns: ['id', 'name', 'display_name', 'model_name', 'is_active']
    },
    {
      table: 'admincolumn',
      requiredColumns: ['id', 'table_id', 'name', 'type', 'is_nullable', 'is_primary']
    }
  ];
  
  for (const check of tableChecks) {
    try {
      const columns = await db.query(`DESCRIBE ${check.table}`);
      const existingColumns = columns.map(col => col.Field);
      
      let allColumnsExist = true;
      for (const requiredCol of check.requiredColumns) {
        if (!existingColumns.includes(requiredCol)) {
          console.log(`❌ ${check.table}.${requiredCol} missing`);
          allColumnsExist = false;
        }
      }
      
      if (allColumnsExist) {
        console.log(`✅ Table '${check.table}' structure OK (${existingColumns.length} columns)`);
      }
    } catch (error) {
      console.log(`❌ Error checking ${check.table}: ${error.message}`);
    }
  }
  
  console.log('');
}

async function checkForeignKeyConstraints() {
  console.log('🔗 Checking foreign key constraints...');
  
  const fkChecks = [
    {
      table: 'role_user',
      column: 'user_id',
      references: 'user.id'
    },
    {
      table: 'role_user', 
      column: 'role_id',
      references: 'role.id'
    },
    {
      table: 'role_permissions',
      column: 'role_id',
      references: 'role.id'
    },
    {
      table: 'role_permissions',
      column: 'permission_id',
      references: 'permissions.id'
    },
    {
      table: 'admincolumn',
      column: 'table_id',
      references: 'admintable.id'
    }
  ];
  
  for (const fk of fkChecks) {
    try {
      // Check if foreign key constraint exists
      const constraints = await db.query(`
        SELECT CONSTRAINT_NAME, COLUMN_NAME, REFERENCED_TABLE_NAME, REFERENCED_COLUMN_NAME
        FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
        WHERE TABLE_SCHEMA = DATABASE() 
        AND TABLE_NAME = ? 
        AND COLUMN_NAME = ?
        AND REFERENCED_TABLE_NAME IS NOT NULL
      `, [fk.table, fk.column]);
      
      if (constraints.length > 0) {
        console.log(`✅ FK constraint: ${fk.table}.${fk.column} → ${fk.references}`);
      } else {
        console.log(`⚠️  FK constraint missing: ${fk.table}.${fk.column} → ${fk.references}`);
      }
    } catch (error) {
      console.log(`❌ Error checking FK ${fk.table}.${fk.column}: ${error.message}`);
    }
  }
  
  console.log('');
}

async function checkDataIntegrity() {
  console.log('📊 Checking data integrity...');
  
  try {
    // Check for orphaned records
    const orphanedRoleUsers = await db.query(`
      SELECT COUNT(*) as count FROM role_user ru
      LEFT JOIN user u ON ru.user_id = u.id
      LEFT JOIN role r ON ru.role_id = r.id
      WHERE u.id IS NULL OR r.id IS NULL
    `);
    
    if (orphanedRoleUsers[0].count === 0) {
      console.log('✅ No orphaned role_user records');
    } else {
      console.log(`❌ Found ${orphanedRoleUsers[0].count} orphaned role_user records`);
    }
    
    // Check for orphaned role_permissions
    const orphanedRolePermissions = await db.query(`
      SELECT COUNT(*) as count FROM role_permissions rp
      LEFT JOIN role r ON rp.role_id = r.id
      LEFT JOIN permissions p ON rp.permission_id = p.id
      WHERE r.id IS NULL OR p.id IS NULL
    `);
    
    if (orphanedRolePermissions[0].count === 0) {
      console.log('✅ No orphaned role_permissions records');
    } else {
      console.log(`❌ Found ${orphanedRolePermissions[0].count} orphaned role_permissions records`);
    }
    
    // Check admin system integrity
    const orphanedAdminColumns = await db.query(`
      SELECT COUNT(*) as count FROM admincolumn ac
      LEFT JOIN admintable at ON ac.table_id = at.id
      WHERE at.id IS NULL
    `);
    
    if (orphanedAdminColumns[0].count === 0) {
      console.log('✅ No orphaned admincolumn records');
    } else {
      console.log(`❌ Found ${orphanedAdminColumns[0].count} orphaned admincolumn records`);
    }
    
  } catch (error) {
    console.log(`❌ Error checking data integrity: ${error.message}`);
  }
  
  console.log('');
}

async function checkAdminSystemConfig() {
  console.log('⚙️  Checking admin system configuration...');
  
  try {
    // Check if all system tables are configured in admintable
    const systemTables = ['user', 'role', 'permissions', 'role_user', 'role_permissions', 'admintable', 'admin_menus'];
    const configuredTables = await db.query('SELECT name FROM admintable WHERE is_active = 1');
    const configuredTableNames = configuredTables.map(t => t.name);
    
    for (const table of systemTables) {
      if (configuredTableNames.includes(table)) {
        console.log(`✅ Table '${table}' configured in admin system`);
      } else {
        console.log(`⚠️  Table '${table}' not configured in admin system`);
      }
    }
    
    // Check if admin tables have columns configured
    const tablesWithoutColumns = await db.query(`
      SELECT at.name, COUNT(ac.id) as column_count
      FROM admintable at
      LEFT JOIN admincolumn ac ON at.id = ac.table_id
      WHERE at.is_active = 1
      GROUP BY at.id, at.name
      HAVING column_count = 0
    `);
    
    if (tablesWithoutColumns.length === 0) {
      console.log('✅ All admin tables have columns configured');
    } else {
      console.log('⚠️  Some admin tables have no columns configured:');
      tablesWithoutColumns.forEach(t => {
        console.log(`   - ${t.name}`);
      });
    }
    
  } catch (error) {
    console.log(`❌ Error checking admin system config: ${error.message}`);
  }
  
  console.log('');
}

async function checkPermissionsAndRoles() {
  console.log('🔐 Checking permissions and roles...');
  
  try {
    // Check if admin user has admin role
    const adminUser = await db.query(`
      SELECT u.email, r.name as role_name
      FROM user u
      INNER JOIN role_user ru ON u.id = ru.user_id
      INNER JOIN role r ON ru.role_id = r.id
      WHERE u.email = '<EMAIL>'
    `);
    
    if (adminUser.length > 0 && adminUser[0].role_name === 'Admin') {
      console.log('✅ Admin user has Admin role');
    } else {
      console.log('❌ Admin user does not have Admin role');
    }
    
    // Check if Admin role has permissions
    const adminPermissions = await db.query(`
      SELECT COUNT(*) as count
      FROM role r
      INNER JOIN role_permissions rp ON r.id = rp.role_id
      WHERE r.name = 'Admin'
    `);
    
    if (adminPermissions[0].count > 0) {
      console.log(`✅ Admin role has ${adminPermissions[0].count} permissions`);
    } else {
      console.log('❌ Admin role has no permissions');
    }
    
    // Check role distribution
    const roleStats = await db.query(`
      SELECT r.name, COUNT(ru.user_id) as user_count
      FROM role r
      LEFT JOIN role_user ru ON r.id = ru.role_id
      GROUP BY r.id, r.name
      ORDER BY user_count DESC
    `);
    
    console.log('📊 Role distribution:');
    roleStats.forEach(stat => {
      console.log(`   - ${stat.name}: ${stat.user_count} users`);
    });
    
  } catch (error) {
    console.log(`❌ Error checking permissions and roles: ${error.message}`);
  }
  
  console.log('');
}

// Run the verification
if (require.main === module) {
  verifyDatabaseIntegrity()
    .then(() => {
      console.log('✨ Verification completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Verification failed:', error);
      process.exit(1);
    });
}

module.exports = { verifyDatabaseIntegrity };
