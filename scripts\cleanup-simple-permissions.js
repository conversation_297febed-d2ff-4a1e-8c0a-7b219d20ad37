const db = require('../config/database');
const permissionService = require('../services/permissionService');

async function cleanupSimplePermissions() {
  try {
    console.log('🧹 Cleaning up and setting up simple permission system...');

    // Initialize database connection
    db.connect('development');
    
    // 1. Remove all browse permissions
    console.log('1. Removing browse permissions...');
    
    // First remove role_permissions for browse actions
    const browsePermissions = await db.query(`
      SELECT id FROM permissions WHERE action = 'browse'
    `);
    
    if (browsePermissions.length > 0) {
      const permissionIds = browsePermissions.map(p => p.id);
      await db.query(`
        DELETE FROM role_permissions 
        WHERE permission_id IN (${permissionIds.join(',')})
      `);
      
      // Then remove the permissions themselves
      await db.query(`
        DELETE FROM permissions WHERE action = 'browse'
      `);
      
      console.log(`✅ Removed ${browsePermissions.length} browse permissions`);
    } else {
      console.log('⚠️ No browse permissions found');
    }

    // 2. Create simplified system permissions (only for Admin)
    console.log('2. Creating simplified system permissions...');
    
    const systemModules = [
      { name: 'tables', display_name: 'Tables', table_name: 'admintable' },
      { name: 'menus', display_name: 'Menus', table_name: 'admin_menus' },
      { name: 'roles', display_name: 'Roles', table_name: 'role' },
      { name: 'permissions', display_name: 'Permissions', table_name: 'permissions' }
    ];

    const actions = ['read', 'edit', 'add', 'delete'];
    let systemPermissionCount = 0;

    for (const module of systemModules) {
      for (const action of actions) {
        const permissionName = `${action}_${module.name}`;
        const displayName = `${capitalizeFirst(action)} ${module.display_name}`;
        const description = getActionDescription(action, module.display_name);

        // Check if permission exists
        const existingPermission = await db.queryOne(`
          SELECT id FROM permissions WHERE name = ?
        `, [permissionName]);

        if (!existingPermission) {
          await db.query(`
            INSERT INTO permissions (name, display_name, description, table_name, action)
            VALUES (?, ?, ?, ?, ?)
          `, [permissionName, displayName, description, module.table_name, action]);
          systemPermissionCount++;
        }
      }
    }

    console.log(`✅ Created ${systemPermissionCount} system permissions`);

    // 3. Recreate permissions for all existing tables
    console.log('3. Recreating permissions for existing tables...');
    
    const existingTables = await db.query(`
      SELECT name, display_name FROM admintable WHERE is_active = 1
    `);

    let tablePermissionCount = 0;
    for (const table of existingTables) {
      // Skip system tables
      if (['admintable', 'admin_menus', 'role', 'permissions'].includes(table.name)) {
        continue;
      }

      for (const action of actions) {
        const permissionName = `${action}_${table.name}`;
        const displayName = `${capitalizeFirst(action)} ${table.display_name}`;
        const description = getActionDescription(action, table.display_name);

        // Check if permission exists
        const existingPermission = await db.queryOne(`
          SELECT id FROM permissions WHERE name = ?
        `, [permissionName]);

        if (!existingPermission) {
          await db.query(`
            INSERT INTO permissions (name, display_name, description, table_name, action)
            VALUES (?, ?, ?, ?, ?)
          `, [permissionName, displayName, description, table.name, action]);
          tablePermissionCount++;
        }
      }
    }

    console.log(`✅ Created ${tablePermissionCount} table permissions for ${existingTables.length} tables`);

    // 4. Grant all permissions to Admin role
    console.log('4. Granting permissions to Admin role...');
    
    const adminRole = await db.queryOne(`
      SELECT id FROM role WHERE name = 'Admin'
    `);

    if (!adminRole) {
      throw new Error('Admin role not found');
    }

    // Get all current permissions
    const allPermissions = await db.query('SELECT id FROM permissions');
    
    let grantedCount = 0;
    for (const permission of allPermissions) {
      // Check if Admin already has this permission
      const existingPermission = await db.queryOne(`
        SELECT id FROM role_permissions 
        WHERE role_id = ? AND permission_id = ?
      `, [adminRole.id, permission.id]);

      if (!existingPermission) {
        await db.query(`
          INSERT INTO role_permissions (role_id, permission_id, granted_at)
          VALUES (?, ?, NOW())
        `, [adminRole.id, permission.id]);
        grantedCount++;
      }
    }

    console.log(`✅ Granted ${grantedCount} permissions to Admin role`);

    // 5. Setup basic permissions for User role
    console.log('5. Setting up basic permissions for User role...');
    
    const userRole = await db.queryOne(`
      SELECT id FROM role WHERE name = 'User'
    `);

    if (!userRole) {
      throw new Error('User role not found');
    }

    // Grant basic user permissions
    const basicUserPermissions = [
      'read_user',
      'edit_user'  // Users can edit their own profile
    ];

    let userGrantedCount = 0;
    for (const permissionName of basicUserPermissions) {
      const permission = await db.queryOne(`
        SELECT id FROM permissions WHERE name = ?
      `, [permissionName]);

      if (permission) {
        // Check if User already has this permission
        const existingPermission = await db.queryOne(`
          SELECT id FROM role_permissions 
          WHERE role_id = ? AND permission_id = ?
        `, [userRole.id, permission.id]);

        if (!existingPermission) {
          await db.query(`
            INSERT INTO role_permissions (role_id, permission_id, granted_at)
            VALUES (?, ?, NOW())
          `, [userRole.id, permission.id]);
          userGrantedCount++;
        }
      }
    }

    console.log(`✅ Granted ${userGrantedCount} basic permissions to User role`);

    // 6. Clear permission cache
    permissionService.clearCache();

    // 7. Verify final state
    console.log('7. Verifying final state...');
    
    const finalStats = await db.queryOne(`
      SELECT 
        (SELECT COUNT(*) FROM permissions) as total_permissions,
        (SELECT COUNT(*) FROM permissions WHERE action = 'browse') as browse_permissions,
        (SELECT COUNT(*) FROM role_permissions WHERE role_id = ?) as admin_permissions,
        (SELECT COUNT(*) FROM role_permissions WHERE role_id = ?) as user_permissions
    `, [adminRole.id, userRole.id]);

    console.log('\n📊 Final statistics:');
    console.log(`   - Total permissions: ${finalStats.total_permissions}`);
    console.log(`   - Browse permissions: ${finalStats.browse_permissions} (should be 0)`);
    console.log(`   - Admin permissions: ${finalStats.admin_permissions}`);
    console.log(`   - User permissions: ${finalStats.user_permissions}`);

    // Show permission breakdown by action
    const actionBreakdown = await db.query(`
      SELECT action, COUNT(*) as count
      FROM permissions 
      GROUP BY action
      ORDER BY CASE action
        WHEN 'read' THEN 1
        WHEN 'edit' THEN 2  
        WHEN 'add' THEN 3
        WHEN 'delete' THEN 4
        ELSE 5
      END
    `);

    console.log('\n🔍 Permission breakdown by action:');
    actionBreakdown.forEach(row => {
      console.log(`   - ${row.action}: ${row.count} permissions`);
    });

    console.log('\n🎉 Simple permission system setup completed!');
    console.log('\nNew system structure:');
    console.log('📋 For each table: Read, Edit, Add, Delete (4 permissions)');
    console.log('👤 User role: Basic permissions (read/edit own profile)');
    console.log('⚡ Admin role: All permissions including system management');

  } catch (error) {
    console.error('❌ Error cleaning up permissions:', error);
  }
  
  process.exit(0);
}

// Helper functions
function capitalizeFirst(str) {
  return str.charAt(0).toUpperCase() + str.slice(1);
}

function getActionDescription(action, displayName) {
  const descriptions = {
    read: `Quyền xem ${displayName}`,
    edit: `Quyền chỉnh sửa ${displayName}`,
    add: `Quyền tạo ${displayName} mới`,
    delete: `Quyền xóa ${displayName}`
  };
  return descriptions[action] || `Quyền ${action} ${displayName}`;
}

cleanupSimplePermissions(); 