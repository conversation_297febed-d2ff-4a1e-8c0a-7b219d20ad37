const db = require('../config/database');

/**
 * <PERSON>ript để tạo các bảng bị thiếu cho deviceController và multiDeviceService
 */

async function createMissingTables() {
  try {
    console.log('🔧 Creating missing tables for device management...\n');
    
    // Connect to database
    db.connect('development');
    console.log('✅ Connected to database');

    // 1. Create user_sessions table if not exists
    console.log('\n📋 Creating user_sessions table...');
    const createUserSessionsSQL = `
      CREATE TABLE IF NOT EXISTS \`user_sessions\` (
        \`id\` INT AUTO_INCREMENT PRIMARY KEY,
        \`user_id\` INT NOT NULL,
        \`jwt_token_id\` VARCHAR(255) NOT NULL UNIQUE,
        \`token_id\` VARCHAR(255) NULL,
        \`device_name\` VARCHAR(255) NULL,
        \`device_type\` VARCHAR(50) NULL,
        \`browser\` VARCHAR(100) NULL,
        \`os\` VARCHAR(100) NULL,
        \`device_info\` TEXT NULL,
        \`ip_address\` VARCHAR(45) NULL,
        \`user_agent\` TEXT NULL,
        \`login_at\` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        \`last_activity\` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        \`expires_at\` TIMESTAMP NULL,
        \`is_active\` TINYINT(1) DEFAULT 1,
        \`is_current_session\` TINYINT(1) DEFAULT 0,
        \`created_at\` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        \`last_used_at\` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        
        FOREIGN KEY (\`user_id\`) REFERENCES \`user\`(\`id\`) ON DELETE CASCADE,
        INDEX \`idx_user_id\` (\`user_id\`),
        INDEX \`idx_jwt_token_id\` (\`jwt_token_id\`),
        INDEX \`idx_token_id\` (\`token_id\`),
        INDEX \`idx_expires_at\` (\`expires_at\`),
        INDEX \`idx_is_active\` (\`is_active\`),
        INDEX \`idx_last_activity\` (\`last_activity\`)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    `;

    await db.query(createUserSessionsSQL);
    console.log('✅ user_sessions table created successfully');

    // 2. Create user_session_settings table if not exists
    console.log('\n📋 Creating user_session_settings table...');
    const createUserSessionSettingsSQL = `
      CREATE TABLE IF NOT EXISTS \`user_session_settings\` (
        \`id\` INT AUTO_INCREMENT PRIMARY KEY,
        \`user_id\` INT NOT NULL UNIQUE,
        \`max_sessions\` INT DEFAULT 5,
        \`session_timeout_hours\` INT DEFAULT 24,
        \`allow_multiple_devices\` TINYINT(1) DEFAULT 1,
        \`notify_new_login\` TINYINT(1) DEFAULT 1,
        \`auto_logout_inactive\` TINYINT(1) DEFAULT 1,
        \`inactive_timeout_hours\` INT DEFAULT 72,
        \`require_2fa_new_device\` TINYINT(1) DEFAULT 0,
        \`created_at\` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        \`updated_at\` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        
        FOREIGN KEY (\`user_id\`) REFERENCES \`user\`(\`id\`) ON DELETE CASCADE,
        INDEX \`idx_user_id\` (\`user_id\`)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    `;

    await db.query(createUserSessionSettingsSQL);
    console.log('✅ user_session_settings table created successfully');

    // 3. Add missing columns to user table if not exists
    console.log('\n📋 Adding missing columns to user table...');
    
    const addColumnsSQL = [
      'ALTER TABLE `user` ADD COLUMN IF NOT EXISTS `jwt_token_id` VARCHAR(255) NULL',
      'ALTER TABLE `user` ADD COLUMN IF NOT EXISTS `device_info` TEXT NULL',
      'ALTER TABLE `user` ADD COLUMN IF NOT EXISTS `token_created_at` TIMESTAMP NULL',
      'ALTER TABLE `user` ADD COLUMN IF NOT EXISTS `last_login` TIMESTAMP NULL',
      'ALTER TABLE `user` ADD INDEX IF NOT EXISTS `idx_jwt_token_id` (`jwt_token_id`)'
    ];

    for (const sql of addColumnsSQL) {
      try {
        await db.query(sql);
        console.log(`✅ Executed: ${sql.substring(0, 50)}...`);
      } catch (error) {
        if (error.message.includes('Duplicate column') || error.message.includes('Duplicate key')) {
          console.log(`⚠️  Column/Index already exists: ${sql.substring(0, 50)}...`);
        } else {
          console.log(`❌ Error: ${error.message}`);
        }
      }
    }

    // 4. Insert default session settings for existing users
    console.log('\n📋 Creating default session settings for existing users...');
    
    const users = await db.query('SELECT id FROM user');
    console.log(`Found ${users.length} users`);

    for (const user of users) {
      try {
        const existingSettings = await db.query(
          'SELECT id FROM user_session_settings WHERE user_id = ?', 
          [user.id]
        );

        if (existingSettings.length === 0) {
          await db.query(`
            INSERT INTO user_session_settings 
            (user_id, max_sessions, session_timeout_hours, allow_multiple_devices, notify_new_login, auto_logout_inactive, inactive_timeout_hours, require_2fa_new_device) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
          `, [user.id, 5, 24, 1, 1, 1, 72, 0]);
          
          console.log(`✅ Created default settings for user ${user.id}`);
        } else {
          console.log(`⚠️  Settings already exist for user ${user.id}`);
        }
      } catch (error) {
        console.log(`❌ Error creating settings for user ${user.id}: ${error.message}`);
      }
    }

    // 5. Verify tables were created
    console.log('\n🔍 Verifying created tables...');
    
    const tables = await db.query('SHOW TABLES');
    const tableNames = tables.map(row => Object.values(row)[0]);
    
    const requiredTables = ['user_sessions', 'user_session_settings'];
    
    for (const table of requiredTables) {
      if (tableNames.includes(table)) {
        const count = await db.query(`SELECT COUNT(*) as count FROM ${table}`);
        console.log(`✅ Table '${table}' exists with ${count[0].count} records`);
      } else {
        console.log(`❌ Table '${table}' not found`);
      }
    }

    // 6. Test basic functionality
    console.log('\n🧪 Testing basic functionality...');
    
    try {
      // Test user_sessions table
      const testSession = {
        user_id: 1,
        jwt_token_id: 'test_token_' + Date.now(),
        device_name: 'Test Device',
        device_type: 'desktop',
        browser: 'Chrome',
        os: 'Windows',
        ip_address: '127.0.0.1',
        is_active: 1
      };

      const insertResult = await db.query(
        'INSERT INTO user_sessions (user_id, jwt_token_id, device_name, device_type, browser, os, ip_address, is_active) VALUES (?, ?, ?, ?, ?, ?, ?, ?)',
        [testSession.user_id, testSession.jwt_token_id, testSession.device_name, testSession.device_type, testSession.browser, testSession.os, testSession.ip_address, testSession.is_active]
      );

      console.log('✅ Test session created successfully');

      // Clean up test data
      await db.query('DELETE FROM user_sessions WHERE jwt_token_id = ?', [testSession.jwt_token_id]);
      console.log('✅ Test data cleaned up');

    } catch (error) {
      console.log(`❌ Test failed: ${error.message}`);
    }

    console.log('\n🎉 Missing tables creation completed successfully!');
    console.log('\n📋 Summary:');
    console.log('✅ user_sessions table - For multi-device session management');
    console.log('✅ user_session_settings table - For user session preferences');
    console.log('✅ Additional user table columns - For backward compatibility');
    console.log('✅ Default settings for existing users');

  } catch (error) {
    console.error('💥 Error creating missing tables:', error);
    process.exit(1);
  } finally {
    try {
      const pool = db.get();
      if (pool) {
        pool.end();
        console.log('🔌 Database connection closed');
      }
    } catch (error) {
      console.log('⚠️  Database connection already closed');
    }
  }
}

// Run if called directly
if (require.main === module) {
  createMissingTables()
    .then(() => {
      console.log('✨ Script completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Script failed:', error);
      process.exit(1);
    });
}

module.exports = { createMissingTables };
