const express = require('express');
const db = require('../config/database');

async function testServer() {
  try {
    console.log('🧪 Testing server startup...\n');
    
    // Test database connection
    console.log('1. Testing database connection...');
    db.connect('development');
    console.log('✅ Database connected successfully');
    
    // Test basic Express app
    console.log('\n2. Testing Express app...');
    const app = express();
    console.log('✅ Express app created successfully');
    
    // Test admin user permissions
    console.log('\n3. Testing admin permissions...');
    const adminPermissions = await db.query(`
      SELECT COUNT(*) as count
      FROM permissions p
      JOIN role_permissions rp ON p.id = rp.permission_id
      JOIN role_user ru ON rp.role_id = ru.role_id
      WHERE ru.user_id = 1 AND p.name = 'read_admintable'
    `);
    
    if (adminPermissions[0].count > 0) {
      console.log('✅ Admin has read_admintable permission');
    } else {
      console.log('❌ Admin missing read_admintable permission');
    }
    
    // Test JWT service
    console.log('\n4. Testing JWT service...');
    const jwtService = require('../services/jwtService');
    const testUser = { id: 1, email: '<EMAIL>', fullname: 'Admin' };
    const { token } = jwtService.createToken(testUser);
    console.log('✅ JWT service working');
    
    // Test permission service
    console.log('\n5. Testing permission service...');
    const permissionService = require('../services/permissionService');
    const hasPermission = await permissionService.checkUserPermission(1, 'read_admintable');
    console.log(`✅ Permission check result: ${hasPermission}`);
    
    console.log('\n🎉 All tests passed! Server should work correctly.');
    console.log('\n📋 To start server manually:');
    console.log('   node ./bin/www');
    console.log('\n📋 To test admin access:');
    console.log('   1. <NAME_EMAIL> / password123');
    console.log('   2. Go to /admin/tables');
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  } finally {
    try {
      db.get().end();
      console.log('\n🔌 Database connection closed');
    } catch (e) {
      console.log('⚠️  Database already closed');
    }
  }
}

testServer();
