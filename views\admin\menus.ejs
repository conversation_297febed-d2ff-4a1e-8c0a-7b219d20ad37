<div class="container-fluid">

  <!-- Page Header -->
  <div class="row">
    <div class="col-12">
      <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
          <h4 class="card-title mb-0">
            <i class="fas fa-bars"></i> Menu Management
          </h4>
          <div>
            <% if (user && user.isAdmin) { %>
              <button type="button" class="btn btn-success btn-sm" onclick="syncTableMenus()">
                <i class="fas fa-sync"></i> Sync Table Menus
              </button>
              <button type="button" class="btn btn-info btn-sm" onclick="createDefaultMenus()">
                <i class="fas fa-plus-circle"></i> Create Defaults
              </button>
            <% } %>
            <% if (userPermissions && userPermissions.canCreate) { %>
              <button type="button" class="btn btn-primary btn-sm" data-toggle="modal" data-target="#menuModal">
                <i class="fas fa-plus"></i> Add Menu
              </button>
            <% } else { %>
              <span class="text-muted"><i class="fas fa-lock me-1"></i>No permission to add</span>
            <% } %>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Menu Table -->
  <div class="row mt-3">
    <div class="col-12">
      <div class="card">
        <div class="card-header">
          <h5 class="card-title mb-0">Menu Items</h5>
        </div>
        <div class="card-body">
          <div class="table-responsive">
            <table class="table table-striped table-hover" id="menusTable">
              <thead>
                <tr>
                  <th>ID</th>
                  <th>Title</th>
                  <th>URL</th>
                  <th>Icon</th>
                  <th>Parent</th>
                  <th>Order</th>
                  <th>Type</th>
                  <th>Status</th>
                  <th>Badge</th>
                  <th>Table</th>
                  <th>Children</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                <!-- Data will be loaded via AJAX -->
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Menu Modal -->
<div class="modal fade" id="menuModal" tabindex="-1" role="dialog">
  <div class="modal-dialog modal-lg" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="menuModalTitle">Add New Menu</h5>
        <button type="button" class="btn-close" data-dismiss="modal" aria-label="Close"></button>
      </div>
      <form id="menuForm">
        <div class="modal-body">
          <div class="row">
            <div class="col-md-6">
              <div class="form-group">
                <label for="menuTitle">Title *</label>
                <input type="text" class="form-control" id="menuTitle" name="title" required>
              </div>
            </div>
            <div class="col-md-6">
              <div class="form-group">
                <label for="menuUrl">URL</label>
                <input type="text" class="form-control" id="menuUrl" name="url" placeholder="/path/to/page">
                <small class="form-text text-muted">Leave empty for parent menus</small>
              </div>
            </div>
          </div>
          
          <div class="row">
            <div class="col-md-6">
              <div class="form-group">
                <label for="menuIcon">Icon</label>
                <input type="text" class="form-control" id="menuIcon" name="icon" placeholder="cil-speedometer">
                <small class="form-text text-muted">CoreUI icon name (without 'cil-' prefix)</small>
              </div>
            </div>
            <div class="col-md-6">
              <div class="form-group">
                <label for="menuParent">Parent Menu</label>
                <select class="form-control" id="menuParent" name="parent_id">
                  <option value="">-- Root Menu --</option>
                </select>
              </div>
            </div>
          </div>

          <div class="row">
            <div class="col-md-6">
              <div class="form-group">
                <label for="menuOrder">Order Index</label>
                <input type="number" class="form-control" id="menuOrder" name="order_index" value="0">
              </div>
            </div>
            <div class="col-md-6">
              <div class="form-group">
                <label for="menuTarget">Target</label>
                <select class="form-control" id="menuTarget" name="target">
                  <option value="">Default</option>
                  <option value="_blank">New Window</option>
                  <option value="_self">Same Window</option>
                </select>
              </div>
            </div>
          </div>

          <div class="row">
            <div class="col-md-6">
              <div class="form-group">
                <label for="badgeText">Badge Text</label>
                <input type="text" class="form-control" id="badgeText" name="badge_text" placeholder="NEW">
              </div>
            </div>
            <div class="col-md-6">
              <div class="form-group">
                <label for="badgeColor">Badge Color</label>
                <select class="form-control" id="badgeColor" name="badge_color">
                  <option value="">None</option>
                  <option value="primary">Primary</option>
                  <option value="secondary">Secondary</option>
                  <option value="success">Success</option>
                  <option value="danger">Danger</option>
                  <option value="warning">Warning</option>
                  <option value="info">Info</option>
                  <option value="light">Light</option>
                  <option value="dark">Dark</option>
                </select>
              </div>
            </div>
          </div>

          <div class="row">
            <div class="col-md-6">
              <div class="form-group">
                <label for="linkedTable">Linked Table</label>
                <select class="form-control" id="linkedTable" name="table_id">
                  <option value="">-- No Table --</option>
                </select>
                <small class="form-text text-muted">Auto-generate URL from table</small>
              </div>
            </div>
          </div>

          <hr>
          <h6>Menu Type</h6>
          <div class="row">
            <div class="col-md-4">
              <div class="form-check">
                <input type="checkbox" class="form-check-input" id="isActive" name="is_active" checked>
                <label class="form-check-label" for="isActive">Active</label>
              </div>
            </div>
            <div class="col-md-4">
              <div class="form-check">
                <input type="checkbox" class="form-check-input" id="isTitle" name="is_title">
                <label class="form-check-label" for="isTitle">Title/Header</label>
              </div>
            </div>
            <div class="col-md-4">
              <div class="form-check">
                <input type="checkbox" class="form-check-input" id="isDivider" name="is_divider">
                <label class="form-check-label" for="isDivider">Divider</label>
              </div>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
          <button type="submit" class="btn btn-primary">Save Menu</button>
        </div>
      </form>
    </div>
  </div>
</div>

<script>
// Permission flags for JavaScript
const USER_PERMISSIONS = <%- JSON.stringify({
    canBrowse: userPermissions && userPermissions.canBrowse || false,
    canRead: userPermissions && userPermissions.canRead || false,
    canCreate: userPermissions && userPermissions.canCreate || false,
    canUpdate: userPermissions && userPermissions.canUpdate || false,
    canDelete: userPermissions && userPermissions.canDelete || false,
    isAdmin: user && user.isAdmin || false
}) %>;

$(document).ready(function() {
    let menusTable;
    let isEditMode = false;
    let editingMenuId = null;

    // Initialize DataTable
    menusTable = $('#menusTable').DataTable({
        processing: true,
        serverSide: false,
        ajax: {
            url: '/admin/menus/data',
            type: 'GET',
            error: function(xhr, error, code) {
                console.log('DataTable error:', xhr, error, code);
                if (xhr.status === 403) {
                    const response = xhr.responseJSON;
                    showAlert('danger', response?.message || 'Bạn không có quyền truy cập danh sách này');
                    
                    // Clear table and show permission message
                    $('#menusTable tbody').html(`
                        <tr>
                            <td colspan="12" class="text-center text-danger">
                                <i class="fas fa-lock fa-2x mb-2"></i><br>
                                <strong>${response?.message || 'Bạn không có quyền truy cập danh sách này'}</strong>
                            </td>
                        </tr>
                    `);
                } else {
                    showAlert('danger', 'Lỗi tải dữ liệu menu');
                }
            }
        },
        columns: [
            { data: 'id' },
            { data: 'title' },
            { data: 'url' },
            { 
                data: 'icon',
                render: function(data, type, row) {
                    if (data && data !== '-') {
                        return `<i class="cil-${data}"></i> ${data}`;
                    }
                    return '-';
                }
            },
            { data: 'parent' },
            { data: 'order_index' },
            { 
                data: null,
                render: function(data, type, row) {
                    let badges = [];
                    if (row.is_title) badges.push('<span class="badge badge-info">Title</span>');
                    if (row.is_divider) badges.push('<span class="badge badge-secondary">Divider</span>');
                    if (badges.length === 0) badges.push('<span class="badge badge-primary">Menu</span>');
                    return badges.join(' ');
                }
            },
            { 
                data: 'is_active',
                render: function(data, type, row) {
                    return data ? 
                        '<span class="badge badge-success">Active</span>' : 
                        '<span class="badge badge-danger">Inactive</span>';
                }
            },
            { data: 'badge_text' },
            { data: 'table_name' },
            { data: 'children_count' },
            {
                data: 'actions',
                orderable: false,
                searchable: false
            }
        ],
        order: [[4, 'asc'], [5, 'asc']], // Order by parent, then order_index
        pageLength: 25
    });

    // Load parent menus and admin tables for dropdowns
    loadDropdownData();

    // Add/Edit menu form submission
    $('#menuForm').on('submit', function(e) {
        e.preventDefault();
        
        const formData = new FormData(this);
        const menuData = {};
        
        for (let [key, value] of formData.entries()) {
            if (key.startsWith('is_')) {
                menuData[key] = value === 'on' ? true : false;
            } else if (key === 'order_index' || key === 'parent_id' || key === 'table_id') {
                menuData[key] = value && value !== '' ? parseInt(value) : null;
            } else {
                menuData[key] = value && value.trim() !== '' ? value.trim() : null;
            }
        }

        // Ensure boolean fields are properly set
        ['is_active', 'is_title', 'is_divider'].forEach(field => {
            if (menuData[field] === undefined) {
                menuData[field] = false;
            }
        });

        const url = isEditMode ? `/admin/menus/${editingMenuId}` : '/admin/menus';
        const method = isEditMode ? 'PUT' : 'POST';

        $.ajax({
            url: url,
            method: method,
            data: menuData,
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    showAlert('success', response.message);
                    $('#menuModal').modal('hide');
                    menusTable.ajax.reload();
                    resetForm();
                } else {
                    showAlert('danger', response.message || 'Operation failed');
                }
            },
            error: function(xhr) {
                const response = xhr.responseJSON;
                showAlert('danger', response?.message || 'Operation failed');
            }
        });
    });

    // Edit menu button click
    $(document).on('click', '.edit-menu', function() {
        if (!USER_PERMISSIONS.canUpdate) {
            alert('Bạn không có quyền chỉnh sửa menu');
            return;
        }
        
        const menuId = $(this).data('id');
        editingMenuId = menuId;
        isEditMode = true;
        
        $('#menuModalTitle').text('Edit Menu');
        
        // Load menu data
        $.get(`/admin/menus/${menuId}`)
            .done(function(response) {
                if (response.success) {
                    const menu = response.data;
                    
                    $('#menuTitle').val(menu.title);
                    $('#menuUrl').val(menu.url || '');
                    $('#menuIcon').val(menu.icon || '');
                    $('#menuParent').val(menu.parent_id || '');
                    $('#menuOrder').val(menu.order_index);
                    $('#menuTarget').val(menu.target || '');
                    $('#badgeText').val(menu.badge_text || '');
                    $('#badgeColor').val(menu.badge_color || '');
                    $('#linkedTable').val(menu.table_id || '');
                    
                    $('#isActive').prop('checked', menu.is_active);
                    $('#isTitle').prop('checked', menu.is_title);
                    $('#isDivider').prop('checked', menu.is_divider);
                    
                    $('#menuModal').modal('show');
                } else {
                    showAlert('danger', response.message || 'Error loading menu');
                }
            })
            .fail(function(xhr) {
                const response = xhr.responseJSON;
                if (xhr.status === 403) {
                    showAlert('danger', response?.message || 'Bạn không có quyền truy cập chi tiết menu này');
                } else {
                    showAlert('danger', 'Error loading menu data');
                }
            });
    });

    // Delete menu button click
    $(document).on('click', '.delete-menu', function() {
        if (!USER_PERMISSIONS.canDelete) {
            alert('Bạn không có quyền xóa menu');
            return;
        }
        
        const menuId = $(this).data('id');
        const menuTitle = $(this).data('title');
        
        if (confirm(`Are you sure you want to delete menu "${menuTitle}"?`)) {
            $.ajax({
                url: `/admin/menus/${menuId}`,
                method: 'DELETE',
                success: function(response) {
                    if (response.success) {
                        showAlert('success', response.message);
                        menusTable.ajax.reload();
                    } else {
                        showAlert('danger', response.message || 'Delete failed');
                    }
                },
                error: function(xhr) {
                    const response = xhr.responseJSON;
                    showAlert('danger', response?.message || 'Delete failed');
                }
            });
        }
    });

    // Reset form when modal is hidden
    $('#menuModal').on('hidden.bs.modal', function() {
        resetForm();
    });

    function resetForm() {
        $('#menuForm')[0].reset();
        $('#menuModalTitle').text('Add New Menu');
        isEditMode = false;
        editingMenuId = null;
        $('#isActive').prop('checked', true);
    }

    function loadDropdownData() {
        // Load parent menus
        $.get('/admin/api/parent-menus', function(response) {
            if (response.success) {
                const select = $('#menuParent');
                select.find('option:not(:first)').remove();
                
                response.data.forEach(menu => {
                    const indent = '&nbsp;'.repeat(menu.level * 4);
                    select.append(`<option value="${menu.id}">${indent}${menu.title}</option>`);
                });
            }
        });

        // Load admin tables
        $.get('/admin/api/admin-tables', function(response) {
            if (response.success) {
                const select = $('#linkedTable');
                select.find('option:not(:first)').remove();
                
                response.data.forEach(table => {
                    select.append(`<option value="${table.id}">${table.display_name} (${table.name})</option>`);
                });
            }
        });
    }

    function showAlert(type, message) {
        const alertHtml = `
            <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                ${message}
                <button type="button" class="close" data-dismiss="alert">
                    <span>&times;</span>
                </button>
            </div>
        `;
        
        $('.alert').remove();
        $('.container-fluid').prepend(alertHtml);
        
        setTimeout(() => {
            $('.alert').fadeOut();
        }, 5000);
    }

    // Global functions for buttons
    window.syncTableMenus = function() {
        if (!USER_PERMISSIONS.isAdmin) {
            alert('Chỉ Admin mới có thể sync table menus');
            return;
        }
        
        if (confirm('This will create menu items for all active admin tables. Continue?')) {
            $.post('/admin/menus/sync-tables', {}, function(response) {
                if (response.success) {
                    showAlert('success', response.message);
                    menusTable.ajax.reload();
                } else {
                    showAlert('danger', response.message || 'Sync failed');
                }
            }).fail(function(xhr) {
                const response = xhr.responseJSON;
                showAlert('danger', response?.message || 'Sync failed');
            });
        }
    };

    window.createDefaultMenus = function() {
        if (!USER_PERMISSIONS.isAdmin) {
            alert('Chỉ Admin mới có thể tạo default menus');
            return;
        }
        
        if (confirm('This will create default menu structure. Continue?')) {
            $.post('/admin/menus/create-defaults', {}, function(response) {
                if (response.success) {
                    showAlert('success', response.message);
                    menusTable.ajax.reload();
                } else {
                    showAlert('danger', response.message || 'Creation failed');
                }
            }).fail(function(xhr) {
                const response = xhr.responseJSON;
                showAlert('danger', response?.message || 'Creation failed');
            });
        }
    };
});
</script>
