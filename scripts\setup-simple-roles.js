const db = require('../config/database');
const permissionService = require('../services/permissionService');

async function setupSimpleRoles() {
  try {
    console.log('🎯 Setting up simple role system (Admin + User + Custom)...');

    // Initialize database connection
    db.connect('development');
    
    // 1. Ensure basic roles exist
    console.log('1. Creating basic roles...');
    
    const basicRoles = [
      {
        name: 'Admin',
        description: 'Administrator v<PERSON><PERSON> quyề<PERSON> quản lý toàn bộ hệ thống'
      },
      {
        name: 'User',
        description: 'User thông thường với quyền cơ bản'
      }
    ];

    for (const roleData of basicRoles) {
      // Check if role exists
      const existingRole = await db.queryOne(`
        SELECT id FROM role WHERE name = ?
      `, [roleData.name]);

      if (!existingRole) {
        await db.query(`
          INSERT INTO role (name) VALUES (?)
        `, [roleData.name]);
        console.log(`✅ Created role: ${roleData.name}`);
      } else {
        console.log(`⚠️ Role already exists: ${roleData.name}`);
      }
    }

    // 2. Get Admin role and grant all permissions
    console.log('2. Granting all permissions to Admin role...');
    
    const adminRole = await db.queryOne(`
      SELECT id FROM role WHERE name = 'Admin'
    `);

    if (!adminRole) {
      throw new Error('Admin role not found');
    }

    // Get all permissions
    const allPermissions = await db.query('SELECT id FROM permissions');
    console.log(`Found ${allPermissions.length} permissions`);

    let grantedCount = 0;
    for (const permission of allPermissions) {
      // Check if Admin already has this permission
      const existingPermission = await db.queryOne(`
        SELECT id FROM role_permissions 
        WHERE role_id = ? AND permission_id = ?
      `, [adminRole.id, permission.id]);

      if (!existingPermission) {
        await db.query(`
          INSERT INTO role_permissions (role_id, permission_id, granted_at)
          VALUES (?, ?, NOW())
        `, [adminRole.id, permission.id]);
        grantedCount++;
      }
    }

    console.log(`✅ Granted ${grantedCount} new permissions to Admin role`);

    // 3. Setup basic permissions for User role
    console.log('3. Setting up basic permissions for User role...');
    
    const userRole = await db.queryOne(`
      SELECT id FROM role WHERE name = 'User'
    `);

    if (!userRole) {
      throw new Error('User role not found');
    }

    // Grant basic permissions to User role
    const basicPermissions = [
      'read_user',
      'edit_user' // Users can edit their own profile
    ];

    let userGrantedCount = 0;
    for (const permissionName of basicPermissions) {
      const permission = await db.queryOne(`
        SELECT id FROM permissions WHERE name = ?
      `, [permissionName]);

      if (permission) {
        // Check if User already has this permission
        const existingPermission = await db.queryOne(`
          SELECT id FROM role_permissions 
          WHERE role_id = ? AND permission_id = ?
        `, [userRole.id, permission.id]);

        if (!existingPermission) {
          await db.query(`
            INSERT INTO role_permissions (role_id, permission_id, granted_at)
            VALUES (?, ?, NOW())
          `, [userRole.id, permission.id]);
          userGrantedCount++;
        }
      }
    }

    console.log(`✅ Granted ${userGrantedCount} basic permissions to User role`);

    // 4. Verify setup
    console.log('4. Verifying setup...');
    
    const roles = await db.query(`
      SELECT 
        r.id,
        r.name,
        COUNT(rp.permission_id) as permission_count
      FROM role r
      LEFT JOIN role_permissions rp ON r.id = rp.role_id
      GROUP BY r.id, r.name
      ORDER BY r.id
    `);

    console.log('\n📋 Current roles:');
    roles.forEach(role => {
      console.log(`   - ${role.name}: ${role.permission_count} permissions`);
    });

    // Show user assignments
    const userAssignments = await db.query(`
      SELECT 
        u.email,
        r.name as role_name
      FROM user_role ur
      INNER JOIN user u ON ur.user_id = u.id
      INNER JOIN role r ON ur.role_id = r.id
      ORDER BY r.name, u.email
    `);

    console.log('\n👥 User role assignments:');
    if (userAssignments.length > 0) {
      userAssignments.forEach(assignment => {
        console.log(`   - ${assignment.email} → ${assignment.role_name}`);
      });
    } else {
      console.log('   (No users assigned to roles yet)');
    }

    console.log('\n🎉 Simple role system setup completed!');
    console.log('\nRole hierarchy:');
    console.log('1. Admin - Full system access');
    console.log('2. User - Basic user permissions');
    console.log('3. Custom roles - Can be created with specific permissions');
    
    console.log('\nNext steps:');
    console.log('- Assign users to appropriate roles');
    console.log('- Create custom roles as needed');
    console.log('- Use permission middleware in routes');

  } catch (error) {
    console.error('❌ Error setting up simple roles:', error);
  }
  
  process.exit(0);
}

setupSimpleRoles(); 