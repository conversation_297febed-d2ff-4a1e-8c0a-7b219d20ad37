const fs = require('fs');
const path = require('path');
const db = require('../config/database');

/**
 * Complete Database Setup Script for Backend CoreUI Project
 * Đ<PERSON>y là script setup database hoàn chỉnh với tất cả các chức năng:
 * - User management system
 * - Role-based permission system
 * - Dynamic CRUD admin system
 * - Menu management system
 * - Session management
 * - Foreign key relationships
 */

/**
 * Add missing columns and data for device management using existing connection
 * Note: Tables user_sessions and user_session_settings are already created in complete_database_schema.sql
 */
async function createMissingTablesInline() {
  console.log('📋 Adding missing columns to user table...');

  // Add missing columns to user table if not exists (for backward compatibility)
  const addColumnsSQL = [
    'ALTER TABLE `user` ADD COLUMN IF NOT EXISTS `jwt_token_id` VARCHAR(255) NULL',
    'ALTER TABLE `user` ADD COLUMN IF NOT EXISTS `device_info` TEXT NULL',
    'ALTER TABLE `user` ADD COLUMN IF NOT EXISTS `token_created_at` TIMESTAMP NULL',
    'ALTER TABLE `user` ADD COLUMN IF NOT EXISTS `last_login` TIMESTAMP NULL'
  ];

  for (const sql of addColumnsSQL) {
    try {
      await db.query(sql);
      console.log('✅ Added column successfully');
    } catch (error) {
      if (error.message.includes('Duplicate column')) {
        console.log('⚠️  Column already exists, skipping...');
      } else {
        console.log(`❌ Error adding column: ${error.message}`);
      }
    }
  }

  console.log('📋 Ensuring session settings exist for all users...');

  // Ensure session settings exist for all users (data is already inserted in SQL file)
  try {
    const users = await db.query('SELECT id FROM user');
    const settings = await db.query('SELECT user_id FROM user_session_settings');
    const existingUserIds = settings.map(s => s.user_id);

    let addedCount = 0;
    for (const user of users) {
      if (!existingUserIds.includes(user.id)) {
        try {
          await db.query(`
            INSERT INTO user_session_settings
            (user_id, max_sessions, session_timeout_hours, allow_multiple_devices, notify_new_login, auto_logout_inactive, inactive_timeout_hours, require_2fa_new_device)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
          `, [user.id, 5, 24, 1, 1, 1, 72, 0]);
          addedCount++;
        } catch (error) {
          if (!error.message.includes('Duplicate entry')) {
            console.log(`⚠️  Could not add settings for user ${user.id}: ${error.message}`);
          }
        }
      }
    }

    if (addedCount > 0) {
      console.log(`✅ Added session settings for ${addedCount} users`);
    } else {
      console.log('✅ All users already have session settings');
    }
  } catch (error) {
    console.log(`⚠️  Error checking session settings: ${error.message}`);
  }
}

async function runDatabaseSetup() {
  try {
    console.log('🚀 Starting complete database setup...');
    console.log('📋 This will create all tables, relationships, and sample data');

    // Ensure database exists first
    await ensureDatabaseExists();

    // Connect to database
    db.connect('development');
    console.log('✅ Connected to database');

    // Check if database is empty or has existing data
    await checkExistingData();

    // Read and execute SQL file
    const sqlFilePath = path.join(__dirname, '../database/complete_database_schema.sql');

    if (!fs.existsSync(sqlFilePath)) {
      throw new Error(`SQL file not found: ${sqlFilePath}`);
    }

    const sqlContent = fs.readFileSync(sqlFilePath, 'utf8');
    console.log('📖 Read SQL file successfully');

    // Improved SQL statement parsing
    const statements = parseSQL(sqlContent);
    console.log(`📝 Found ${statements.length} SQL statements to execute`);

    // Execute statements with improved error handling
    const result = await executeStatements(statements);

    console.log('\n📊 Database Setup Summary:');
    console.log(`✅ Successful statements: ${result.successCount}`);
    console.log(`❌ Failed statements: ${result.errorCount}`);
    console.log(`📈 Success rate: ${result.successRate}%`);

    if (result.errorCount > 0) {
      console.log('\n⚠️  Some statements failed. Check the errors above.');
      console.log('💡 This might be normal if tables already exist.');
    }



    // Comprehensive verification
    await verifyDatabaseSetup();

    // Test relationships and foreign keys
    await testRelationships();

    // Test admin system functionality
    await testAdminSystem();

    // Create missing tables for device management
    console.log('\n🔧 Creating missing tables for device management...');
    try {
      await createMissingTablesInline();
      console.log('✅ Missing tables created successfully');
    } catch (error) {
      console.log('⚠️  Error creating missing tables:', error.message);
      console.log('💡 You can manually create missing tables later if needed');
    }

  } catch (error) {
    console.error('💥 Database setup failed:', error);
    process.exit(1);
  } finally {
    // Close database connection properly (if not already closed)
    try {
      const pool = db.get();
      if (pool && !pool._closed) {
        pool.end();
        console.log('🔌 Database connection closed');
      }
    } catch (error) {
      console.log('⚠️  Database connection already closed');
    }
  }
}

/**
 * Parse SQL content into individual statements
 * Handles multi-line statements and comments properly
 */
function parseSQL(sqlContent) {
  // Remove comments and normalize line endings
  const cleanContent = sqlContent
    .replace(/--.*$/gm, '') // Remove single line comments
    .replace(/\/\*[\s\S]*?\*\//g, '') // Remove multi-line comments
    .replace(/\r\n/g, '\n')
    .replace(/\r/g, '\n');

  // Split by semicolon but be careful with strings and functions
  const statements = [];
  let currentStatement = '';
  let inString = false;
  let stringChar = '';

  for (let i = 0; i < cleanContent.length; i++) {
    const char = cleanContent[i];

    if (!inString && (char === '"' || char === "'")) {
      inString = true;
      stringChar = char;
    } else if (inString && char === stringChar && cleanContent[i - 1] !== '\\') {
      inString = false;
      stringChar = '';
    } else if (!inString && char === ';') {
      const statement = currentStatement.trim();
      if (statement.length > 0) {
        statements.push(statement);
      }
      currentStatement = '';
      continue;
    }

    currentStatement += char;
  }

  // Add the last statement if it doesn't end with semicolon
  const lastStatement = currentStatement.trim();
  if (lastStatement.length > 0) {
    statements.push(lastStatement);
  }

  return statements.filter(stmt =>
    stmt.length > 0 &&
    !stmt.match(/^\s*$/) &&
    !stmt.startsWith('--') &&
    !stmt.startsWith('/*')
  );
}

/**
 * Ensure database exists, create if not
 */
async function ensureDatabaseExists() {
  const mysql = require('mysql2');
  const dotenv = require('dotenv');
  dotenv.config();

  const { DB_USER, DB_PASSWORD, DB_NAME, DB_HOST = 'localhost' } = process.env;

  console.log('\n🔍 Checking if database exists...');

  // Create connection without database name
  const connection = mysql.createConnection({
    host: DB_HOST,
    user: DB_USER,
    password: DB_PASSWORD
  });

  try {
    // Check if database exists
    const [databases] = await connection.promise().query('SHOW DATABASES LIKE ?', [DB_NAME]);

    if (databases.length === 0) {
      console.log(`📝 Database '${DB_NAME}' does not exist, creating...`);
      await connection.promise().query(`CREATE DATABASE \`${DB_NAME}\` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci`);
      console.log(`✅ Database '${DB_NAME}' created successfully`);
    } else {
      console.log(`✅ Database '${DB_NAME}' already exists`);
    }
  } catch (error) {
    console.error('❌ Error checking/creating database:', error.message);
    throw error;
  } finally {
    connection.end();
  }
}

/**
 * Check if database has existing data
 */
async function checkExistingData() {
  console.log('\n🔍 Checking existing database state...');

  try {
    const tables = await db.query('SHOW TABLES');
    if (tables.length > 0) {
      console.log(`⚠️  Found ${tables.length} existing tables`);
      console.log('📝 This setup will drop and recreate all tables');

      // List existing tables
      const tableNames = tables.map(row => Object.values(row)[0]);
      console.log(`📋 Existing tables: ${tableNames.join(', ')}`);
    } else {
      console.log('✅ Database is empty, ready for setup');
    }
  } catch (error) {
    console.log('⚠️  Could not check existing tables:', error.message);
  }
}

/**
 * Execute SQL statements with improved error handling
 */
async function executeStatements(statements) {
  let successCount = 0;
  let errorCount = 0;
  const errors = [];

  console.log('\n⚡ Executing SQL statements...');

  for (let i = 0; i < statements.length; i++) {
    const statement = statements[i];

    try {
      await db.query(statement);
      successCount++;

      // Log progress every 10 statements
      if ((i + 1) % 10 === 0) {
        console.log(`⏳ Executed ${i + 1}/${statements.length} statements...`);
      }

      // Log important operations
      if (statement.toUpperCase().includes('CREATE TABLE')) {
        const tableName = statement.match(/CREATE TABLE\s+`?(\w+)`?/i)?.[1];
        console.log(`📋 Created table: ${tableName}`);
      } else if (statement.toUpperCase().includes('INSERT INTO')) {
        const tableName = statement.match(/INSERT INTO\s+`?(\w+)`?/i)?.[1];
        if (tableName && (i + 1) % 5 === 0) {
          console.log(`📝 Inserting data into: ${tableName}`);
        }
      }

    } catch (error) {
      errorCount++;
      const errorInfo = {
        statementIndex: i + 1,
        statement: statement.substring(0, 100) + '...',
        error: error.message
      };
      errors.push(errorInfo);

      console.error(`❌ Error executing statement ${i + 1}:`, error.message);

      // Stop on critical errors
      if (error.message.includes('syntax error') ||
          error.message.includes('Access denied') ||
          error.message.includes('Unknown database')) {
        console.error('🛑 Critical error detected, stopping execution');
        throw error;
      }
    }
  }

  const successRate = ((successCount / (successCount + errorCount)) * 100).toFixed(1);

  return {
    successCount,
    errorCount,
    successRate,
    errors
  };
}

/**
 * Comprehensive database verification
 */
async function verifyDatabaseSetup() {
  console.log('\n🔍 Verifying database setup...');

  const verificationQueries = [
    { name: 'Users', query: 'SELECT COUNT(*) as count FROM user', expected: 4 },
    { name: 'Roles', query: 'SELECT COUNT(*) as count FROM role', expected: 4 },
    { name: 'Permissions', query: 'SELECT COUNT(*) as count FROM permissions', expected: 25 },
    { name: 'Role Permissions', query: 'SELECT COUNT(*) as count FROM role_permissions', expected: 20 },
    { name: 'User Roles', query: 'SELECT COUNT(*) as count FROM role_user', expected: 4 },
    { name: 'Admin Tables', query: 'SELECT COUNT(*) as count FROM admintable', expected: 7 },
    { name: 'Admin Columns', query: 'SELECT COUNT(*) as count FROM admincolumn', expected: 30 },
    { name: 'Admin Relations', query: 'SELECT COUNT(*) as count FROM adminrelation', expected: 2 },
    { name: 'Admin Menus', query: 'SELECT COUNT(*) as count FROM admin_menus', expected: 8 },
    { name: 'User Sessions', query: 'SELECT COUNT(*) as count FROM user_sessions', expected: 0 }
  ];

  let verificationPassed = 0;
  let verificationFailed = 0;

  for (const verification of verificationQueries) {
    try {
      const result = await db.query(verification.query);
      const count = result[0].count;

      if (count >= verification.expected) {
        console.log(`✅ ${verification.name}: ${count} records (expected: ${verification.expected})`);
        verificationPassed++;
      } else {
        console.log(`⚠️  ${verification.name}: ${count} records (expected: ${verification.expected})`);
        verificationFailed++;
      }
    } catch (error) {
      console.log(`❌ ${verification.name}: Error - ${error.message}`);
      verificationFailed++;
    }
  }

  console.log(`\n📊 Verification Summary: ${verificationPassed} passed, ${verificationFailed} failed`);

  if (verificationFailed === 0) {
    console.log('🎉 All verifications passed!');
  } else {
    console.log('⚠️  Some verifications failed. Check the setup.');
  }
}

/**
 * Test foreign key relationships
 */
async function testRelationships() {
  console.log('\n🔗 Testing foreign key relationships...');

  try {
    // Test user-role relationship
    const userRoles = await db.query(`
      SELECT u.fullname, r.name as role_name
      FROM user u
      INNER JOIN role_user ru ON u.id = ru.user_id
      INNER JOIN role r ON ru.role_id = r.id
      LIMIT 3
    `);

    if (userRoles.length > 0) {
      console.log('✅ User-Role relationships working');
      userRoles.forEach(ur => {
        console.log(`   - ${ur.fullname} → ${ur.role_name}`);
      });
    } else {
      console.log('⚠️  No user-role relationships found');
    }

    // Test role-permission relationship
    const rolePermissions = await db.query(`
      SELECT r.name as role_name, COUNT(rp.permission_id) as permission_count
      FROM role r
      LEFT JOIN role_permissions rp ON r.id = rp.role_id
      GROUP BY r.id, r.name
    `);

    if (rolePermissions.length > 0) {
      console.log('✅ Role-Permission relationships working');
      rolePermissions.forEach(rp => {
        console.log(`   - ${rp.role_name}: ${rp.permission_count} permissions`);
      });
    } else {
      console.log('⚠️  No role-permission relationships found');
    }

    // Test admin table relationships
    const adminRelations = await db.query(`
      SELECT ar.*, t.name as table_name, ft.name as foreign_table_name, c.name as column_name
      FROM adminrelation ar
      LEFT JOIN admintable t ON ar.table_id = t.id
      LEFT JOIN admintable ft ON ar.foreign_table_id = ft.id
      LEFT JOIN admincolumn c ON ar.column_id = c.id
    `);

    if (adminRelations.length > 0) {
      console.log('✅ Admin table relationships working');
      adminRelations.forEach(ar => {
        console.log(`   - ${ar.table_name}.${ar.column_name} → ${ar.foreign_table_name}.${ar.foreign_column}`);
      });
    } else {
      console.log('⚠️  No admin table relationships found');
    }

  } catch (error) {
    console.log('❌ Error testing relationships:', error.message);
  }
}

/**
 * Test admin system functionality
 */
async function testAdminSystem() {
  console.log('\n🛠️  Testing admin system functionality...');

  try {
    // Test admin user login
    console.log('\n👤 Testing admin user...');
    const adminUser = await db.query('SELECT id, email, fullname FROM user WHERE email = ?', ['<EMAIL>']);

    if (adminUser.length > 0) {
      console.log(`✅ Admin user found: ${adminUser[0].fullname} (${adminUser[0].email})`);

      // Check admin role
      const adminRole = await db.query(`
        SELECT r.name
        FROM role r
        INNER JOIN role_user ru ON r.id = ru.role_id
        WHERE ru.user_id = ?
      `, [adminUser[0].id]);

      if (adminRole.length > 0) {
        console.log(`✅ Admin role assigned: ${adminRole[0].name}`);
      } else {
        console.log('⚠️  Admin role not assigned');
      }
    } else {
      console.log('❌ Admin user not found');
    }

    // Test menu system
    console.log('\n📋 Testing menu system...');
    const menus = await db.query(`
      SELECT m.title, m.url, m.icon, t.name as table_name
      FROM admin_menus m
      LEFT JOIN admintable t ON m.table_id = t.id
      WHERE m.is_active = 1
      ORDER BY m.order_index
    `);

    if (menus.length > 0) {
      console.log(`✅ Menu system working: ${menus.length} menu items`);
      menus.forEach(menu => {
        const tableInfo = menu.table_name ? ` (table: ${menu.table_name})` : '';
        console.log(`   - ${menu.title}: ${menu.url || 'No URL'}${tableInfo}`);
      });
    } else {
      console.log('⚠️  No menu items found');
    }

    // Test admin table configuration
    console.log('\n🗂️  Testing admin table configuration...');
    const adminTables = await db.query(`
      SELECT at.name, at.display_name, COUNT(ac.id) as column_count
      FROM admintable at
      LEFT JOIN admincolumn ac ON at.id = ac.table_id
      WHERE at.is_active = 1
      GROUP BY at.id, at.name, at.display_name
      ORDER BY at.order_index
    `);

    if (adminTables.length > 0) {
      console.log(`✅ Admin tables configured: ${adminTables.length} tables`);
      adminTables.forEach(table => {
        console.log(`   - ${table.display_name} (${table.name}): ${table.column_count} columns`);
      });
    } else {
      console.log('⚠️  No admin tables configured');
    }

    // Test permissions
    console.log('\n🔐 Testing permission system...');
    const permissionStats = await db.query(`
      SELECT
        COUNT(DISTINCT p.id) as total_permissions,
        COUNT(DISTINCT rp.role_id) as roles_with_permissions,
        COUNT(DISTINCT p.table_name) as tables_with_permissions
      FROM permissions p
      LEFT JOIN role_permissions rp ON p.id = rp.permission_id
    `);

    if (permissionStats.length > 0) {
      const stats = permissionStats[0];
      console.log(`✅ Permission system: ${stats.total_permissions} permissions, ${stats.roles_with_permissions} roles, ${stats.tables_with_permissions} tables`);
    } else {
      console.log('⚠️  Permission system not configured');
    }

  } catch (error) {
    console.log('❌ Error testing admin system:', error.message);
  }
}

/**
 * Display final setup information
 */
function displaySetupInfo() {
  console.log('\n🔑 Sample Login Credentials:');
  console.log('==========================================');
  console.log('👑 ADMIN USER:');
  console.log('   Email: <EMAIL>');
  console.log('   Password: password123');
  console.log('   Role: Admin (full access)');
  console.log('');
  console.log('👨‍💼 MANAGER USER:');
  console.log('   Email: <EMAIL>');
  console.log('   Password: password123');
  console.log('   Role: Manager (limited access)');
  console.log('');
  console.log('✏️  EDITOR USER:');
  console.log('   Email: <EMAIL>');
  console.log('   Password: password123');
  console.log('   Role: Editor (content management)');
  console.log('');
  console.log('👤 REGULAR USER:');
  console.log('   Email: <EMAIL>');
  console.log('   Password: password123');
  console.log('   Role: User (basic access)');

  console.log('\n🚀 Next Steps:');
  console.log('==========================================');
  console.log('1. Start the application: npm start');
  console.log('2. Open browser: http://localhost:3000/admin');
  console.log('3. Login with admin credentials above');
  console.log('4. Navigate to System Management → Tables to manage database structure');
  console.log('5. Navigate to User Management to manage users and roles');
  console.log('6. Use the dynamic CRUD system to manage your data');

  console.log('\n📋 Available Features:');
  console.log('==========================================');
  console.log('✅ User Management System');
  console.log('✅ Role-Based Permission System');
  console.log('✅ Dynamic CRUD Admin Interface');
  console.log('✅ Database Table Management');
  console.log('✅ Foreign Key Relationships');
  console.log('✅ Dynamic Menu System');
  console.log('✅ Session Management');
  console.log('✅ File Upload Support');
  console.log('✅ Search & Pagination');
  console.log('✅ Data Validation');

  console.log('\n🎉 Database setup completed successfully!');
  console.log('Your Laravel Voyager-like admin system is ready to use!');
}

// Run the setup
if (require.main === module) {
  runDatabaseSetup()
    .then(() => {
      displaySetupInfo();
      console.log('\n✨ Setup script completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 Setup script failed:', error);
      console.error('\n🔧 Troubleshooting:');
      console.error('1. Check your database connection settings in .env file');
      console.error('2. Make sure MySQL server is running');
      console.error('3. Verify database user has CREATE/DROP privileges');
      console.error('4. Check if database exists and is accessible');
      process.exit(1);
    });
}

module.exports = { runDatabaseSetup };
