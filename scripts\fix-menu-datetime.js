// Script để sửa lỗi datetime trong admin_menus
const db = require('../config/database');

async function fixMenuDateTime() {
  try {
    console.log('Fixing menu datetime fields...');
    
    // Update created_at and updated_at fields for existing menus
    const result = await db.query(`
      UPDATE admin_menus 
      SET created_at = NOW(), updated_at = NOW() 
      WHERE created_at IS NULL OR updated_at IS NULL
    `);
    
    console.log(`Updated ${result.affectedRows} menu records`);
    
    // Check current menu data
    const menus = await db.query(`
      SELECT id, title, created_at, updated_at 
      FROM admin_menus 
      ORDER BY id
    `);
    
    console.log('\nCurrent menu data:');
    menus.forEach(menu => {
      console.log(`- ${menu.title}: created=${menu.created_at}, updated=${menu.updated_at}`);
    });
    
  } catch (error) {
    console.error('Error fixing menu datetime:', error);
  }
}

fixMenuDateTime();

module.exports = { fixMenuDateTime };
