
<style>
  .form-card {
    border-radius: 0.5rem;
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
  }
  .form-header {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    color: white;
    border-radius: 0.5rem 0.5rem 0 0;
    padding: 1.5rem;
  }
  .form-body {
    padding: 2rem;
  }
  .action-preview {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    padding: 1rem;
    margin-top: 1rem;
  }
  .preview-permission {
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    color: #495057;
    background: #fff;
    padding: 0.5rem;
    border-radius: 0.25rem;
    border: 1px solid #ced4da;
    margin-top: 0.5rem;
  }
  .help-text {
    font-size: 0.875rem;
    color: #6c757d;
    margin-top: 0.25rem;
  }
  .action-badges {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
    margin-top: 0.5rem;
  }
  .action-badge {
    padding: 0.375rem 0.75rem;
    border-radius: 0.25rem;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
  }
  .action-badge:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  }
</style>

<div class="container-fluid">
  <!-- Header -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="d-flex justify-content-between align-items-center">
        <div>
          <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-shield-alt text-primary me-2"></i>
            <%= permission ? 'Chỉnh sửa Permission' : 'Tạo Permission Mới' %>
          </h1>
          <p class="text-muted mb-0">
            <%= permission ? 'Cập nhật thông tin permission' : 'Thêm permission mới vào hệ thống' %>
          </p>
        </div>
        <div>
          <a href="/admin/permissions" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-1"></i>Quay lại
          </a>
        </div>
      </div>
    </div>
  </div>

  <!-- Form -->
  <div class="row justify-content-center">
    <div class="col-lg-8">
      <div class="card form-card">
        <div class="form-header">
          <h5 class="mb-0">
            <i class="fas fa-edit me-2"></i>
            Thông tin Permission
          </h5>
        </div>
        <div class="form-body">
          <form id="permissionForm">
            <!-- Tên Permission -->
            <div class="mb-4">
              <label for="name" class="form-label">
                <i class="fas fa-tag text-primary me-1"></i>
                Tên Permission <span class="text-danger">*</span>
              </label>
              <input type="text" 
                     class="form-control" 
                     id="name" 
                     name="name" 
                     value="<%= permission ? permission.name : '' %>"
                     <%= permission ? 'readonly' : '' %>
                                            placeholder="vd: read_users, edit_posts, delete_comments"
                     required>
              <div class="help-text">
                Tên duy nhất của permission. Không thể thay đổi sau khi tạo.
              </div>
            </div>

            <!-- Tên hiển thị -->
            <div class="mb-4">
              <label for="display_name" class="form-label">
                <i class="fas fa-eye text-info me-1"></i>
                Tên hiển thị <span class="text-danger">*</span>
              </label>
              <input type="text" 
                     class="form-control" 
                     id="display_name" 
                     name="display_name" 
                     value="<%= permission ? permission.display_name : '' %>"
                                            placeholder="vd: Read Users, Edit Posts, Delete Comments"
                     required>
              <div class="help-text">
                Tên hiển thị thân thiện với người dùng.
              </div>
            </div>

            <!-- Bảng -->
            <div class="row mb-4">
              <div class="col-md-6">
                <label for="table_name" class="form-label">
                  <i class="fas fa-table text-success me-1"></i>
                  Bảng
                </label>
                <select class="form-select" id="table_name" name="table_name">
                  <option value="">Chọn bảng (System nếu để trống)</option>
                  <% tables.forEach(table => { %>
                    <option value="<%= table.name %>" 
                            <%= permission && permission.table_name === table.name ? 'selected' : '' %>>
                      <%= table.display_name %> (<%= table.name %>)
                    </option>
                  <% }) %>
                </select>
                <div class="help-text">
                  Bảng mà permission này áp dụng.
                </div>
              </div>

              <div class="col-md-6">
                <label for="action" class="form-label">
                  <i class="fas fa-bolt text-warning me-1"></i>
                  Action <span class="text-danger">*</span>
                </label>
                <select class="form-select" id="action" name="action" required>
                  <option value="">Chọn action</option>
                  <% actions.forEach(action => { %>
                    <option value="<%= action %>" 
                            <%= permission && permission.action === action ? 'selected' : '' %>>
                      <%= action %>
                    </option>
                  <% }) %>
                </select>
                <div class="help-text">
                  Hành động mà permission này cho phép.
                </div>
              </div>
            </div>

            <!-- Quick Actions -->
            <div class="mb-4">
              <label class="form-label">
                <i class="fas fa-magic text-purple me-1"></i>
                Quick Actions
              </label>
              <div class="action-badges">
                <span class="action-badge bg-info text-white" onclick="selectAction('read')">
                  Read - Xem dữ liệu
                </span>
                <span class="action-badge bg-warning text-white" onclick="selectAction('edit')">
                  Edit - Chỉnh sửa
                </span>
                <span class="action-badge bg-success text-white" onclick="selectAction('add')">
                  Add - Thêm mới
                </span>
                <span class="action-badge bg-danger text-white" onclick="selectAction('delete')">
                  Delete - Xóa
                </span>
              </div>
              <div class="help-text">
                Nhấp vào các action phổ biến để chọn nhanh.
              </div>
            </div>

            <!-- Mô tả -->
            <div class="mb-4">
              <label for="description" class="form-label">
                <i class="fas fa-align-left text-secondary me-1"></i>
                Mô tả
              </label>
              <textarea class="form-control" 
                        id="description" 
                        name="description" 
                        rows="3"
                        placeholder="Mô tả chi tiết về permission này và chức năng của nó"><%= permission ? (permission.description || '') : '' %></textarea>
              <div class="help-text">
                Mô tả chi tiết giúp quản trị viên hiểu rõ mục đích của permission.
              </div>
            </div>

            <!-- Preview -->
            <div class="action-preview">
              <h6 class="mb-2">
                <i class="fas fa-eye me-1"></i>
                Preview Permission
              </h6>
              <div class="preview-permission" id="permissionPreview">
                <%= permission ? permission.name : 'action_table_name' %>
              </div>
              <small class="text-muted">Tên permission sẽ được tạo tự động dựa trên action và table</small>
            </div>

            <!-- Buttons -->
            <div class="row mt-4">
              <div class="col-12">
                <div class="d-flex justify-content-between">
                  <a href="/admin/permissions" class="btn btn-secondary">
                    <i class="fas fa-times me-1"></i>Hủy
                  </a>
                  <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save me-1"></i>
                    <%= permission ? 'Cập nhật' : 'Tạo mới' %>
                  </button>
                </div>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
// Auto-generate permission name
function updatePermissionName() {
  const action = document.getElementById('action').value;
  const tableName = document.getElementById('table_name').value;
  
  if (action) {
    let permissionName = action;
    if (tableName) {
      permissionName += '_' + tableName;
    }
    
         // Only update if not editing existing permission
     <% if (!permission) { %>
     document.getElementById('name').value = permissionName;
     <% } %>
    
    document.getElementById('permissionPreview').textContent = permissionName;
  }
}

// Auto-generate display name
function updateDisplayName() {
  const action = document.getElementById('action').value;
  const tableName = document.getElementById('table_name').value;
  const tableSelect = document.getElementById('table_name');
  
  if (action && !document.getElementById('display_name').value) {
    let displayName = action.charAt(0).toUpperCase() + action.slice(1);
    
    if (tableName) {
      const selectedOption = tableSelect.options[tableSelect.selectedIndex];
      const tableDisplayName = selectedOption.text.split('(')[0].trim();
      displayName += ' ' + tableDisplayName;
    }
    
    document.getElementById('display_name').value = displayName;
  }
}

// Auto-generate description
function updateDescription() {
  const action = document.getElementById('action').value;
  const tableName = document.getElementById('table_name').value;
  const tableSelect = document.getElementById('table_name');
  
  if (action && !document.getElementById('description').value) {
    const actionDescriptions = {
      read: 'Quyền xem',
      edit: 'Quyền chỉnh sửa',
      add: 'Quyền tạo mới',
      delete: 'Quyền xóa'
    };
    
    let description = actionDescriptions[action] || `Quyền ${action}`;
    
    if (tableName) {
      const selectedOption = tableSelect.options[tableSelect.selectedIndex];
      const tableDisplayName = selectedOption.text.split('(')[0].trim();
      description += ' ' + tableDisplayName;
    }
    
    document.getElementById('description').value = description;
  }
}

// Select action function
function selectAction(action) {
  document.getElementById('action').value = action;
  updatePermissionName();
  updateDisplayName();
  updateDescription();
}

// Event listeners
document.getElementById('action').addEventListener('change', function() {
  updatePermissionName();
  updateDisplayName();
  updateDescription();
});

document.getElementById('table_name').addEventListener('change', function() {
  updatePermissionName();
  updateDisplayName();
  updateDescription();
});

// Form submission
document.getElementById('permissionForm').addEventListener('submit', function(e) {
  e.preventDefault();
  
  const formData = new FormData(this);
  const data = Object.fromEntries(formData.entries());
  
     const url = '<%= permission ? `/admin/permissions/${permission.id}` : "/admin/permissions" %>';
   const method = '<%= permission ? "PUT" : "POST" %>';
  
  const submitBtn = document.querySelector('button[type="submit"]');
  const originalText = submitBtn.innerHTML;
  submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Đang xử lý...';
  submitBtn.disabled = true;
  
  fetch(url, {
    method: method,
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(data)
  })
  .then(response => response.json())
  .then(data => {
    if (data.success) {
      showAlert('success', data.message);
      setTimeout(() => {
        window.location.href = '/admin/permissions';
      }, 1500);
    } else {
      showAlert('danger', data.message);
      submitBtn.innerHTML = originalText;
      submitBtn.disabled = false;
    }
  })
  .catch(error => {
    showAlert('danger', 'Có lỗi xảy ra khi xử lý yêu cầu');
    submitBtn.innerHTML = originalText;
    submitBtn.disabled = false;
  });
});

// Alert function
function showAlert(type, message) {
  const alertDiv = document.createElement('div');
  alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
  alertDiv.style.top = '20px';
  alertDiv.style.right = '20px';
  alertDiv.style.zIndex = '9999';
  alertDiv.innerHTML = `
    ${message}
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
  `;
  document.body.appendChild(alertDiv);
  
  setTimeout(() => {
    if (alertDiv.parentNode) {
      alertDiv.parentNode.removeChild(alertDiv);
    }
  }, 5000);
}

// Initialize preview
updatePermissionName();
</script> 