const mysql = require('mysql2');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

const { DB_USER, DB_PASSWORD, DB_NAME, DB_HOST = 'localhost' } = process.env;

console.log('Testing database connection...');
console.log('Host:', DB_HOST);
console.log('User:', DB_USER);
console.log('Database:', DB_NAME);

async function testConnection() {
  try {
    // Test connection without database first
    console.log('\n1. Testing connection without database...');
    const connection = mysql.createConnection({
      host: DB_HOST,
      user: DB_USER,
      password: DB_PASSWORD
    });

    await connection.promise().query('SELECT 1');
    console.log('✅ Basic connection successful');

    // Check if database exists
    console.log('\n2. Checking if database exists...');
    const [databases] = await connection.promise().query('SHOW DATABASES LIKE ?', [DB_NAME]);
    
    if (databases.length === 0) {
      console.log(`Database '${DB_NAME}' does not exist, creating...`);
      await connection.promise().query(`CREATE DATABASE \`${DB_NAME}\` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci`);
      console.log(`✅ Database '${DB_NAME}' created successfully`);
    } else {
      console.log(`✅ Database '${DB_NAME}' already exists`);
    }

    connection.end();

    // Test connection with database
    console.log('\n3. Testing connection with database...');
    const dbConnection = mysql.createConnection({
      host: DB_HOST,
      user: DB_USER,
      password: DB_PASSWORD,
      database: DB_NAME
    });

    await dbConnection.promise().query('SELECT 1');
    console.log('✅ Database connection successful');

    dbConnection.end();
    console.log('\n🎉 All tests passed!');

  } catch (error) {
    console.error('❌ Connection failed:', error.message);
    process.exit(1);
  }
}

testConnection();
