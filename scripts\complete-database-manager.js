#!/usr/bin/env node

/**
 * Complete Database Manager for Backend CoreUI Project
 * Quản lý toàn bộ database với các chức năng:
 * - Setup database hoàn chỉnh
 * - Verify tính toàn vẹn
 * - Backup và restore
 * - Reset database
 */

const { runDatabaseSetup } = require('./run-database-setup');
const { verifyDatabaseIntegrity } = require('./verify-database-integrity');
const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function colorize(text, color) {
  return `${colors[color]}${text}${colors.reset}`;
}

function showHelp() {
  console.log(colorize('\n🗄️  Complete Database Manager - Backend CoreUI', 'cyan'));
  console.log(colorize('='.repeat(50), 'blue'));
  
  console.log('\n📋 Available Commands:');
  console.log('  setup     - Setup database hoàn chỉnh với tất cả bảng và dữ liệu mẫu');
  console.log('  verify    - Kiểm tra tính toàn vẹn database');
  console.log('  backup    - Backup database hiện tại');
  console.log('  restore   - Restore database từ backup');
  console.log('  reset     - Reset database (backup + setup mới)');
  console.log('  status    - Hiển thị trạng thái database');
  console.log('  help      - Hiển thị hướng dẫn này');
  
  console.log('\n🚀 Usage:');
  console.log('  node scripts/complete-database-manager.js <command>');
  
  console.log('\n📝 Examples:');
  console.log('  node scripts/complete-database-manager.js setup');
  console.log('  node scripts/complete-database-manager.js verify');
  console.log('  node scripts/complete-database-manager.js backup');
  console.log('  node scripts/complete-database-manager.js reset');
  
  console.log('\n⚠️  Important Notes:');
  console.log('  - Luôn backup trước khi chạy setup hoặc reset');
  console.log('  - Chỉ sử dụng trong môi trường development');
  console.log('  - Đảm bảo MySQL server đang chạy');
  console.log('  - Kiểm tra file .env có cấu hình database đúng');
  
  console.log('\n🔑 Default Login Credentials:');
  console.log('  Admin: <EMAIL> / password123');
  console.log('  Manager: <EMAIL> / password123');
  console.log('  Editor: <EMAIL> / password123');
  console.log('  User: <EMAIL> / password123\n');
}

async function backupDatabase() {
  console.log(colorize('💾 Starting database backup...', 'yellow'));
  
  const dotenv = require('dotenv');
  dotenv.config();
  
  const { DB_USER, DB_PASSWORD, DB_NAME, DB_HOST = 'localhost' } = process.env;
  
  if (!DB_USER || !DB_NAME) {
    throw new Error('Missing database configuration in .env file');
  }
  
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19);
  const backupDir = path.join(__dirname, '../backups');
  const backupFile = path.join(backupDir, `${DB_NAME}_backup_${timestamp}.sql`);
  
  // Create backups directory if not exists
  if (!fs.existsSync(backupDir)) {
    fs.mkdirSync(backupDir, { recursive: true });
    console.log('📁 Created backups directory');
  }
  
  try {
    const passwordArg = DB_PASSWORD ? `-p${DB_PASSWORD}` : '';
    const command = `mysqldump -h ${DB_HOST} -u ${DB_USER} ${passwordArg} ${DB_NAME}`;
    
    console.log('⏳ Creating backup...');
    const backup = execSync(command, { encoding: 'utf8' });
    
    fs.writeFileSync(backupFile, backup);
    console.log(colorize(`✅ Backup created: ${backupFile}`, 'green'));
    
    // Show backup info
    const stats = fs.statSync(backupFile);
    console.log(`📊 Backup size: ${(stats.size / 1024 / 1024).toFixed(2)} MB`);
    
    return backupFile;
  } catch (error) {
    console.error(colorize(`❌ Backup failed: ${error.message}`, 'red'));
    throw error;
  }
}

async function restoreDatabase(backupFile) {
  console.log(colorize(`🔄 Restoring database from: ${backupFile}`, 'yellow'));
  
  if (!fs.existsSync(backupFile)) {
    throw new Error(`Backup file not found: ${backupFile}`);
  }
  
  const dotenv = require('dotenv');
  dotenv.config();
  
  const { DB_USER, DB_PASSWORD, DB_NAME, DB_HOST = 'localhost' } = process.env;
  
  try {
    const passwordArg = DB_PASSWORD ? `-p${DB_PASSWORD}` : '';
    const command = `mysql -h ${DB_HOST} -u ${DB_USER} ${passwordArg} ${DB_NAME}`;
    
    console.log('⏳ Restoring database...');
    execSync(`${command} < "${backupFile}"`, { stdio: 'inherit' });
    
    console.log(colorize('✅ Database restored successfully', 'green'));
  } catch (error) {
    console.error(colorize(`❌ Restore failed: ${error.message}`, 'red'));
    throw error;
  }
}

async function showDatabaseStatus() {
  console.log(colorize('📊 Database Status', 'cyan'));
  console.log(colorize('='.repeat(30), 'blue'));
  
  const db = require('../config/database');
  
  try {
    db.connect('development');
    
    // Get table count and sizes
    const tables = await db.query('SHOW TABLES');
    console.log(`📋 Tables: ${tables.length}`);
    
    // Get record counts for main tables
    const mainTables = ['user', 'role', 'permissions', 'role_user', 'role_permissions', 'admintable'];
    
    for (const table of mainTables) {
      try {
        const result = await db.query(`SELECT COUNT(*) as count FROM ${table}`);
        console.log(`   - ${table}: ${result[0].count} records`);
      } catch (error) {
        console.log(`   - ${table}: Error - ${error.message}`);
      }
    }
    
    // Check database size
    const sizeResult = await db.query(`
      SELECT 
        ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS 'DB Size (MB)'
      FROM information_schema.tables 
      WHERE table_schema = DATABASE()
    `);
    
    if (sizeResult.length > 0) {
      console.log(`💾 Database size: ${sizeResult[0]['DB Size (MB)']} MB`);
    }
    
    // Check last backup
    const backupDir = path.join(__dirname, '../backups');
    if (fs.existsSync(backupDir)) {
      const backups = fs.readdirSync(backupDir)
        .filter(file => file.endsWith('.sql'))
        .sort()
        .reverse();
      
      if (backups.length > 0) {
        console.log(`💾 Last backup: ${backups[0]}`);
      } else {
        console.log('💾 No backups found');
      }
    } else {
      console.log('💾 No backup directory');
    }
    
    const pool = db.get();
    if (pool) {
      pool.end();
    }
    
  } catch (error) {
    console.error(colorize(`❌ Error getting database status: ${error.message}`, 'red'));
  }
}

async function resetDatabase() {
  console.log(colorize('🔄 Resetting database...', 'yellow'));
  console.log('This will backup current data and setup fresh database');
  
  try {
    // 1. Backup current database
    const backupFile = await backupDatabase();
    console.log(colorize(`✅ Backup completed: ${path.basename(backupFile)}`, 'green'));
    
    // 2. Setup fresh database
    console.log('\n🚀 Setting up fresh database...');
    await runDatabaseSetup();
    
    console.log(colorize('\n🎉 Database reset completed successfully!', 'green'));
    console.log(`💾 Your old data is backed up in: ${backupFile}`);
    
  } catch (error) {
    console.error(colorize(`❌ Reset failed: ${error.message}`, 'red'));
    throw error;
  }
}

async function main() {
  const command = process.argv[2];
  
  if (!command || command === 'help') {
    showHelp();
    return;
  }
  
  try {
    switch (command.toLowerCase()) {
      case 'setup':
        console.log(colorize('🚀 Starting database setup...', 'cyan'));
        await runDatabaseSetup();
        break;
        
      case 'verify':
        console.log(colorize('🔍 Starting database verification...', 'cyan'));
        await verifyDatabaseIntegrity();
        break;
        
      case 'backup':
        await backupDatabase();
        break;
        
      case 'restore':
        const backupFile = process.argv[3];
        if (!backupFile) {
          console.error(colorize('❌ Please specify backup file path', 'red'));
          console.log('Usage: node scripts/complete-database-manager.js restore <backup-file>');
          process.exit(1);
        }
        await restoreDatabase(backupFile);
        break;
        
      case 'reset':
        await resetDatabase();
        break;
        
      case 'status':
        await showDatabaseStatus();
        break;
        
      default:
        console.error(colorize(`❌ Unknown command: ${command}`, 'red'));
        console.log('Run "node scripts/complete-database-manager.js help" for available commands');
        process.exit(1);
    }
    
  } catch (error) {
    console.error(colorize(`💥 Command failed: ${error.message}`, 'red'));
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  main();
}

module.exports = {
  backupDatabase,
  restoreDatabase,
  showDatabaseStatus,
  resetDatabase
};
