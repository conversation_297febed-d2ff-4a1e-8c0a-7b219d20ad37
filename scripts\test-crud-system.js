const axios = require('axios');

// Test configuration
const BASE_URL = 'http://localhost:3000';
const TEST_TABLE_ID = 1; // Adjust this to an existing table ID

class CrudSystemTester {
  constructor() {
    this.baseURL = BASE_URL;
    this.tableId = TEST_TABLE_ID;
    this.createdRecordId = null;
  }

  async runAllTests() {
    console.log('🚀 Starting CRUD System Tests...\n');

    try {
      await this.testValidationFrontend();
      await this.testValidationBackend();
      await this.testCreateRecord();
      await this.testUpdateRecord();
      await this.testForeignKeyHandling();
      await this.testDateTimeHandling();
      await this.testDeleteRecord();
      await this.testCascadeDelete();

      console.log('\n✅ All tests completed successfully!');
    } catch (error) {
      console.error('\n❌ Test suite failed:', error.message);
      console.error(error.stack);
    }
  }

  async testValidationFrontend() {
    console.log('📝 Testing Frontend Validation...');
    
    // This would typically be tested with a browser automation tool
    // For now, we'll just verify the validation script is accessible
    try {
      const response = await axios.get(`${this.baseURL}/js/table-data-validation.js`);
      console.log('  ✅ Frontend validation script accessible');
      
      // Check if the script contains expected validation functions
      const scriptContent = response.data;
      const expectedFunctions = ['validateField', 'validateForm', 'setupRealTimeValidation'];
      
      expectedFunctions.forEach(func => {
        if (scriptContent.includes(func)) {
          console.log(`  ✅ Function ${func} found in validation script`);
        } else {
          console.log(`  ❌ Function ${func} missing from validation script`);
        }
      });
    } catch (error) {
      console.log('  ❌ Frontend validation script not accessible:', error.message);
    }
  }

  async testValidationBackend() {
    console.log('\n🔍 Testing Backend Validation...');
    
    try {
      // Test with invalid data
      const invalidData = {
        name: '', // Empty required field
        email: 'invalid-email', // Invalid email format
        age: 'not-a-number' // Invalid number
      };

      const response = await axios.post(
        `${this.baseURL}/admin/tables/${this.tableId}/records`,
        invalidData,
        { validateStatus: () => true }
      );

      if (response.status === 400 && response.data.errors) {
        console.log('  ✅ Backend validation working - rejected invalid data');
        console.log('  📋 Validation errors:', response.data.errors.length);
      } else {
        console.log('  ❌ Backend validation not working properly');
      }
    } catch (error) {
      console.log('  ❌ Backend validation test failed:', error.message);
    }
  }

  async testCreateRecord() {
    console.log('\n➕ Testing Create Record...');
    
    try {
      const testData = {
        name: 'Test Record ' + Date.now(),
        description: 'Test description for CRUD testing',
        status: 1
      };

      const response = await axios.post(
        `${this.baseURL}/admin/tables/${this.tableId}/records`,
        testData
      );

      if (response.data.success && response.data.data) {
        this.createdRecordId = response.data.data.id;
        console.log('  ✅ Record created successfully, ID:', this.createdRecordId);
      } else {
        console.log('  ❌ Failed to create record:', response.data.message);
      }
    } catch (error) {
      console.log('  ❌ Create record test failed:', error.message);
    }
  }

  async testUpdateRecord() {
    console.log('\n✏️ Testing Update Record...');
    
    if (!this.createdRecordId) {
      console.log('  ⚠️ Skipping update test - no record created');
      return;
    }

    try {
      const updateData = {
        name: 'Updated Test Record ' + Date.now(),
        description: 'Updated description'
      };

      const response = await axios.put(
        `${this.baseURL}/admin/tables/${this.tableId}/records/${this.createdRecordId}`,
        updateData
      );

      if (response.data.success) {
        console.log('  ✅ Record updated successfully');
      } else {
        console.log('  ❌ Failed to update record:', response.data.message);
      }
    } catch (error) {
      console.log('  ❌ Update record test failed:', error.message);
    }
  }

  async testForeignKeyHandling() {
    console.log('\n🔗 Testing Foreign Key Handling...');
    
    try {
      // Test dropdown data endpoint
      const response = await axios.get(`${this.baseURL}/admin/dropdown/users`);
      
      if (response.data.success && Array.isArray(response.data.data)) {
        console.log('  ✅ Foreign key dropdown data loaded successfully');
        console.log('  📊 Records count:', response.data.data.length);
      } else {
        console.log('  ❌ Failed to load foreign key dropdown data');
      }
    } catch (error) {
      console.log('  ❌ Foreign key test failed:', error.message);
    }
  }

  async testDateTimeHandling() {
    console.log('\n📅 Testing DateTime Handling...');
    
    try {
      const dateTimeData = {
        name: 'DateTime Test Record',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      const response = await axios.post(
        `${this.baseURL}/admin/tables/${this.tableId}/records`,
        dateTimeData,
        { validateStatus: () => true }
      );

      if (response.data.success || response.status === 400) {
        console.log('  ✅ DateTime validation working');
      } else {
        console.log('  ❌ DateTime handling issue');
      }
    } catch (error) {
      console.log('  ❌ DateTime test failed:', error.message);
    }
  }

  async testDeleteRecord() {
    console.log('\n🗑️ Testing Delete Record...');
    
    if (!this.createdRecordId) {
      console.log('  ⚠️ Skipping delete test - no record created');
      return;
    }

    try {
      const response = await axios.delete(
        `${this.baseURL}/admin/tables/${this.tableId}/records/${this.createdRecordId}`
      );

      if (response.data.success) {
        console.log('  ✅ Record deleted successfully');
      } else {
        console.log('  ❌ Failed to delete record:', response.data.message);
      }
    } catch (error) {
      console.log('  ❌ Delete record test failed:', error.message);
    }
  }

  async testCascadeDelete() {
    console.log('\n🔥 Testing Cascade Delete...');
    
    try {
      // This test would need a record with foreign key relationships
      // For now, we'll just verify the endpoint exists
      const response = await axios.get(
        `${this.baseURL}/admin/tables/${this.tableId}/records/1/related`,
        { validateStatus: () => true }
      );

      if (response.status === 200 || response.status === 404) {
        console.log('  ✅ Cascade delete endpoint accessible');
      } else {
        console.log('  ❌ Cascade delete endpoint issue');
      }
    } catch (error) {
      console.log('  ❌ Cascade delete test failed:', error.message);
    }
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  const tester = new CrudSystemTester();
  tester.runAllTests();
}

module.exports = CrudSystemTester;
