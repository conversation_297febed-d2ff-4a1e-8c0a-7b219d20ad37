const db = require('../config/database');
const permissionService = require('../services/permissionService');

class RoleController {
  /**
   * Hi<PERSON>n thị danh sách roles
   */
  async index(req, res) {
    try {
      const breadcrumb = [
        {name: 'Home', url:'/', active: false},
        {name: 'Roles', url:'/admin/roles', active: true},
      ];
      
      res.render('admin/roles', {
        title: 'Quản lý Roles',
        user: req.user,
        breadcrumb: breadcrumb
      });
    } catch (error) {
      console.error('Error in roles index:', error);
      res.status(500).render('500', { error: error.message, title: 'Error' });
    }
  }

  /**
   * API: DataTables endpoint for roles
   */
  async api(req, res) {
    try {
      // DataTables parameters
      const draw = parseInt(req.query.draw) || 1;
      const start = parseInt(req.query.start) || 0;
      const length = parseInt(req.query.length) || 25;
      const searchValue = req.query.search?.value || '';
      
      // Custom filters
      const search = req.query.search_custom || searchValue;

      // Order
      const orderColumnIndex = parseInt(req.query.order?.[0]?.column) || 0;
      const orderDirection = req.query.order?.[0]?.dir || 'desc';
      
      // Column mapping for ordering
      const columns = ['id', 'name', 'permissions_count', 'users_count'];
      const orderBy = columns[orderColumnIndex] || 'id';

      // Build WHERE conditions
      let whereConditions = [];
      let queryParams = [];

      if (search) {
        whereConditions.push('r.name LIKE ?');
        queryParams.push(`%${search}%`);
      }

      const whereClause = whereConditions.length > 0 ? ` WHERE ${whereConditions.join(' AND ')}` : '';
      const offset = start;

      // Get data with statistics
      const [roles, countResult] = await Promise.all([
        db.query(`
          SELECT 
            r.id,
            r.name,
            COUNT(DISTINCT rp.permission_id) as permissions_count,
            COUNT(DISTINCT ur.user_id) as users_count,
            MAX(rp.granted_at) as last_permission_granted
          FROM role r
          LEFT JOIN role_permissions rp ON r.id = rp.role_id
          LEFT JOIN role_user ur ON r.id = ur.role_id
          ${whereClause}
          GROUP BY r.id, r.name
          ORDER BY ${orderBy} ${orderDirection}
          LIMIT ${length} OFFSET ${offset}
        `, queryParams),
        db.queryOne(`
          SELECT COUNT(DISTINCT r.id) as total 
          FROM role r 
          ${whereClause}
        `, queryParams)
      ]);

      // Format for DataTables
      const response = {
        draw: draw,
        recordsTotal: countResult.total,
        recordsFiltered: countResult.total,
        data: roles
      };

      res.json(response);
    } catch (error) {
      console.error('Error in roles API:', error);
      res.status(500).json({ 
        error: true, 
        message: error.message,
        draw: parseInt(req.query.draw) || 1,
        recordsTotal: 0,
        recordsFiltered: 0,
        data: []
      });
    }
  }

  /**
   * Hiển thị form tạo role mới
   */
  async create(req, res) {
    try {
      const breadcrumb = [
        {name: 'Home', url:'/', active: false},
        {name: 'Roles', url:'/admin/roles', active: false},
        {name: 'Create', url:'/admin/roles/create', active: true},
      ];
      
      res.render('admin/role-form', {
        title: 'Tạo Role Mới',
        role: null,
        user: req.user,
        breadcrumb: breadcrumb
      });
    } catch (error) {
      console.error('Error in roles create:', error);
      res.status(500).render('500', { error: error.message, title: 'Error' });
    }
  }

  /**
   * Lưu role mới
   */
  async store(req, res) {
    try {
      const { name } = req.body;

      // Validate dữ liệu
      if (!name) {
        return res.status(400).json({
          success: false,
          message: 'Tên role là bắt buộc'
        });
      }

      // Kiểm tra tên role đã tồn tại chưa
      const existingRole = await db.queryOne(`
        SELECT id FROM role WHERE name = ?
      `, [name]);

      if (existingRole) {
        return res.status(400).json({
          success: false,
          message: 'Tên role đã tồn tại'
        });
      }

      // Tạo role
      const result = await db.query(`
        INSERT INTO role (name) VALUES (?)
      `, [name]);

      res.json({
        success: true,
        message: 'Role đã được tạo thành công',
        data: { id: result.insertId }
      });
    } catch (error) {
      console.error('Error in roles store:', error);
      res.status(500).json({
        success: false,
        message: 'Lỗi tạo role: ' + error.message
      });
    }
  }

  /**
   * Hiển thị chi tiết role
   */
  async show(req, res) {
    try {
      const { id } = req.params;

      // Lấy thông tin role
      const role = await db.queryOne(`
        SELECT * FROM role WHERE id = ?
      `, [id]);

      if (!role) {
        return res.status(404).render('404', { message: 'Role không tồn tại', title: 'Not Found' });
      }

      // Lấy thống kê
      const [permissions, users] = await Promise.all([
        db.query(`
          SELECT p.id, p.name, p.display_name, p.table_name, p.action, rp.granted_at
          FROM role_permissions rp
          INNER JOIN permissions p ON rp.permission_id = p.id
          WHERE rp.role_id = ?
          ORDER BY p.table_name, p.action
        `, [id]),
        db.query(`
          SELECT u.id, u.email, u.fullname, ur.assigned_at
          FROM role_user ur
          INNER JOIN user u ON ur.user_id = u.id
          WHERE ur.role_id = ?
          ORDER BY u.fullname
        `, [id])
      ]);

      const breadcrumb = [
        {name: 'Home', url:'/', active: false},
        {name: 'Roles', url:'/admin/roles', active: false},
        {name: 'Detail', url:`/admin/roles/${id}`, active: true},
      ];

      res.render('admin/role-detail', {
        title: `Role: ${role.name}`,
        role,
        permissions,
        users,
        stats: {
          permissions_count: permissions.length,
          users_count: users.length
        },
        user: req.user,
        breadcrumb: breadcrumb
      });
    } catch (error) {
      console.error('Error in roles show:', error);
      res.status(500).render('500', { error: error.message, title: 'Error' });
    }
  }

  /**
   * Hiển thị form chỉnh sửa role
   */
  async edit(req, res) {
    try {
      const { id } = req.params;

      const role = await db.queryOne(`
        SELECT * FROM role WHERE id = ?
      `, [id]);

      if (!role) {
        return res.status(404).render('404', { message: 'Role không tồn tại', title: 'Not Found' });
      }

      const breadcrumb = [
        {name: 'Home', url:'/', active: false},
        {name: 'Roles', url:'/admin/roles', active: false},
        {name: 'Edit', url:`/admin/roles/${id}/edit`, active: true},
      ];

      res.render('admin/role-form', {
        title: 'Chỉnh sửa Role',
        role,
        user: req.user,
        breadcrumb: breadcrumb
      });
    } catch (error) {
      console.error('Error in roles edit:', error);
      res.status(500).render('500', { error: error.message, title: 'Error' });
    }
  }

  /**
   * Cập nhật role
   */
  async update(req, res) {
    try {
      const { id } = req.params;
      const { name } = req.body;

      // Validate dữ liệu
      if (!name) {
        return res.status(400).json({
          success: false,
          message: 'Tên role là bắt buộc'
        });
      }

      // Kiểm tra role có tồn tại không
      const role = await db.queryOne(`
        SELECT id FROM role WHERE id = ?
      `, [id]);

      if (!role) {
        return res.status(404).json({
          success: false,
          message: 'Role không tồn tại'
        });
      }

      // Kiểm tra tên role đã tồn tại chưa (trừ role hiện tại)
      const existingRole = await db.queryOne(`
        SELECT id FROM role WHERE name = ? AND id != ?
      `, [name, id]);

      if (existingRole) {
        return res.status(400).json({
          success: false,
          message: 'Tên role đã tồn tại'
        });
      }

      // Cập nhật role
      await db.query(`
        UPDATE role SET name = ? WHERE id = ?
      `, [name, id]);

      res.json({
        success: true,
        message: 'Role đã được cập nhật thành công'
      });
    } catch (error) {
      console.error('Error in roles update:', error);
      res.status(500).json({
        success: false,
        message: 'Lỗi cập nhật role: ' + error.message
      });
    }
  }

  /**
   * Xóa role
   */
  async destroy(req, res) {
    try {
      const { id } = req.params;

      // Kiểm tra role có tồn tại không
      const role = await db.queryOne(`
        SELECT id, name FROM role WHERE id = ?
      `, [id]);

      if (!role) {
        return res.status(404).json({
          success: false,
          message: 'Role không tồn tại'
        });
      }

      // Kiểm tra có user nào đang sử dụng role này không
      const userCount = await db.queryOne(`
        SELECT COUNT(*) as count FROM user_role WHERE role_id = ?
      `, [id]);

      if (userCount.count > 0) {
        return res.status(400).json({
          success: false,
          message: `Không thể xóa role này vì có ${userCount.count} user đang sử dụng`
        });
      }

      // Xóa tất cả permissions của role trước
      await db.query(`
        DELETE FROM role_permissions WHERE role_id = ?
      `, [id]);

      // Xóa role
      await db.query(`
        DELETE FROM role WHERE id = ?
      `, [id]);

      res.json({
        success: true,
        message: 'Role đã được xóa thành công'
      });
    } catch (error) {
      console.error('Error in roles destroy:', error);
      res.status(500).json({
        success: false,
        message: 'Lỗi xóa role: ' + error.message
      });
    }
  }

  /**
   * API: Lấy thống kê roles
   */
  async stats(req, res) {
    try {
      const stats = await db.queryOne(`
        SELECT 
          COUNT(DISTINCT r.id) as total_roles,
          COUNT(DISTINCT rp.permission_id) as total_permissions_granted,
          COUNT(DISTINCT ur.user_id) as total_users_with_roles,
          AVG(perm_count.permission_count) as avg_permissions_per_role
        FROM role r
        LEFT JOIN role_permissions rp ON r.id = rp.role_id
        LEFT JOIN user_role ur ON r.id = ur.role_id
        LEFT JOIN (
          SELECT role_id, COUNT(*) as permission_count 
          FROM role_permissions 
          GROUP BY role_id
        ) perm_count ON r.id = perm_count.role_id
      `);

      res.json({
        success: true,
        data: {
          total_roles: stats.total_roles || 0,
          total_permissions_granted: stats.total_permissions_granted || 0,
          total_users_with_roles: stats.total_users_with_roles || 0,
          avg_permissions_per_role: Math.round(stats.avg_permissions_per_role || 0)
        }
      });
    } catch (error) {
      console.error('Error getting roles stats:', error);
      res.status(500).json({ 
        success: false, 
        message: error.message 
      });
    }
  }
}

module.exports = new RoleController(); 