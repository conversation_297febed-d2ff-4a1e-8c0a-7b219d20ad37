# Backend CoreUI - Device Management System

## 🚀 Quick Start

### 1. Setup Database (One Command)
```bash
npm run db:setup
```
**Lệnh này sẽ tạo đầy đủ tất cả bảng cần thiết bao gồm:**
- ✅ User management system
- ✅ Role-based permission system
- ✅ **Multi-device session management** (tích hợp trong schema chính)
- ✅ **User session settings** (tích hợp trong schema chính)
- ✅ Dynamic CRUD admin system
- ✅ Menu management system
- ✅ **Tự động migration** các cột thiếu cho backward compatibility

### 2. Test Everything Works
```bash
npm run db:test
```

### 3. Start Application
```bash
npm start
```

## 📋 What's New - Device Management

### New Tables Added (Tích hợp trong schema chính)
1. **`user_sessions`** - Multi-device session tracking
2. **`user_session_settings`** - User session preferences
3. **`user`** - Enhanced with device management columns (auto-migration)

### Tối ưu hóa Schema
- ✅ **Tập trung schema**: Tất cả bảng trong `database/complete_database_schema.sql`
- ✅ **<PERSON>ễ bảo trì**: Không cần script riêng biệt
- ✅ **Auto-migration**: Tự động thêm cột thiếu khi setup

### New Features
- 🔐 **Multi-device login support**
- 📱 **Automatic device detection** (mobile, tablet, desktop)
- 🌐 **Browser & OS identification**
- ⏰ **Session timeout management**
- 🚪 **Remote device logout**
- ⚙️ **Per-user session settings**

## 🧪 Testing Commands

```bash
# Test database setup and device functionality
npm run db:test

# Test device management in detail
npm run test:device

# Quick functionality test
npm run test:quick

# Verify database integrity
npm run db:verify
```

## 🔧 API Endpoints

### Device Management
- `GET /api/device/active` - Get active devices
- `POST /api/device/logout/:deviceId` - Logout specific device
- `POST /api/device/logout-all-others` - Logout all other devices

### Usage Example
```javascript
// Get user's active devices
const response = await fetch('/api/device/active', {
  headers: { 'Authorization': `Bearer ${token}` }
});
const devices = await response.json();

// Logout a specific device
await fetch(`/api/device/logout/${deviceId}`, {
  method: 'POST',
  headers: { 'Authorization': `Bearer ${token}` }
});
```

## 📊 Admin Interface

### New Admin Tables
1. **User Sessions** - View and manage all user sessions
2. **Session Settings** - Configure session policies per user

### Access Admin Panel
1. Start application: `npm start`
2. Open: http://localhost:3000/admin
3. Login with: `<EMAIL>` / `password123`

## 🔐 Security Features

- **Session Limits**: Configurable max sessions per user
- **Auto Logout**: Inactive session timeout
- **Device Tracking**: Full device information logging
- **Remote Control**: Logout devices from anywhere
- **Login Notifications**: Alert on new device login

## 📖 Sample Data

### Default Users
- **Admin**: <EMAIL> / password123
- **Manager**: <EMAIL> / password123  
- **Editor**: <EMAIL> / password123
- **User**: <EMAIL> / password123

### Default Session Settings
- **Admin**: 10 sessions, 24h timeout
- **Manager**: 5 sessions, 12h timeout
- **Editor**: 3 sessions, 8h timeout
- **User**: 2 sessions, 4h timeout

## 🛠️ Troubleshooting

### Common Issues

**Database connection error:**
```bash
npm run db:status  # Check database status
```

**Missing tables:**
```bash
npm run db:setup   # Recreate all tables
```

**Device functions not working:**
```bash
npm run test:device  # Test device functionality
```

### Support Files
- `docs/DEVICE_MANAGEMENT_UPDATE.md` - Detailed technical documentation
- `scripts/test-setup.js` - Database verification script
- `scripts/test-device-functionality.js` - Device management testing

## ✅ Verification Checklist

After running `npm run db:setup`, you should have:

- [x] 11 database tables created
- [x] 4 sample users with different roles
- [x] 30 permissions configured
- [x] 9 admin tables configured
- [x] Device management fully functional
- [x] Session settings for all users
- [x] Foreign key relationships working

## 🎯 Next Steps

1. **Frontend Integration**: Add device management UI to admin panel
2. **Push Notifications**: Real-time login alerts
3. **2FA Integration**: Two-factor authentication for new devices
4. **Session Analytics**: Usage reports and statistics

---

**✨ Your Laravel Voyager-like admin system with multi-device session management is ready!**

For detailed technical information, see: `docs/DEVICE_MANAGEMENT_UPDATE.md`
