const db = require('../config/database');

async function checkRelations() {
  try {
    console.log('Checking database relations...');
    
    // Check foreign key constraints
    const foreignKeys = await db.query(`
      SELECT 
        TABLE_NAME,
        COLUMN_NAME,
        REFERENCED_TABLE_NAME,
        REFERENCED_COLUMN_NAME
      FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
      WHERE TABLE_SCHEMA = DATABASE() 
      AND REFERENCED_TABLE_NAME IS NOT NULL
      ORDER BY TABLE_NAME, COLUMN_NAME
    `);
    
    console.log('Foreign key constraints found:');
    foreignKeys.forEach(fk => {
      console.log(`- ${fk.TABLE_NAME}.${fk.COLUMN_NAME} -> ${fk.REFERENCED_TABLE_NAME}.${fk.REFERENCED_COLUMN_NAME}`);
    });
    
    // Check admin relations
    const adminRelations = await db.query(`
      SELECT ar.*, 
             t.name as table_name, 
             ft.name as foreign_table_name,
             c.name as column_name
      FROM adminrelation ar
      LEFT JOIN admintable t ON ar.table_id = t.id
      LEFT JOIN admintable ft ON ar.foreign_table_id = ft.id
      LEFT JOIN admincolumn c ON ar.column_id = c.id
      ORDER BY t.name, c.name
    `);
    
    console.log('\nAdmin relations found:');
    adminRelations.forEach(rel => {
      console.log(`- ${rel.table_name}.${rel.column_name} -> ${rel.foreign_table_name}.${rel.foreign_column}`);
    });
    
  } catch (error) {
    console.error('Error checking relations:', error);
  }
}

checkRelations();
