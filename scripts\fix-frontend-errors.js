const fs = require('fs');
const path = require('path');

console.log('🔧 Fixing Frontend JavaScript Errors...\n');

// 1. Check and fix table-data-validation.js
const validationFile = path.join(__dirname, '../public/js/table-data-validation.js');

try {
    let content = fs.readFileSync(validationFile, 'utf8');
    
    // Check if the regex fix is applied
    if (content.includes("['\"`;\\\\|*%<>{}[\\]^$()]/i")) {
        console.log('✅ Regex fix already applied in table-data-validation.js');
    } else {
        console.log('❌ Regex fix not found in table-data-validation.js');
    }
    
    // Check for other potential issues
    const lines = content.split('\n');
    let hasErrors = false;
    
    lines.forEach((line, index) => {
        // Check for unescaped regex characters
        if (line.includes('/[') && line.includes(']/') && line.includes('(') && line.includes(')')) {
            if (!line.includes("['\"`;\\\\|*%<>{}[\\]^$()]/i")) {
                console.log(`⚠️ Potential regex issue at line ${index + 1}: ${line.trim()}`);
                hasErrors = true;
            }
        }
        
        // Check for syntax errors
        if (line.includes('function') && !line.includes('{') && !line.includes(';')) {
            const nextLine = lines[index + 1];
            if (nextLine && !nextLine.trim().startsWith('{')) {
                console.log(`⚠️ Potential function syntax issue at line ${index + 1}: ${line.trim()}`);
            }
        }
    });
    
    if (!hasErrors) {
        console.log('✅ No obvious syntax errors found in table-data-validation.js');
    }
    
} catch (error) {
    console.error('❌ Error reading table-data-validation.js:', error.message);
}

// 2. Check table-data.ejs for common issues
const tableDataFile = path.join(__dirname, '../views/admin/table-data.ejs');

try {
    let content = fs.readFileSync(tableDataFile, 'utf8');
    
    // Check for duplicate variable declarations
    const columnMatches = content.match(/const columns\s*=/g);
    if (columnMatches && columnMatches.length > 1) {
        console.log(`⚠️ Found ${columnMatches.length} 'const columns' declarations in table-data.ejs`);
    } else {
        console.log('✅ No duplicate column variable declarations found');
    }
    
    // Check for window function assignments
    const windowFunctions = [
        'window.openAddModal',
        'window.addRecord',
        'window.editRecord',
        'window.updateRecord',
        'window.deleteRecord'
    ];
    
    windowFunctions.forEach(func => {
        if (content.includes(func)) {
            console.log(`✅ ${func} is properly assigned to window`);
        } else {
            console.log(`❌ ${func} is not assigned to window`);
        }
    });
    
    // Check for proper script structure
    if (content.includes('<script>') && content.includes('</script>')) {
        console.log('✅ Script tags are properly structured');
    } else {
        console.log('❌ Script tags may be malformed');
    }
    
} catch (error) {
    console.error('❌ Error reading table-data.ejs:', error.message);
}

// 3. Generate a summary report
console.log('\n📋 Summary Report:');
console.log('==================');

const recommendations = [
    '1. Make sure all functions called from onclick are assigned to window object',
    '2. Avoid duplicate variable declarations in the same scope',
    '3. Use proper regex escaping for special characters',
    '4. Test JavaScript in browser console for runtime errors',
    '5. Use browser developer tools to check for console errors'
];

recommendations.forEach(rec => {
    console.log(rec);
});

console.log('\n🚀 To test the fixes:');
console.log('1. Start the server: npm start');
console.log('2. Visit: http://localhost:3000/admin/test-js');
console.log('3. Check browser console for any remaining errors');
console.log('4. Test CRUD operations on actual table data pages');

console.log('\n✅ Frontend error fixing script completed!');
