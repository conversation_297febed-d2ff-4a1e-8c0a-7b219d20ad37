<div class="container-fluid">

  <!-- Page Header -->
  <div class="row">
    <div class="col-12">
      <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
          <h4 class="card-title mb-0">
            <i class="fas fa-table"></i> Table Structure: <%= table.display_name %>
          </h4>
          <div>
            <a href="/admin/tables/<%= table.id %>/data" class="btn btn-info btn-sm">
              <i class="fas fa-database"></i> Manage Data
            </a>
            <a href="/admin/tables" class="btn btn-secondary btn-sm">
              <i class="fas fa-arrow-left"></i> Back to Tables
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Table Info -->
  <div class="row mt-3">
    <div class="col-md-6">
      <div class="card">
        <div class="card-header">
          <h5 class="card-title mb-0">Table Information</h5>
        </div>
        <div class="card-body">
          <table class="table table-sm">
            <tr>
              <td><strong>Table Name:</strong></td>
              <td><%= table.name %></td>
            </tr>
            <tr>
              <td><strong>Display Name:</strong></td>
              <td><%= table.display_name %></td>
            </tr>
            <tr>
              <td><strong>Model Name:</strong></td>
              <td><%= table.model_name %></td>
            </tr>
            <tr>
              <td><strong>Description:</strong></td>
              <td><%= table.description || 'N/A' %></td>
            </tr>
            <tr>
              <td><strong>Status:</strong></td>
              <td>
                <span class="badge badge-<%= table.is_active ? 'success' : 'danger' %>">
                  <%= table.is_active ? 'Active' : 'Inactive' %>
                </span>
              </td>
            </tr>
          </table>
        </div>
      </div>
    </div>
    <div class="col-md-6">
      <div class="card">
        <div class="card-header">
          <h5 class="card-title mb-0">Quick Stats</h5>
        </div>
        <div class="card-body">
          <table class="table table-sm">
            <tr>
              <td><strong>Total Columns:</strong></td>
              <td><%= table.columns.length %></td>
            </tr>
            <tr>
              <td><strong>Visible in List:</strong></td>
              <td><%= table.columns.filter(col => col.is_visible_list).length %></td>
            </tr>
            <tr>
              <td><strong>Visible in Form:</strong></td>
              <td><%= table.columns.filter(col => col.is_visible_form).length %></td>
            </tr>
            <tr>
              <td><strong>Searchable:</strong></td>
              <td><%= table.columns.filter(col => col.is_searchable).length %></td>
            </tr>
            <tr>
              <td><strong>Relations:</strong></td>
              <td><%= table.relations.length %></td>
            </tr>
          </table>
        </div>
      </div>
    </div>
  </div>

  <!-- Columns Management -->
  <div class="row mt-3">
    <div class="col-12">
      <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
          <h5 class="card-title mb-0">Columns</h5>
          <button type="button" class="btn btn-primary btn-sm" data-toggle="modal" data-target="#addColumnModal">
            <i class="fas fa-plus"></i> Add Column
          </button>
        </div>
        <div class="card-body">
          <div class="table-responsive">
            <table class="table table-striped table-hover" id="columnsTable">
              <thead>
                <tr>
                  <th>Name</th>
                  <th>Display Name</th>
                  <th>Type</th>
                  <th>Nullable</th>
                  <th>Primary</th>
                  <th>Auto Inc</th>
                  <th>Default</th>
                  <th>List</th>
                  <th>Form</th>
                  <th>Search</th>
                  <th>Sort</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                <% table.columns.forEach(column => { %>
                <tr data-column-id="<%= column.id %>">
                  <td><code><%= column.name %></code></td>
                  <td><%= column.display_name %></td>
                  <td>
                    <span class="badge badge-info">
                      <%= column.type %><%= column.length ? `(${column.length})` : '' %>
                    </span>
                  </td>
                  <td>
                    <span class="badge badge-<%= column.is_nullable ? 'warning' : 'success' %>">
                      <%= column.is_nullable ? 'YES' : 'NO' %>
                    </span>
                  </td>
                  <td>
                    <% if (column.is_primary) { %>
                      <i class="fas fa-key text-warning" title="Primary Key"></i>
                    <% } %>
                  </td>
                  <td>
                    <% if (column.is_auto_increment) { %>
                      <i class="fas fa-plus-circle text-info" title="Auto Increment"></i>
                    <% } %>
                  </td>
                  <td><%= column.default_value || '-' %></td>
                  <td>
                    <% if (column.is_visible_list) { %>
                      <i class="fas fa-check text-success"></i>
                    <% } else { %>
                      <i class="fas fa-times text-danger"></i>
                    <% } %>
                  </td>
                  <td>
                    <% if (column.is_visible_form) { %>
                      <i class="fas fa-check text-success"></i>
                    <% } else { %>
                      <i class="fas fa-times text-danger"></i>
                    <% } %>
                  </td>
                  <td>
                    <% if (column.is_searchable) { %>
                      <i class="fas fa-check text-success"></i>
                    <% } else { %>
                      <i class="fas fa-times text-danger"></i>
                    <% } %>
                  </td>
                  <td>
                    <% if (column.is_sortable) { %>
                      <i class="fas fa-check text-success"></i>
                    <% } else { %>
                      <i class="fas fa-times text-danger"></i>
                    <% } %>
                  </td>
                  <td>
                    <button type="button" class="btn btn-sm btn-warning edit-column" 
                            data-column-id="<%= column.id %>" title="Edit">
                      <i class="fas fa-edit"></i>
                    </button>
                    <% if (!column.is_primary) { %>
                    <button type="button" class="btn btn-sm btn-danger delete-column" 
                            data-column-id="<%= column.id %>" 
                            data-column-name="<%= column.name %>" title="Delete">
                      <i class="fas fa-trash"></i>
                    </button>
                    <% } %>
                  </td>
                </tr>
                <% }) %>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Relations Section -->
  <div class="row mt-3">
    <div class="col-12">
      <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
          <h5 class="card-title mb-0">Relations</h5>
          <button type="button" class="btn btn-primary btn-sm" data-toggle="modal" data-target="#addRelationModal">
            <i class="fas fa-plus"></i> Add Relation
          </button>
        </div>
        <div class="card-body">
          <% if (table.relations.length > 0) { %>
          <div class="table-responsive">
            <table class="table table-striped">
              <thead>
                <tr>
                  <th>Column</th>
                  <th>Foreign Table</th>
                  <th>Foreign Column</th>
                  <th>Display Column</th>
                  <th>Type</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                <% table.relations.forEach(relation => { %>
                <tr>
                  <td><code><%= relation.column.name %></code></td>
                  <td><%= relation.foreign_table.display_name %></td>
                  <td><code><%= relation.foreign_column %></code></td>
                  <td><code><%= relation.display_column %></code></td>
                  <td><span class="badge badge-info"><%= relation.relation_type %></span></td>
                  <td>
                    <button type="button" class="btn btn-sm btn-danger delete-relation" 
                            data-relation-id="<%= relation.id %>" title="Delete">
                      <i class="fas fa-trash"></i>
                    </button>
                  </td>
                </tr>
                <% }) %>
              </tbody>
            </table>
          </div>
          <% } else { %>
          <p class="text-muted">No relations defined for this table.</p>
          <% } %>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Add Column Modal -->
<div class="modal fade" id="addColumnModal" tabindex="-1" role="dialog">
  <div class="modal-dialog modal-lg" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Add New Column</h5>
        <button class="btn-close" type="button" data-dismiss="modal" aria-label="Close"></button>
      </div>
      <form id="addColumnForm">
        <div class="modal-body">
          <!-- Column form fields will be added here -->
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
          <button type="submit" class="btn btn-primary">Add Column</button>
        </div>
      </form>
    </div>
  </div>
</div>

<!-- Edit Column Modal -->
<div class="modal fade" id="editColumnModal" tabindex="-1" role="dialog">
  <div class="modal-dialog modal-lg" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Edit Column</h5>
        <button type="button" class="btn-close" data-dismiss="modal" aria-label="Close"></button>
      </div>
      <form id="editColumnForm">
        <div class="modal-body">
          <!-- Column form fields will be added here -->
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
          <button type="submit" class="btn btn-primary">Update Column</button>
        </div>
      </form>
    </div>
  </div>
</div>

<!-- Add Relation Modal -->
<div class="modal fade" id="addRelationModal" tabindex="-1" role="dialog">
  <div class="modal-dialog modal-lg" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">
          <i class="fas fa-link"></i> Add New Relation
        </h5>
        <button type="button" class="btn-close" data-dismiss="modal" aria-label="Close"></button>
      </div>
      <form id="addRelationForm">
        <div class="modal-body">
          <!-- Relation Type Selection -->
          <div class="row mb-3">
            <div class="col-12">
              <div class="form-group">
                <label for="relationType" class="form-label">Relation Type *</label>
                <select class="form-select" id="relationType" name="relation_type" required>
                  <option value="">-- Select Relation Type --</option>
                  <option value="belongsTo">Belongs To (Many-to-One)</option>
                  <option value="hasMany">Has Many (One-to-Many)</option>
                  <option value="hasOne">Has One (One-to-One)</option>
                  <option value="belongsToMany">Belongs To Many (Many-to-Many)</option>
                </select>
                <div class="form-text">
                  <small>
                    <strong>Belongs To:</strong> This table has a foreign key to another table<br>
                    <strong>Has Many:</strong> Another table has a foreign key to this table<br>
                    <strong>Has One:</strong> Another table has exactly one record related to this table<br>
                    <strong>Belongs To Many:</strong> Many-to-many relationship through a pivot table
                  </small>
                </div>
              </div>
            </div>
          </div>

          <!-- Local Column Selection -->
          <div class="row mb-3">
            <div class="col-md-6">
              <div class="form-group">
                <label for="relationColumn" class="form-label">Local Column *</label>
                <select class="form-select" id="relationColumn" name="column_id" required>
                  <option value="">-- Select Column --</option>
                  <% table.columns.forEach(column => { %>
                    <% if (!column.is_primary) { %>
                      <option value="<%= column.id %>" data-type="<%= column.type %>">
                        <%= column.name %> (<%= column.display_name %>) - <%= column.type %>
                      </option>
                    <% } %>
                  <% }) %>
                </select>
                <div class="form-text">The column in this table that references another table</div>
              </div>
            </div>
            <div class="col-md-6">
              <div class="form-group">
                <label for="foreignTable" class="form-label">Foreign Table *</label>
                <select class="form-select" id="foreignTable" name="foreign_table_id" required>
                  <option value="">-- Select Table --</option>
                </select>
                <div class="form-text">The table being referenced</div>
              </div>
            </div>
          </div>

          <!-- Foreign Column Selection -->
          <div class="row mb-3">
            <div class="col-md-6">
              <div class="form-group">
                <label for="foreignColumn" class="form-label">Foreign Column *</label>
                <select class="form-select" id="foreignColumn" name="foreign_column" required>
                  <option value="">-- Select Column --</option>
                </select>
                <div class="form-text">Usually the primary key of the foreign table</div>
              </div>
            </div>
            <div class="col-md-6">
              <div class="form-group">
                <label for="displayColumn" class="form-label">Display Column *</label>
                <select class="form-select" id="displayColumn" name="display_column" required>
                  <option value="">-- Select Column --</option>
                </select>
                <div class="form-text">Column to show in dropdowns (e.g., name, title)</div>
              </div>
            </div>
          </div>

          <!-- Advanced Options -->
          <div class="row mb-3">
            <div class="col-md-6">
              <div class="form-group">
                <label for="onDelete" class="form-label">On Delete Action</label>
                <select class="form-select" id="onDelete" name="on_delete">
                  <option value="RESTRICT">RESTRICT (Prevent deletion)</option>
                  <option value="CASCADE" selected>CASCADE (Delete related records)</option>
                  <option value="SET NULL">SET NULL (Set to null)</option>
                  <option value="NO ACTION">NO ACTION (No action)</option>
                </select>
                <div class="form-text">What happens when foreign record is deleted</div>
              </div>
            </div>
            <div class="col-md-6">
              <div class="form-group">
                <label for="onUpdate" class="form-label">On Update Action</label>
                <select class="form-select" id="onUpdate" name="on_update">
                  <option value="RESTRICT">RESTRICT (Prevent update)</option>
                  <option value="CASCADE" selected>CASCADE (Update related records)</option>
                  <option value="SET NULL">SET NULL (Set to null)</option>
                  <option value="NO ACTION">NO ACTION (No action)</option>
                </select>
                <div class="form-text">What happens when foreign record is updated</div>
              </div>
            </div>
          </div>

          <!-- Relation Preview -->
          <div class="row mb-3">
            <div class="col-12">
              <div class="card">
                <div class="card-body">
                  <h6 class="card-title">
                    <i class="fas fa-eye"></i> Relation Preview
                  </h6>
                  <div id="relationPreview" class="text-muted">
                    Select options above to see the relation preview...
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-dismiss="modal">
            <i class="fas fa-times"></i> Cancel
          </button>
          <button type="submit" class="btn btn-primary">
            <i class="fas fa-plus"></i> Add Relation
          </button>
        </div>
      </form>
    </div>
  </div>
</div>

<script>
  const table = <%- JSON.stringify(table) %>;
  console.log("table", table);
$(document).ready(function() {
    // Initialize DataTable for columns
    $('#columnsTable').DataTable({
        pageLength: 25,
        order: [[0, 'asc']],
        columnDefs: [
            { orderable: false, targets: [11] } // Actions column
        ]
    });

    // Column form template
    const columnFormFields = `
        <div class="row">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="columnName">Column Name *</label>
                    <input type="text" class="form-control" id="columnName" name="name" required>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="columnDisplayName">Display Name *</label>
                    <input type="text" class="form-control" id="columnDisplayName" name="display_name" required>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="columnType">Data Type *</label>
                    <select class="form-control" id="columnType" name="type" required>
                        <option value="varchar">VARCHAR</option>
                        <option value="int">INT</option>
                        <option value="bigint">BIGINT</option>
                        <option value="text">TEXT</option>
                        <option value="longtext">LONGTEXT</option>
                        <option value="datetime">DATETIME</option>
                        <option value="date">DATE</option>
                        <option value="time">TIME</option>
                        <option value="timestamp">TIMESTAMP</option>
                        <option value="boolean">BOOLEAN</option>
                        <option value="decimal">DECIMAL</option>
                        <option value="float">FLOAT</option>
                        <option value="double">DOUBLE</option>
                        <option value="json">JSON</option>
                    </select>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="columnLength">Length</label>
                    <input type="number" class="form-control" id="columnLength" name="length">
                    <small class="form-text text-muted">For VARCHAR, DECIMAL, etc.</small>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="columnDefault">Default Value</label>
                    <input type="text" class="form-control" id="columnDefault" name="default_value">
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="formType">Form Type</label>
                    <select class="form-control" id="formType" name="form_type">
                        <option value="input">Input</option>
                        <option value="textarea">Textarea</option>
                        <option value="select">Select</option>
                        <option value="checkbox">Checkbox</option>
                        <option value="radio">Radio</option>
                        <option value="datepicker">Date Picker</option>
                        <option value="file">File Upload</option>
                    </select>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-3">
                <div class="form-check">
                    <input type="checkbox" class="form-check-input" id="isNullable" name="is_nullable" value="1">
                    <label class="form-check-label" for="isNullable">Nullable</label>
                </div>
            </div>
            <div class="col-md-3">
                <div class="form-check">
                    <input type="checkbox" class="form-check-input" id="isUnique" name="is_unique" value="1">
                    <label class="form-check-label" for="isUnique">Unique</label>
                </div>
            </div>
            <div class="col-md-3">
                <div class="form-check">
                    <input type="checkbox" class="form-check-input" id="isAutoIncrement" name="is_auto_increment" value="1">
                    <label class="form-check-label" for="isAutoIncrement">Auto Increment</label>
                </div>
            </div>
        </div>
        <hr>
        <h6>Display Options</h6>
        <div class="row">
            <div class="col-md-3">
                <div class="form-check">
                    <input type="checkbox" class="form-check-input" id="isVisibleList" name="is_visible_list" value="1" checked>
                    <label class="form-check-label" for="isVisibleList">Show in List</label>
                </div>
            </div>
            <div class="col-md-3">
                <div class="form-check">
                    <input type="checkbox" class="form-check-input" id="isVisibleForm" name="is_visible_form" value="1" checked>
                    <label class="form-check-label" for="isVisibleForm">Show in Form</label>
                </div>
            </div>
            <div class="col-md-3">
                <div class="form-check">
                    <input type="checkbox" class="form-check-input" id="isSearchable" name="is_searchable" value="1">
                    <label class="form-check-label" for="isSearchable">Searchable</label>
                </div>
            </div>
            <div class="col-md-3">
                <div class="form-check">
                    <input type="checkbox" class="form-check-input" id="isSortable" name="is_sortable" value="1" checked>
                    <label class="form-check-label" for="isSortable">Sortable</label>
                </div>
            </div>
        </div>
    `;

    // Add column form fields to modal
    $('#addColumnModal .modal-body').html(columnFormFields);
    $('#editColumnModal .modal-body').html(columnFormFields);

    // Load available tables for relations
    function loadAvailableTables() {
        console.log('Loading available tables...');
        $.get('/admin/tables/data?length=1000', function(response) {
            console.log('Tables response:', response);
            if (response.success || response.data) {
                const select = $('#foreignTable');
                select.find('option:not(:first)').remove();

                const tables = response.data || response.data;
                const currentTableId = '<%= table.id %>';
                console.log('Current table ID:', currentTableId);
                tables.forEach(table => {
                    console.log('Processing table:', table);
                    if (table.id.toString() !== currentTableId) { // Exclude current table
                        select.append(`<option value="${table.id}">${table.display_name} (${table.name})</option>`);
                    }
                });
            }
        }).fail(function() {
            console.log('Fallback to database tables...');
            // Fallback to database tables
           
        });
    }

    // Handle foreign table selection
    $('#foreignTable').on('change', function() {
        const tableId = $(this).val();
        if (!tableId) {
            $('#foreignColumn, #displayColumn').find('option:not(:first)').remove();
            updateRelationPreview();
            return;
        }

        // Load columns for selected table
        $.get(`/admin/tables/${tableId}/columns`, function(response) {
            console.log('Table columns response:', response);
            if (response.success) {
                const foreignColumnSelect = $('#foreignColumn');
                const displayColumnSelect = $('#displayColumn');

                foreignColumnSelect.find('option:not(:first)').remove();
                displayColumnSelect.find('option:not(:first)').remove();

                response.data.forEach(column => {
                    console.log('Processing column:', column);
                    const columnName = column.name;
                    const columnType = column.type;
                    const isPrimary = column.is_primary;
                    
                    // Add to foreign column (usually primary keys)
                    if (isPrimary) {
                        foreignColumnSelect.append(`<option value="${columnName}" selected>${columnName} (${columnType}) - Primary Key</option>`);
                    } else {
                        foreignColumnSelect.append(`<option value="${columnName}">${columnName} (${columnType})</option>`);
                    }
                    
                    // Add to display column (usually name, title, etc.)
                    if (columnName.toLowerCase().includes('name') || 
                        columnName.toLowerCase().includes('title') || 
                        columnName.toLowerCase().includes('label')) {
                        displayColumnSelect.append(`<option value="${columnName}" selected>${columnName} (${columnType})</option>`);
                    } else {
                        displayColumnSelect.append(`<option value="${columnName}">${columnName} (${columnType})</option>`);
                    }
                });
                
                updateRelationPreview();
            } else {
                console.error('API returned error:', response);
            }
        }).fail(function(xhr) {
            console.error('Error loading table columns:', xhr.responseJSON);
            alert('Failed to load table columns. Please try again.');
        });
    });

    // Update relation preview
    function updateRelationPreview() {
        const relationType = $('#relationType').val();
        const localColumn = $('#relationColumn option:selected').text();
        const foreignTable = $('#foreignTable option:selected').text();
        const foreignColumn = $('#foreignColumn option:selected').text();
        const displayColumn = $('#displayColumn option:selected').text();

        let preview = '';
        
        if (relationType && localColumn && foreignTable && foreignColumn && displayColumn) {
            const localColumnName = localColumn.split(' (')[0];
            const foreignTableName = foreignTable.split(' (')[0];
            const foreignColumnName = foreignColumn.split(' (')[0];
            const displayColumnName = displayColumn.split(' (')[0];

            switch (relationType) {
                case 'belongsTo':
                    preview = `<strong>${localColumnName}</strong> belongs to <strong>${foreignTableName}</strong> via <strong>${foreignColumnName}</strong>`;
                    break;
                case 'hasMany':
                    preview = `<strong>${foreignTableName}</strong> has many records in this table via <strong>${foreignColumnName}</strong>`;
                    break;
                case 'hasOne':
                    preview = `<strong>${foreignTableName}</strong> has one record in this table via <strong>${foreignColumnName}</strong>`;
                    break;
                case 'belongsToMany':
                    preview = `Many-to-many relationship between <strong>${localColumnName}</strong> and <strong>${foreignTableName}</strong>`;
                    break;
            }
            
            preview += `<br><small class="text-muted">Display: ${displayColumnName}</small>`;
        } else {
            preview = 'Select options above to see the relation preview...';
        }

        $('#relationPreview').html(preview);
    }

    // Update preview when any field changes
    $('#relationType, #relationColumn, #foreignTable, #foreignColumn, #displayColumn').on('change', updateRelationPreview);

    // Add relation form submission
    $('#addRelationForm').on('submit', function(e) {
        e.preventDefault();

        const formData = new FormData(this);
        const relationData = {};

        for (let [key, value] of formData.entries()) {
            relationData[key] = value.trim();
        }

        // Validate required fields
        if (!relationData.column_id || !relationData.foreign_table_id ||
            !relationData.foreign_column || !relationData.display_column ||
            !relationData.relation_type) {
            showAlert('warning', 'Please fill in all required fields');
            return;
        }

        // Add table_id
        relationData.table_id = '<%= table.id %>';

        // Show loading state
        const submitBtn = $(this).find('button[type="submit"]');
        const originalText = submitBtn.html();
        submitBtn.html('<i class="fas fa-spinner fa-spin"></i> Creating...').prop('disabled', true);

        $.ajax({
            url: '/admin/relations',
            method: 'POST',
            data: relationData,
            success: function(response) {
                if (response.success) {
                    showAlert('success', 'Relation added successfully!');
                    $('#addRelationModal').modal('hide');
                    setTimeout(() => location.reload(), 1000);
                } else {
                    showAlert('danger', response.message || 'Failed to add relation');
                }
            },
            error: function(xhr) {
                const response = xhr.responseJSON;
                showAlert('danger', response?.message || 'Failed to add relation');
            },
            complete: function() {
                submitBtn.html(originalText).prop('disabled', false);
            }
        });
    });

    // Delete column button click
    $(document).on('click', '.delete-column', function() {
        const columnId = $(this).data('column-id');
        const columnName = $(this).data('column-name');

        if (confirm(`Are you sure you want to delete column "${columnName}"? This action cannot be undone.`)) {
            $.ajax({
                url: `/admin/tables/<%= table.id %>/structure`,
                method: 'POST',
                data: {
                    action: 'drop_column',
                    columnData: { id: columnId }
                },
                success: function(response) {
                    if (response.success) {
                        showAlert('success', 'Column deleted successfully!');
                        setTimeout(() => location.reload(), 1000);
                    } else {
                        showAlert('danger', response.message || 'Failed to delete column');
                    }
                },
                error: function(xhr) {
                    const response = xhr.responseJSON;
                    showAlert('danger', response?.message || 'Failed to delete column');
                }
            });
        }
    });

    // Show alert function
    function showAlert(type, message) {
        const alertHtml = `
            <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                ${message}
                <button type="button" class="close" data-dismiss="alert">
                    <span>&times;</span>
                </button>
            </div>
        `;

        // Remove existing alerts
        $('.alert').remove();

        // Add new alert at the top of the container
        $('.container-fluid').prepend(alertHtml);

        // Auto dismiss after 5 seconds
        setTimeout(() => {
            $('.alert').fadeOut();
        }, 5000);
    }

    // Delete relation
    $(document).on('click', '.delete-relation', function() {
        const relationId = $(this).data('relation-id');

        if (confirm('Are you sure you want to delete this relation?')) {
            $.ajax({
                url: `/admin/relations/${relationId}`,
                method: 'DELETE',
                data: {},
                success: function(response) {
                    if (response.success) {
                        showAlert('success', 'Relation deleted successfully!');
                        setTimeout(() => location.reload(), 1000);
                    } else {
                        showAlert('danger', response.message || 'Failed to delete relation');
                    }
                },
                error: function(xhr) {
                    const response = xhr.responseJSON;
                    showAlert('danger', response?.message || 'Failed to delete relation');
                }
            });
        }
    });

    // Load available tables for relations
    loadAvailableTables();

    // Add column form submission
    $('#addColumnForm').on('submit', function(e) {
        e.preventDefault();

        const formData = new FormData(this);
        const columnData = {};

        for (let [key, value] of formData.entries()) {
            if (key.startsWith('is_')) {
                columnData[key] = value === '1' || value === 'on';
            } else if (key === 'length') {
                columnData[key] = value === '' ? null : parseInt(value);
            } else {
                columnData[key] = value.trim();
            }
        }

        // Ensure required fields are present
        if (!columnData.name || !columnData.display_name || !columnData.type) {
            alert('Please fill in all required fields (Name, Display Name, Type)');
            return;
        }

        $.ajax({
            url: `/admin/tables/<%= table.id %>/structure`,
            method: 'POST',
            data: {
                action: 'add_column',
                columnData: columnData
            },
            success: function(response) {
                if (response.success) {
                    showAlert('success', 'Column added successfully!');
                    setTimeout(() => location.reload(), 1000);
                } else {
                    showAlert('danger', response.message || 'Failed to add column');
                }
            },
            error: function(xhr) {
                const response = xhr.responseJSON;
                showAlert('danger', response?.message || 'Failed to add column');
            }
        });
    });

    // Edit column button click
    $(document).on('click', '.edit-column', function() {
        const columnId = $(this).data('column-id');
        console.log("columnId", columnId, $(this).attr('data-column-id'));
        // Find column data
        const column = <%- JSON.stringify(table.columns) %>.find(col => col.id === columnId);
        if (!column) return;

        // Populate form
        $('#editColumnModal input[name="name"]').val(column.name);
        $('#editColumnModal input[name="display_name"]').val(column.display_name);
        $('#editColumnModal select[name="type"]').val(column.type);
        $('#editColumnModal input[name="length"]').val(column.length);
        $('#editColumnModal input[name="default_value"]').val(column.default_value);
        $('#editColumnModal select[name="form_type"]').val(column.form_type);

        // Set checkboxes
        $('#editColumnModal input[name="is_nullable"]').prop('checked', column.is_nullable);
        $('#editColumnModal input[name="is_unique"]').prop('checked', column.is_unique);
        $('#editColumnModal input[name="is_auto_increment"]').prop('checked', column.is_auto_increment);
        $('#editColumnModal input[name="is_visible_list"]').prop('checked', column.is_visible_list);
        $('#editColumnModal input[name="is_visible_form"]').prop('checked', column.is_visible_form);
        $('#editColumnModal input[name="is_searchable"]').prop('checked', column.is_searchable);
        $('#editColumnModal input[name="is_sortable"]').prop('checked', column.is_sortable);

        // Store column ID for form submission
        $('#editColumnForm').data('column-id', columnId);

        $('#editColumnModal').modal('show');
    });

    // Edit column form submission
    $('#editColumnForm').on('submit', function(e) {
        e.preventDefault();

        const columnId = $(this).data('column-id');
        console.log("columnId", columnId, $(this).attr('data-column-id'));
        const formData = new FormData(this);
        const columnData = { id: columnId };

        for (let [key, value] of formData.entries()) {
            if (key.startsWith('is_')) {
                columnData[key] = value === '1' || value === 'on';
            } else if (key === 'length') {
                columnData[key] = value === '' ? null : parseInt(value);
            } else {
                columnData[key] = value.trim();
            }
        }

        // Ensure required fields are present
        if (!columnData.name || !columnData.display_name || !columnData.type) {
            alert('Please fill in all required fields (Name, Display Name, Type)');
            return;
        }
        console.log("columnData", columnData);
        $.ajax({
            url: `/admin/tables/<%= table.id %>/structure`,
            method: 'POST',
            data: {
                action: 'modify_column',
                columnData: JSON.stringify(columnData)
            },
            success: function(response) {
                if (response.success) {
                    showAlert('success', 'Column updated successfully!');
                    setTimeout(() => location.reload(), 1000);
                } else {
                    showAlert('danger', response.message || 'Failed to update column');
                }
            },
            error: function(xhr) {
                const response = xhr.responseJSON;
                showAlert('danger', response?.message || 'Failed to update column');
            }
        });
    });
});
</script>
