<%- contentFor('title') %>
<%= title %>
<%- contentFor('title') %>

<%- contentFor('breadcrumb') %>
<nav aria-label="breadcrumb">
  <ol class="breadcrumb">
    <li class="breadcrumb-item"><a href="/admin">Dashboard</a></li>
    <li class="breadcrumb-item active" aria-current="page">Permissions</li>
  </ol>
</nav>
<%- contentFor('breadcrumb') %>

<div class="container-fluid">
  <div class="row">
    <div class="col-12">
      <div class="card">
        <div class="card-header">
          <div class="row align-items-center">
            <div class="col">
              <h4 class="card-title mb-0">
                <i class="fas fa-key"></i> Permissions Management
              </h4>
            </div>
            <div class="col-auto">
              <a href="/admin/permissions/create" class="btn btn-primary">
                <i class="fas fa-plus"></i> Add Permission
              </a>
              <button type="button" class="btn btn-info" onclick="syncPermissions()">
                <i class="fas fa-sync"></i> Sync Permissions
              </button>
            </div>
          </div>
        </div>

        <div class="card-body">
          <!-- Search and Filter Form -->
          <form method="GET" class="mb-4">
            <div class="row">
              <div class="col-md-3">
                <div class="form-group">
                  <label for="search">Search</label>
                  <input type="text" class="form-control" id="search" name="search" 
                         value="<%= search %>" placeholder="Search permissions...">
                </div>
              </div>
              <div class="col-md-2">
                <div class="form-group">
                  <label for="table">Table</label>
                  <select class="form-control" id="table" name="table">
                    <option value="">All Tables</option>
                    <% tables.forEach(table => { %>
                      <option value="<%= table.table_name %>" <%= tableFilter === table.table_name ? 'selected' : '' %>>
                        <%= table.table_name %>
                      </option>
                    <% }) %>
                  </select>
                </div>
              </div>
              <div class="col-md-2">
                <div class="form-group">
                  <label for="action">Action</label>
                  <select class="form-control" id="action" name="action">
                    <option value="">All Actions</option>
                    <% actions.forEach(action => { %>
                      <option value="<%= action.action %>" <%= actionFilter === action.action ? 'selected' : '' %>>
                        <%= action.action %>
                      </option>
                    <% }) %>
                  </select>
                </div>
              </div>
              <div class="col-md-2">
                <div class="form-group">
                  <label for="perpage">Per Page</label>
                  <select class="form-control" id="perpage" name="perpage">
                    <option value="25" <%= pagination.perPage === 25 ? 'selected' : '' %>>25</option>
                    <option value="50" <%= pagination.perPage === 50 ? 'selected' : '' %>>50</option>
                    <option value="100" <%= pagination.perPage === 100 ? 'selected' : '' %>>100</option>
                  </select>
                </div>
              </div>
              <div class="col-md-3">
                <div class="form-group">
                  <label>&nbsp;</label>
                  <div class="d-flex">
                    <button type="submit" class="btn btn-secondary mr-2">
                      <i class="fas fa-search"></i> Filter
                    </button>
                    <a href="/admin/permissions" class="btn btn-outline-secondary">
                      <i class="fas fa-times"></i> Clear
                    </a>
                  </div>
                </div>
              </div>
            </div>
          </form>

          <!-- Permissions Table -->
          <div class="table-responsive">
            <table class="table table-striped table-hover">
              <thead class="thead-dark">
                <tr>
                  <th>ID</th>
                  <th>Name</th>
                  <th>Display Name</th>
                  <th>Table</th>
                  <th>Action</th>
                  <th>Description</th>
                  <th>Created At</th>
                  <th width="120">Actions</th>
                </tr>
              </thead>
              <tbody>
                <% if (permissions.length > 0) { %>
                  <% permissions.forEach(permission => { %>
                    <tr>
                      <td><%= permission.id %></td>
                      <td><code><%= permission.name %></code></td>
                      <td><%= permission.display_name %></td>
                      <td>
                        <% if (permission.table_name) { %>
                          <span class="badge badge-info"><%= permission.table_name %></span>
                        <% } else { %>
                          <span class="text-muted">-</span>
                        <% } %>
                      </td>
                      <td>
                        <span class="badge badge-<%= getActionBadgeClass(permission.action) %>">
                          <%= permission.action %>
                        </span>
                      </td>
                      <td>
                        <% if (permission.description) { %>
                          <%= permission.description.length > 50 ? permission.description.substring(0, 50) + '...' : permission.description %>
                        <% } else { %>
                          <span class="text-muted">-</span>
                        <% } %>
                      </td>
                      <td>
                        <%= new Date(permission.created_at).toLocaleDateString() %>
                      </td>
                      <td>
                        <div class="btn-group btn-group-sm">
                          <a href="/admin/permissions/<%= permission.id %>/edit" 
                             class="btn btn-outline-primary" title="Edit">
                            <i class="fas fa-edit"></i>
                          </a>
                          <button type="button" class="btn btn-outline-danger" 
                                  onclick="deletePermission(<%= permission.id %>)" title="Delete">
                            <i class="fas fa-trash"></i>
                          </button>
                        </div>
                      </td>
                    </tr>
                  <% }) %>
                <% } else { %>
                  <tr>
                    <td colspan="8" class="text-center text-muted py-4">
                      <i class="fas fa-inbox fa-2x mb-2"></i><br>
                      No permissions found
                    </td>
                  </tr>
                <% } %>
              </tbody>
            </table>
          </div>

          <!-- Pagination -->
          <% if (pagination.totalPages > 1) { %>
            <nav aria-label="Permissions pagination">
              <ul class="pagination justify-content-center">
                <li class="page-item <%= pagination.page === 1 ? 'disabled' : '' %>">
                  <a class="page-link" href="?page=<%= pagination.page - 1 %>&search=<%= search %>&table=<%= tableFilter %>&action=<%= actionFilter %>&perpage=<%= pagination.perPage %>">
                    Previous
                  </a>
                </li>
                
                <% for (let i = Math.max(1, pagination.page - 2); i <= Math.min(pagination.totalPages, pagination.page + 2); i++) { %>
                  <li class="page-item <%= pagination.page === i ? 'active' : '' %>">
                    <a class="page-link" href="?page=<%= i %>&search=<%= search %>&table=<%= tableFilter %>&action=<%= actionFilter %>&perpage=<%= pagination.perPage %>">
                      <%= i %>
                    </a>
                  </li>
                <% } %>
                
                <li class="page-item <%= pagination.page === pagination.totalPages ? 'disabled' : '' %>">
                  <a class="page-link" href="?page=<%= pagination.page + 1 %>&search=<%= search %>&table=<%= tableFilter %>&action=<%= actionFilter %>&perpage=<%= pagination.perPage %>">
                    Next
                  </a>
                </li>
              </ul>
            </nav>
            
            <div class="text-center text-muted">
              Showing <%= (pagination.page - 1) * pagination.perPage + 1 %> to 
              <%= Math.min(pagination.page * pagination.perPage, pagination.total) %> of 
              <%= pagination.total %> permissions
            </div>
          <% } %>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
// Helper function for action badge classes
function getActionBadgeClass(action) {
  const classes = {
    'browse': 'primary',
    'read': 'info',
    'edit': 'warning',
    'add': 'success',
    'delete': 'danger'
  };
  return classes[action] || 'secondary';
}

// Sync permissions function
function syncPermissions() {
  if (confirm('Are you sure you want to sync permissions? This will create permissions for all active tables.')) {
    fetch('/admin/permissions/sync', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Requested-With': 'XMLHttpRequest'
      }
    })
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        alert('Permissions synced successfully!');
        location.reload();
      } else {
        alert('Error syncing permissions: ' + data.message);
      }
    })
    .catch(error => {
      console.error('Error:', error);
      alert('Error syncing permissions');
    });
  }
}

// Delete permission function
function deletePermission(permissionId) {
  if (confirm('Are you sure you want to delete this permission? This action cannot be undone.')) {
    fetch(`/admin/permissions/${permissionId}`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
        'X-Requested-With': 'XMLHttpRequest'
      }
    })
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        alert('Permission deleted successfully!');
        location.reload();
      } else {
        alert('Error deleting permission: ' + data.message);
      }
    })
    .catch(error => {
      console.error('Error:', error);
      alert('Error deleting permission');
    });
  }
}
</script>

<%
// Helper function for EJS template
function getActionBadgeClass(action) {
  const classes = {
    'browse': 'primary',
    'read': 'info', 
    'edit': 'warning',
    'add': 'success',
    'delete': 'danger'
  };
  return classes[action] || 'secondary';
}
%>
