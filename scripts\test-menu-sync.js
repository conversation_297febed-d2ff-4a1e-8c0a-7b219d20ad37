// Test script để kiểm tra menu sync
const menuService = require('../services/menuService');
const adminService = require('../services/adminService');

async function testMenuSync() {
  try {
    console.log('🧪 Testing menu sync functionality...\n');

    // 1. Kiểm tra menu hiện tại
    console.log('1. Current menus:');
    const currentMenus = await menuService.getAllMenus();
    console.log(`   Found ${currentMenus.length} root menus`);
    
    currentMenus.forEach(menu => {
      console.log(`   - ${menu.title} (${menu.children.length} children)`);
    });

    // 2. Kiểm tra admin tables
    console.log('\n2. Available admin tables:');
    const tables = await adminService.getAllAdminTables();
    console.log(`   Found ${tables.length} admin tables`);
    
    tables.forEach(table => {
      console.log(`   - ${table.display_name} (${table.name}) - Active: ${table.is_active}`);
    });

    // 3. Test sync table menus
    console.log('\n3. Testing sync table menus...');
    const syncResult = await menuService.syncTableMenus();
    console.log(`   ✅ ${syncResult.message}`);

    // 4. Kiểm tra menu sau sync
    console.log('\n4. Menus after sync:');
    const updatedMenus = await menuService.getAllMenus();
    console.log(`   Found ${updatedMenus.length} root menus`);
    
    updatedMenus.forEach(menu => {
      console.log(`   - ${menu.title} (${menu.children.length} children)`);
      if (menu.children.length > 0) {
        menu.children.forEach(child => {
          console.log(`     └─ ${child.title} → ${child.url}`);
        });
      }
    });

    // 5. Test tạo menu mới
    console.log('\n5. Testing create new menu...');
    const newMenu = await menuService.createMenu({
      title: 'Test Menu',
      url: '/test',
      icon: 'star',
      order_index: 999,
      badge_text: 'TEST',
      badge_color: 'warning'
    });
    console.log(`   ✅ Created menu: ${newMenu.title} (ID: ${newMenu.id})`);

    // 6. Test update menu
    console.log('\n6. Testing update menu...');
    const updatedMenu = await menuService.updateMenu(newMenu.id, {
      title: 'Updated Test Menu',
      badge_text: 'UPDATED',
      badge_color: 'success'
    });
    console.log(`   ✅ Updated menu: ${updatedMenu.title}`);

    // 7. Test delete menu
    console.log('\n7. Testing delete menu...');
    await menuService.deleteMenu(newMenu.id);
    console.log(`   ✅ Deleted test menu`);

    // 8. Final menu count
    console.log('\n8. Final menu structure:');
    const finalMenus = await menuService.getFlatMenuList();
    console.log(`   Total menus: ${finalMenus.length}`);
    
    const activeMenus = finalMenus.filter(m => m.is_active);
    const titleMenus = finalMenus.filter(m => m.is_title);
    const tableMenus = finalMenus.filter(m => m.table_id);
    
    console.log(`   - Active: ${activeMenus.length}`);
    console.log(`   - Titles: ${titleMenus.length}`);
    console.log(`   - Table-linked: ${tableMenus.length}`);

    console.log('\n✅ All menu tests passed!');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Stack trace:', error.stack);
  }
}

// Chạy test
if (require.main === module) {
  testMenuSync().then(() => {
    console.log('\n🎉 Test completed!');
    process.exit(0);
  }).catch(error => {
    console.error('💥 Test crashed:', error);
    process.exit(1);
  });
}

module.exports = { testMenuSync };
