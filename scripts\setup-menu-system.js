// <PERSON>rip<PERSON> để setup hệ thống menu
const mysql = require('mysql2/promise');
const menuService = require('../services/menuService');

async function setupMenuSystem() {
  let connection;
  
  try {
    console.log('🚀 Setting up menu system...\n');

    // Tạo connection đến database
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_NAME || 'coreui'
    });

    console.log('✅ Connected to database');

    // 1. Tạo bảng admin_menus
    console.log('\n1. Creating admin_menus table...');
    
    const createTableSQL = `
      CREATE TABLE IF NOT EXISTS admin_menus (
        id INT AUTO_INCREMENT PRIMARY KEY,
        title VARCHAR(255) NOT NULL,
        url VARCHAR(500) NULL,
        icon VARCHAR(100) NULL,
        parent_id INT NULL,
        order_index INT DEFAULT 0,
        is_active BOOLEAN DEFAULT TRUE,
        is_divider BOOLEAN DEFAULT FALSE,
        is_title BOOLEAN DEFAULT FALSE,
        target VARCHAR(50) NULL,
        badge_text VARCHAR(50) NULL,
        badge_color VARCHAR(50) NULL,
        table_id INT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        
        FOREIGN KEY (parent_id) REFERENCES admin_menus(id) ON DELETE CASCADE,
        FOREIGN KEY (table_id) REFERENCES admin_tables(id) ON DELETE CASCADE,
        
        INDEX idx_parent_id (parent_id),
        INDEX idx_table_id (table_id),
        INDEX idx_order (order_index),
        INDEX idx_active (is_active)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    `;

    await connection.execute(createTableSQL);
    console.log('✅ admin_menus table created successfully');

    // 2. Kiểm tra xem đã có menu nào chưa
    console.log('\n2. Checking existing menus...');
    const [existingMenus] = await connection.execute('SELECT COUNT(*) as count FROM admin_menus');
    
    if (existingMenus[0].count > 0) {
      console.log(`⚠️  Found ${existingMenus[0].count} existing menus. Skipping default menu creation.`);
      console.log('   Use the admin interface to manage menus or delete existing menus to recreate defaults.');
    } else {
      // 3. Tạo menu mặc định
      console.log('\n3. Creating default menus...');
      
      const defaultMenus = [
        // Root menus
        {
          title: 'Dashboard',
          url: '/',
          icon: 'speedometer',
          parent_id: null,
          order_index: 1,
          badge_text: 'NEW',
          badge_color: 'info'
        },
        
        // Database Admin section
        {
          title: 'Database Admin',
          url: null,
          icon: null,
          parent_id: null,
          order_index: 10,
          is_title: true
        },
        {
          title: 'Admin Dashboard',
          url: '/admin',
          icon: 'storage',
          parent_id: null,
          order_index: 11
        },
        {
          title: 'Manage Tables',
          url: '/admin/tables',
          icon: 'grid',
          parent_id: null,
          order_index: 12
        },
        {
          title: 'Menu Management',
          url: '/admin/menus',
          icon: 'menu',
          parent_id: null,
          order_index: 13
        },
        
        // Data Management section (will be populated by sync)
        {
          title: 'Data Management',
          url: null,
          icon: 'storage',
          parent_id: null,
          order_index: 100,
          is_title: true
        }
      ];

      for (const menuData of defaultMenus) {
        const insertSQL = `
          INSERT INTO admin_menus (title, url, icon, parent_id, order_index, is_active, is_title, is_divider, badge_text, badge_color)
          VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `;
        
        await connection.execute(insertSQL, [
          menuData.title,
          menuData.url,
          menuData.icon,
          menuData.parent_id,
          menuData.order_index,
          menuData.is_active !== false,
          menuData.is_title || false,
          menuData.is_divider || false,
          menuData.badge_text || null,
          menuData.badge_color || null
        ]);
        
        console.log(`   ✅ Created menu: ${menuData.title}`);
      }
    }

    // 4. Đồng bộ menu từ admin tables
    console.log('\n4. Syncing table menus...');
    try {
      // Sử dụng menuService để sync
      const result = await menuService.syncTableMenus();
      console.log(`✅ ${result.message}`);
    } catch (error) {
      console.log('⚠️  Could not sync table menus (this is normal if no admin tables exist yet)');
      console.log('   You can sync later using the admin interface');
    }

    // 5. Hiển thị kết quả
    console.log('\n5. Menu system setup complete!');
    const [finalCount] = await connection.execute('SELECT COUNT(*) as count FROM admin_menus WHERE is_active = TRUE');
    console.log(`📊 Total active menus: ${finalCount[0].count}`);

    console.log('\n🎉 Setup completed successfully!');
    console.log('\n📋 Next steps:');
    console.log('   1. Start your server: npm start');
    console.log('   2. Visit: http://localhost:3000/admin/menus');
    console.log('   3. Use "Sync Table Menus" to add your admin tables to the menu');
    console.log('   4. Customize menu items as needed');

  } catch (error) {
    console.error('❌ Setup failed:', error.message);
    console.error('Stack trace:', error.stack);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
      console.log('\n🔌 Database connection closed');
    }
  }
}

// Chạy setup nếu file được gọi trực tiếp
if (require.main === module) {
  setupMenuSystem().then(() => {
    console.log('\n✨ All done!');
    process.exit(0);
  }).catch(error => {
    console.error('💥 Setup crashed:', error);
    process.exit(1);
  });
}

module.exports = { setupMenuSystem };
