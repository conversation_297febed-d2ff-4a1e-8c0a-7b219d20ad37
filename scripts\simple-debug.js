const db = require('../config/database');

async function simpleDebug() {
  try {
    console.log('🔍 Simple debug for role_user...');

    // Check if role_user table exists
    const tables = await db.query("SHOW TABLES LIKE 'role_user'");
    console.log('role_user table exists:', tables.length > 0);

    if (tables.length > 0) {
      // Check data
      const data = await db.query('SELECT * FROM role_user LIMIT 3');
      console.log('Sample data:', data);

      // Check admin table
      const adminTable = await db.query('SELECT * FROM admintable WHERE name = ?', ['role_user']);
      console.log('Admin table:', adminTable);

      if (adminTable.length > 0) {
        // Check relations
        const relations = await db.query(`
          SELECT ar.*, 
                 ft.name as foreign_table_name, 
                 ac.name as column_name
          FROM adminrelation ar
          LEFT JOIN admintable ft ON ar.foreign_table_id = ft.id
          LEFT JOIN admincolumn ac ON ar.column_id = ac.id
          WHERE ar.table_id = ?
        `, [adminTable[0].id]);
        console.log('Relations:', relations);
      }
    }

  } catch (error) {
    console.error('Error:', error);
  }
  
  process.exit(0);
}

simpleDebug();
