const db = require('../config/database');

async function checkTableStructure() {
  try {
    // Connect to database first
    await db.connect();
    console.log('🔍 Checking table structures...\n');

    // Check user table structure
    console.log('📋 User table structure:');
    try {
      const userColumns = await db.query('DESCRIBE user');
      console.log('User columns:', userColumns.map(col => col.Field));
    } catch (error) {
      console.log('❌ Error checking user table:', error.message);
    }

    // Check users table structure (alternative name)
    console.log('\n📋 Users table structure:');
    try {
      const usersColumns = await db.query('DESCRIBE users');
      console.log('Users columns:', usersColumns.map(col => col.Field));
    } catch (error) {
      console.log('❌ Error checking users table:', error.message);
    }

    // Check role table structure
    console.log('\n📋 Role table structure:');
    try {
      const roleColumns = await db.query('DESCRIBE role');
      console.log('Role columns:', roleColumns.map(col => col.Field));
    } catch (error) {
      console.log('❌ Error checking role table:', error.message);
    }

    // Check roles table structure (alternative name)
    console.log('\n📋 Roles table structure:');
    try {
      const rolesColumns = await db.query('DESCRIBE roles');
      console.log('Roles columns:', rolesColumns.map(col => col.Field));
    } catch (error) {
      console.log('❌ Error checking roles table:', error.message);
    }

    // Check role_user table structure
    console.log('\n📋 Role_user table structure:');
    try {
      const roleUserColumns = await db.query('DESCRIBE role_user');
      console.log('Role_user columns:', roleUserColumns.map(col => col.Field));
    } catch (error) {
      console.log('❌ Error checking role_user table:', error.message);
    }

    // Check admin relations for role_user
    console.log('\n🔗 Admin relations for role_user:');
    try {
      const relations = await db.query(`
        SELECT ar.*, 
               t.name as table_name,
               ft.name as foreign_table_name, 
               ac.name as column_name
        FROM adminrelation ar
        LEFT JOIN admintable t ON ar.table_id = t.id
        LEFT JOIN admintable ft ON ar.foreign_table_id = ft.id
        LEFT JOIN admincolumn ac ON ar.column_id = ac.id
        WHERE t.name = 'role_user'
      `);
      
      relations.forEach(rel => {
        console.log(`- ${rel.column_name} -> ${rel.foreign_table_name}.${rel.foreign_column} (display: ${rel.display_column})`);
      });
    } catch (error) {
      console.log('❌ Error checking relations:', error.message);
    }

  } catch (error) {
    console.error('❌ Script error:', error);
  }
  
  process.exit(0);
}

checkTableStructure();
