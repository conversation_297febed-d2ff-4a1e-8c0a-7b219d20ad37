const db = require('../config/database');

async function removeSuperAdmin() {
  try {
    console.log('🗑️ Removing Super Admin from system...');

    // Initialize database connection
    db.connect('development');
    
    // 1. Find Super Admin role
    console.log('1. Finding Super Admin role...');
    const superAdminRole = await db.queryOne(`
      SELECT id, name FROM role WHERE name = 'Super Admin'
    `);

    if (!superAdminRole) {
      console.log('⚠️ Super Admin role not found, system already clean');
      return;
    }

    console.log(`Found Super Admin role with ID: ${superAdminRole.id}`);

    // 2. Find Admin role
    console.log('2. Finding Admin role...');
    let adminRole = await db.queryOne(`
      SELECT id, name FROM role WHERE name = 'Admin'
    `);

    if (!adminRole) {
      console.log('Creating Admin role...');
      const result = await db.query(`
        INSERT INTO role (name, description) VALUES ('Admin', 'Administrator role with full system access')
      `);
      adminRole = { id: result.insertId, name: 'Admin' };
      console.log(`✅ Created Admin role with ID: ${adminRole.id}`);
    }

    // 3. Migrate users from Super Admin to Admin
    console.log('3. Migrating users from Super Admin to Admin...');
    const superAdminUsers = await db.query(`
      SELECT ur.user_id, u.email 
      FROM user_role ur
      INNER JOIN user u ON ur.user_id = u.id
      WHERE ur.role_id = ?
    `, [superAdminRole.id]);

    console.log(`Found ${superAdminUsers.length} users with Super Admin role`);

    for (const user of superAdminUsers) {
      // Check if user already has Admin role
      const existingAdminRole = await db.queryOne(`
        SELECT id FROM user_role 
        WHERE user_id = ? AND role_id = ?
      `, [user.user_id, adminRole.id]);

      if (!existingAdminRole) {
        // Assign Admin role
        await db.query(`
          INSERT INTO user_role (user_id, role_id, assigned_at)
          VALUES (?, ?, NOW())
        `, [user.user_id, adminRole.id]);
        console.log(`   ✅ Migrated user ${user.email} to Admin role`);
      } else {
        console.log(`   ⚠️ User ${user.email} already has Admin role`);
      }
    }

    // 4. Grant all permissions to Admin role
    console.log('4. Granting all permissions to Admin role...');
    
    // Get all current permissions
    const allPermissions = await db.query('SELECT id FROM permissions');
    console.log(`Found ${allPermissions.length} permissions`);

    let grantedCount = 0;
    for (const permission of allPermissions) {
      // Check if Admin role already has this permission
      const existingPermission = await db.queryOne(`
        SELECT id FROM role_permissions 
        WHERE role_id = ? AND permission_id = ?
      `, [adminRole.id, permission.id]);

      if (!existingPermission) {
        await db.query(`
          INSERT INTO role_permissions (role_id, permission_id, granted_at)
          VALUES (?, ?, NOW())
        `, [adminRole.id, permission.id]);
        grantedCount++;
      }
    }

    console.log(`✅ Granted ${grantedCount} permissions to Admin role`);

    // 5. Remove Super Admin role permissions
    console.log('5. Removing Super Admin role permissions...');
    const deletedPermissions = await db.query(`
      DELETE FROM role_permissions WHERE role_id = ?
    `, [superAdminRole.id]);

    console.log(`✅ Removed ${deletedPermissions.affectedRows} role permissions`);

    // 6. Remove Super Admin user assignments
    console.log('6. Removing Super Admin user assignments...');
    const deletedUserRoles = await db.query(`
      DELETE FROM user_role WHERE role_id = ?
    `, [superAdminRole.id]);

    console.log(`✅ Removed ${deletedUserRoles.affectedRows} user role assignments`);

    // 7. Delete Super Admin role
    console.log('7. Deleting Super Admin role...');
    await db.query(`
      DELETE FROM role WHERE id = ?
    `, [superAdminRole.id]);

    console.log(`✅ Deleted Super Admin role`);

    // 8. Verify final state
    console.log('8. Verifying final state...');
    const remainingRoles = await db.query(`
      SELECT id, name, description FROM role ORDER BY id
    `);

    console.log('\n📋 Remaining roles:');
    remainingRoles.forEach(role => {
      console.log(`   - ${role.name} (ID: ${role.id})`);
    });

    const adminUserCount = await db.queryOne(`
      SELECT COUNT(*) as count FROM user_role 
      WHERE role_id = ?
    `, [adminRole.id]);

    console.log(`\n👥 Users with Admin role: ${adminUserCount.count}`);

    console.log('\n🎉 Super Admin removal completed successfully!');
    console.log('\nSystem now uses only:');
    console.log('- Admin role (full permissions)');
    console.log('- User role (limited permissions)');
    console.log('- Custom roles with specific permissions');

  } catch (error) {
    console.error('❌ Error removing Super Admin:', error);
  }
  
  process.exit(0);
}

removeSuperAdmin(); 