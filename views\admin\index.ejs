<div class="container-fluid px-4">
    <!-- Stats Cards -->
    <div class="row g-4 mb-4">
        <div class="col-sm-6 col-xl-3">
            <div class="card text-white bg-primary">
                <div class="card-body pb-0 d-flex justify-content-between align-items-start">
                    <div>
                        <div class="fs-4 fw-semibold"><%= stats.totalTables %></div>
                        <div>Total Tables</div>
                    </div>
                    <div class="dropdown">
                        <svg class="icon icon-xl">
                            <use xlink:href="/icons/sprites/free.svg#cil-storage"></use>
                        </svg>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-sm-6 col-xl-3">
            <div class="card text-white bg-success">
                <div class="card-body pb-0 d-flex justify-content-between align-items-start">
                    <div>
                        <div class="fs-4 fw-semibold"><%= stats.activeTables %></div>
                        <div>Active Tables</div>
                    </div>
                    <div class="dropdown">
                        <svg class="icon icon-xl">
                            <use xlink:href="/icons/sprites/free.svg#cil-check"></use>
                        </svg>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-sm-6 col-xl-3">
            <div class="card text-white bg-info">
                <div class="card-body pb-0 d-flex justify-content-between align-items-start">
                    <div>
                        <div class="fs-4 fw-semibold"><%= stats.totalColumns %></div>
                        <div>Total Columns</div>
                    </div>
                    <div class="dropdown">
                        <svg class="icon icon-xl">
                            <use xlink:href="/icons/sprites/free.svg#cil-columns"></use>
                        </svg>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-sm-6 col-xl-3">
            <div class="card text-white bg-warning">
                <div class="card-body pb-0 d-flex justify-content-between align-items-start">
                    <div>
                        <div class="fs-4 fw-semibold"><%= stats.totalRelations %></div>
                        <div>Relations</div>
                    </div>
                    <div class="dropdown">
                        <svg class="icon icon-xl">
                            <use xlink:href="/icons/sprites/free.svg#cil-link"></use>
                        </svg>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row g-4 mb-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Quick Actions</h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="/admin/tables" class="btn btn-primary">
                            <i class="fas fa-table me-2"></i>Manage Tables
                        </a>
                        <button type="button" class="btn btn-success" onclick="syncTables()">
                            <i class="fas fa-sync me-2"></i>Sync Database Tables
                        </button>
                        <button type="button" class="btn btn-info" data-toggle="modal" data-target="#createTableModal">
                            <i class="fas fa-plus me-2"></i>Create New Table
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Recent Tables</h5>
                </div>
                <div class="card-body">
                    <% if (tables && tables.length > 0) { %>
                        <div class="list-group list-group-flush">
                            <% tables.slice(0, 5).forEach(table => { %>
                                <div class="list-group-item d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="mb-1"><%= table.display_name %></h6>
                                        <small class="text-muted"><%= table.name %> (<%= table.columns.length %> columns)</small>
                                    </div>
                                    <div>
                                        <% if (table.is_active) { %>
                                            <span class="badge bg-success">Active</span>
                                        <% } else { %>
                                            <span class="badge bg-secondary">Inactive</span>
                                        <% } %>
                                    </div>
                                </div>
                            <% }); %>
                        </div>
                    <% } else { %>
                        <p class="text-muted">No tables found. <a href="#" onclick="syncTables()">Sync from database</a></p>
                    <% } %>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Create Table Modal -->
<div class="modal fade" id="createTableModal" tabindex="-1" aria-labelledby="createTableModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="createTableModalLabel">Create New Table</h5>
                <button type="button" class="btn-close" data-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="createTableForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="tableName" class="form-label">Table Name</label>
                                <input type="text" class="form-control" id="tableName" name="name" required>
                                <div class="form-text">Use lowercase with underscores (e.g., user_profiles)</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="displayName" class="form-label">Display Name</label>
                                <input type="text" class="form-control" id="displayName" name="display_name" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="description" class="form-label">Description</label>
                        <textarea class="form-control" id="description" name="description" rows="2"></textarea>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="modelName" class="form-label">Model Name</label>
                                <input type="text" class="form-control" id="modelName" name="model_name" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="icon" class="form-label">Icon</label>
                                <input type="text" class="form-control" id="icon" name="icon" placeholder="fas fa-table">
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="isActive" name="is_active" checked>
                        <label class="form-check-label" for="isActive">
                            Active (show in admin panel)
                        </label>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="createTable()">Create Table</button>
            </div>
        </div>
    </div>
</div>

<script>

// Auto-generate display name and model name from table name
document.getElementById('tableName').addEventListener('input', function() {
    const tableName = this.value;
    const displayName = tableName.split('_').map(word => 
        word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ');
    const modelName = tableName.split('_').map(word => 
        word.charAt(0).toUpperCase() + word.slice(1)
    ).join('');
    
    document.getElementById('displayName').value = displayName;
    document.getElementById('modelName').value = modelName;
});

// Sync tables from database
async function syncTables() {
    try {
        const response = await fetch('/admin/sync-tables', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        });
        
        const result = await response.json();
        
        if (result.success) {
            let message = result.message;
            if (result.data) {
                const { deleted, added } = result.data;
                if (deleted > 0 || added > 0) {
                    message = `Tables synced successfully!\n\nDeleted: ${deleted} table(s)\nAdded: ${added} table(s)`;
                }
            }
            alert(message);
            location.reload();
        } else {
            alert('Error syncing tables: ' + result.message);
        }
    } catch (error) {
        console.error('Error:', error);
        alert('Error syncing tables');
    }
}

// Create new table
async function createTable() {
    try {
        const form = document.getElementById('createTableForm');
        const formData = new FormData(form);
        const data = Object.fromEntries(formData);
        data.is_active = document.getElementById('isActive').checked;
        
        const response = await fetch('/admin/tables', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(data)
        });
        
        const result = await response.json();
        
        if (result.success) {
            alert('Table created successfully!');
            location.reload();
        } else {
            alert('Error creating table: ' + result.message);
        }
    } catch (error) {
        console.error('Error:', error);
        alert('Error creating table');
    }
}
</script>
