const express = require('express');
const router = express.Router();
const roleController = require('../controller/roleController');
const { checkSpecificPermission, requireAdmin, injectUserPermissions } = require('../middleware/permissionMiddleware');

// Middleware inject user permissions cho tất cả routes
router.use(injectUserPermissions());

// ========== ROLE ROUTES ==========

// Danh sách roles
router.get('/', 
  checkSpecificPermission('read_roles'),
  roleController.index
);

// API DataTables cho roles
router.get('/api', 
  checkSpecificPermission('read_roles'),
  roleController.api
);

// API thống kê roles
router.get('/stats', 
  checkSpecificPermission('read_roles'),
  roleController.stats
);

// Form tạo role mới
router.get('/create', 
  checkSpecificPermission('add_roles'),
  roleController.create
);

// Lưu role mới
router.post('/', 
  checkSpecificPermission('add_roles'),
  roleController.store
);

// Chi tiết role
router.get('/:id', 
  checkSpecificPermission('read_roles'),
  roleController.show
);

// Form chỉnh sửa role
router.get('/:id/edit', 
  checkSpecificPermission('edit_roles'),
  roleController.edit
);

// Cập nhật role
router.put('/:id', 
  checkSpecificPermission('edit_roles'),
  roleController.update
);

// Xóa role
router.delete('/:id', 
  checkSpecificPermission('delete_roles'),
  roleController.destroy
);

module.exports = router; 