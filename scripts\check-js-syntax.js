const fs = require('fs');
const path = require('path');

function checkJSSyntax() {
  console.log('🔍 Checking JavaScript syntax in table-data.ejs...\n');

  try {
    const filePath = path.join(__dirname, '../views/admin/table-data.ejs');
    const content = fs.readFileSync(filePath, 'utf8');
    
    // Extract JavaScript sections
    const scriptMatches = content.match(/<script[^>]*>([\s\S]*?)<\/script>/g);
    
    if (!scriptMatches) {
      console.log('❌ No script sections found');
      return;
    }

    console.log(`📋 Found ${scriptMatches.length} script section(s)\n`);

    scriptMatches.forEach((script, index) => {
      console.log(`🔍 Checking script section ${index + 1}:`);
      
      // Extract JavaScript code
      const jsCode = script.replace(/<script[^>]*>/, '').replace(/<\/script>/, '');
      
      // Check for common duplicate declaration patterns
      const duplicatePatterns = [
        /const\s+(\w+)\s*=/g,
        /let\s+(\w+)\s*=/g,
        /var\s+(\w+)\s*=/g
      ];

      const declarations = new Map();
      let hasErrors = false;

      duplicatePatterns.forEach(pattern => {
        let match;
        while ((match = pattern.exec(jsCode)) !== null) {
          const varName = match[1];
          if (declarations.has(varName)) {
            console.log(`  ❌ Duplicate declaration: ${varName}`);
            hasErrors = true;
          } else {
            declarations.set(varName, true);
          }
        }
      });

      // Check for EJS template variables that might cause issues
      const ejsVariables = jsCode.match(/<%[\s\S]*?%>/g);
      if (ejsVariables) {
        console.log(`  📝 Found ${ejsVariables.length} EJS template sections`);
        
        // Check for potential issues in EJS loops
        ejsVariables.forEach(ejs => {
          if (ejs.includes('forEach') && ejs.includes('const ')) {
            console.log(`  ⚠️ Potential duplicate declaration in EJS loop: ${ejs.substring(0, 50)}...`);
          }
        });
      }

      if (!hasErrors) {
        console.log(`  ✅ No duplicate declarations found in script ${index + 1}`);
      }
      
      console.log('');
    });

    // Check for specific problematic patterns
    console.log('🔍 Checking for specific problematic patterns:');
    
    const problematicPatterns = [
      { pattern: /const\s+relation\s*=/g, name: 'const relation' },
      { pattern: /const\s+fieldElement\s*=/g, name: 'const fieldElement' },
      { pattern: /const\s+checkboxElement\s*=/g, name: 'const checkboxElement' },
      { pattern: /const\s+recordValue\s*=/g, name: 'const recordValue' },
      { pattern: /const\s+prefix\s*=/g, name: 'const prefix' }
    ];

    problematicPatterns.forEach(({ pattern, name }) => {
      const matches = content.match(pattern);
      if (matches && matches.length > 1) {
        console.log(`  ❌ Multiple ${name} declarations found: ${matches.length}`);
      } else if (matches) {
        console.log(`  ✅ Single ${name} declaration found`);
      } else {
        console.log(`  ✅ No ${name} declarations found`);
      }
    });

    console.log('\n✅ JavaScript syntax check completed!');

  } catch (error) {
    console.error('❌ Error checking JavaScript syntax:', error.message);
  }
}

checkJSSyntax();
