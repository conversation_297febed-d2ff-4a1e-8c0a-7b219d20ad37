const mysql = require('mysql2/promise');
const db = require('../config/database');

async function setupPermissionSystem() {
  try {
    console.log('🔧 Setting up dynamic permission system...');

    // Initialize database connection
    db.connect('development');
    
    // 1. Create permissions table
    console.log('1. Creating permissions table...');
    await db.query(`
      CREATE TABLE IF NOT EXISTS permissions (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL UNIQUE,
        display_name VARCHAR(255) NOT NULL,
        description TEXT NULL,
        table_name VARCHAR(255) NULL,
        action VARCHAR(50) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        
        INDEX idx_table_action (table_name, action),
        INDEX idx_name (name)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);

    // 2. Create role_permissions table
    console.log('2. Creating role_permissions table...');
    await db.query(`
      CREATE TABLE IF NOT EXISTS role_permissions (
        id INT AUTO_INCREMENT PRIMARY KEY,
        role_id INT NOT NULL,
        permission_id INT NOT NULL,
        granted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        granted_by INT NULL,
        
        UNIQUE KEY unique_role_permission (role_id, permission_id),
        FOREIGN KEY (role_id) REFERENCES role(id) ON DELETE CASCADE,
        FOREIGN KEY (permission_id) REFERENCES permissions(id) ON DELETE CASCADE,
        FOREIGN KEY (granted_by) REFERENCES user(id) ON DELETE SET NULL,
        
        INDEX idx_role_id (role_id),
        INDEX idx_permission_id (permission_id)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);

    // Connection is managed by the db service, no need to close manually

    // 3. Insert basic system permissions
    console.log('3. Creating basic system permissions...');
    
    const systemPermissions = [
      // Table management permissions
      {
        name: 'browse_tables',
        display_name: 'Browse Tables',
        description: 'Quyền truy cập danh sách bảng và quản lý cấu trúc bảng',
        table_name: 'admintable',
        action: 'browse'
      },
      {
        name: 'read_tables',
        display_name: 'Read Tables',
        description: 'Quyền xem chi tiết cấu trúc bảng',
        table_name: 'admintable',
        action: 'read'
      },
      {
        name: 'edit_tables',
        display_name: 'Edit Tables',
        description: 'Quyền chỉnh sửa cấu trúc bảng',
        table_name: 'admintable',
        action: 'edit'
      },
      {
        name: 'add_tables',
        display_name: 'Add Tables',
        description: 'Quyền tạo bảng mới',
        table_name: 'admintable',
        action: 'add'
      },
      {
        name: 'delete_tables',
        display_name: 'Delete Tables',
        description: 'Quyền xóa bảng',
        table_name: 'admintable',
        action: 'delete'
      },

      // Menu management permissions
      {
        name: 'browse_menus',
        display_name: 'Browse Menus',
        description: 'Quyền truy cập danh sách menu',
        table_name: 'admin_menus',
        action: 'browse'
      },
      {
        name: 'read_menus',
        display_name: 'Read Menus',
        description: 'Quyền xem chi tiết menu',
        table_name: 'admin_menus',
        action: 'read'
      },
      {
        name: 'edit_menus',
        display_name: 'Edit Menus',
        description: 'Quyền chỉnh sửa menu',
        table_name: 'admin_menus',
        action: 'edit'
      },
      {
        name: 'add_menus',
        display_name: 'Add Menus',
        description: 'Quyền tạo menu mới',
        table_name: 'admin_menus',
        action: 'add'
      },
      {
        name: 'delete_menus',
        display_name: 'Delete Menus',
        description: 'Quyền xóa menu',
        table_name: 'admin_menus',
        action: 'delete'
      },

      // Role management permissions
      {
        name: 'browse_roles',
        display_name: 'Browse Roles',
        description: 'Quyền truy cập danh sách vai trò',
        table_name: 'role',
        action: 'browse'
      },
      {
        name: 'read_roles',
        display_name: 'Read Roles',
        description: 'Quyền xem chi tiết vai trò',
        table_name: 'role',
        action: 'read'
      },
      {
        name: 'edit_roles',
        display_name: 'Edit Roles',
        description: 'Quyền chỉnh sửa vai trò',
        table_name: 'role',
        action: 'edit'
      },
      {
        name: 'add_roles',
        display_name: 'Add Roles',
        description: 'Quyền tạo vai trò mới',
        table_name: 'role',
        action: 'add'
      },
      {
        name: 'delete_roles',
        display_name: 'Delete Roles',
        description: 'Quyền xóa vai trò',
        table_name: 'role',
        action: 'delete'
      },

      // Permission management permissions
      {
        name: 'browse_permissions',
        display_name: 'Browse Permissions',
        description: 'Quyền truy cập danh sách quyền',
        table_name: 'permissions',
        action: 'browse'
      },
      {
        name: 'read_permissions',
        display_name: 'Read Permissions',
        description: 'Quyền xem chi tiết quyền',
        table_name: 'permissions',
        action: 'read'
      },
      {
        name: 'edit_permissions',
        display_name: 'Edit Permissions',
        description: 'Quyền chỉnh sửa quyền',
        table_name: 'permissions',
        action: 'edit'
      },
      {
        name: 'add_permissions',
        display_name: 'Add Permissions',
        description: 'Quyền tạo quyền mới',
        table_name: 'permissions',
        action: 'add'
      },
      {
        name: 'delete_permissions',
        display_name: 'Delete Permissions',
        description: 'Quyền xóa quyền',
        table_name: 'permissions',
        action: 'delete'
      }
    ];

    // Insert permissions
    for (const permission of systemPermissions) {
      await db.query(`
        INSERT IGNORE INTO permissions (name, display_name, description, table_name, action)
        VALUES (?, ?, ?, ?, ?)
      `, [
        permission.name,
        permission.display_name,
        permission.description,
        permission.table_name,
        permission.action
      ]);
    }

    console.log(`✅ Created ${systemPermissions.length} system permissions`);

    // 4. Auto-generate permissions for existing tables
    console.log('4. Auto-generating permissions for existing tables...');
    
    const existingTables = await db.query(`
      SELECT name, display_name FROM admintable WHERE is_active = 1
    `);

    let generatedCount = 0;
    for (const table of existingTables) {
      const tablePermissions = [
        {
          name: `browse_${table.name}`,
          display_name: `Browse ${table.display_name}`,
          description: `Quyền truy cập danh sách ${table.display_name}`,
          table_name: table.name,
          action: 'browse'
        },
        {
          name: `read_${table.name}`,
          display_name: `Read ${table.display_name}`,
          description: `Quyền xem chi tiết ${table.display_name}`,
          table_name: table.name,
          action: 'read'
        },
        {
          name: `edit_${table.name}`,
          display_name: `Edit ${table.display_name}`,
          description: `Quyền chỉnh sửa ${table.display_name}`,
          table_name: table.name,
          action: 'edit'
        },
        {
          name: `add_${table.name}`,
          display_name: `Add ${table.display_name}`,
          description: `Quyền tạo ${table.display_name} mới`,
          table_name: table.name,
          action: 'add'
        },
        {
          name: `delete_${table.name}`,
          display_name: `Delete ${table.display_name}`,
          description: `Quyền xóa ${table.display_name}`,
          table_name: table.name,
          action: 'delete'
        }
      ];

      for (const permission of tablePermissions) {
        await db.query(`
          INSERT IGNORE INTO permissions (name, display_name, description, table_name, action)
          VALUES (?, ?, ?, ?, ?)
        `, [
          permission.name,
          permission.display_name,
          permission.description,
          permission.table_name,
          permission.action
        ]);
        generatedCount++;
      }
    }

    console.log(`✅ Generated ${generatedCount} permissions for ${existingTables.length} existing tables`);

    // 5. Grant all permissions to Admin role (role_id = 1)
    console.log('5. Granting all permissions to Admin role...');
    
    const allPermissions = await db.query('SELECT id FROM permissions');
    const adminRoleId = 1; // Assuming Admin role has ID = 1

    for (const permission of allPermissions) {
      await db.query(`
        INSERT IGNORE INTO role_permissions (role_id, permission_id)
        VALUES (?, ?)
      `, [adminRoleId, permission.id]);
    }

    console.log(`✅ Granted ${allPermissions.length} permissions to Admin role`);

    console.log('\n🎉 Permission system setup completed successfully!');
    console.log('\nNext steps:');
    console.log('- Run the permission service to manage permissions');
    console.log('- Apply permission middleware to routes');
    console.log('- Create permission management interface');

  } catch (error) {
    console.error('❌ Error setting up permission system:', error);
  }
  
  process.exit(0);
}

setupPermissionSystem();
