<style>
  .role-badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
  }
  
  .stats-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
  }
  
  .filter-card {
    background: white;
    border-radius: 10px;
    padding: 1.5rem;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    border: 1px solid #e3f2fd;
  }
  
  .table-actions .btn {
    margin-right: 0.25rem;
  }
  
  .permission-count-badge {
    background: linear-gradient(45deg, #2196F3, #21CBF3);
    color: white;
    border-radius: 20px;
    padding: 0.25rem 0.75rem;
    font-size: 0.75rem;
    font-weight: 600;
  }
  
  .users-count-badge {
    background: linear-gradient(45deg, #4CAF50, #45a049);
    color: white;
    border-radius: 20px;
    padding: 0.25rem 0.75rem;
    font-size: 0.75rem;
    font-weight: 600;
  }
</style>

<div class="container-fluid">
  <!-- Header -->
  <div class="d-sm-flex align-items-center justify-content-between mb-4">
    <div>
      <h1 class="h3 mb-0 text-gray-800">
        <i class="fas fa-user-tag text-primary me-2"></i>
        Quản lý Roles
      </h1>
      <p class="text-muted mb-0">Quản lý vai trò người dùng trong hệ thống</p>
    </div>
    <div class="btn-group" role="group">
      <a href="/admin/roles/create" class="btn btn-primary">
        <i class="fas fa-plus me-1"></i>Tạo Role Mới
      </a>
      <a href="/admin/permissions" class="btn btn-outline-info">
        <i class="fas fa-shield-alt me-1"></i>Quản lý Permissions
      </a>
    </div>
  </div>

  <!-- Stats -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="stats-card">
        <div class="row">
          <div class="col-md-3 text-center">
            <h3 class="mb-1" id="statTotal">-</h3>
            <p class="mb-0">Tổng Roles</p>
          </div>
          <div class="col-md-3 text-center">
            <h3 class="mb-1" id="statPermissions">-</h3>
            <p class="mb-0">Permissions được gán</p>
          </div>
          <div class="col-md-3 text-center">
            <h3 class="mb-1" id="statUsers">-</h3>
            <p class="mb-0">Users có roles</p>
          </div>
          <div class="col-md-3 text-center">
            <h3 class="mb-1" id="statAvg">-</h3>
            <p class="mb-0">Avg Permissions/Role</p>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Filters -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="filter-card">
        <div class="row">
          <div class="col-md-6 mb-3">
            <label for="search" class="form-label">Tìm kiếm</label>
            <input type="text" class="form-control" id="search" name="search" 
                   placeholder="Tìm theo tên role...">
          </div>
          <div class="col-md-3 mb-3">
            <label class="form-label">&nbsp;</label>
            <div class="d-grid">
              <button type="button" class="btn btn-secondary" onclick="clearFilters()">
                <i class="fas fa-times me-1"></i>Clear
              </button>
            </div>
          </div>
          <div class="col-md-3 mb-3">
            <label class="form-label">&nbsp;</label>
            <div class="d-grid">
              <button type="button" class="btn btn-info" onclick="refreshStats()">
                <i class="fas fa-sync-alt me-1"></i>Refresh Stats
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Roles Table -->
  <div class="row">
    <div class="col-12">
      <div class="card shadow">
        <div class="card-header py-3">
          <h6 class="m-0 font-weight-bold text-primary">
            Danh sách Roles
            <span class="badge bg-secondary ms-2" id="totalCount">-</span>
          </h6>
        </div>
        <div class="card-body">
          <div class="table-responsive">
            <table id="roles-table" class="table table-bordered table-hover" style="width:100%">
              <thead class="table-light">
                <tr>
                  <th>ID</th>
                  <th>Tên Role</th>
                  <th>Permissions</th>
                  <th>Users</th>
                  <th>Thao tác</th>
                </tr>
              </thead>
              <tbody>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Delete Role Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="deleteModalLabel">
          <i class="fas fa-exclamation-triangle text-warning me-2"></i>
          Xác nhận xóa Role
        </h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <p class="mb-0">Bạn có chắc chắn muốn xóa role <strong id="deleteRoleName"></strong>?</p>
        <div class="alert alert-warning mt-3">
          <i class="fas fa-exclamation-triangle me-2"></i>
          <strong>Cảnh báo:</strong> Hành động này không thể hoàn tác!
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Hủy</button>
        <button type="button" class="btn btn-danger" id="confirmDelete">
          <i class="fas fa-trash me-1"></i>Xóa Role
        </button>
      </div>
    </div>
  </div>
</div>

<!-- DataTables CSS & JS -->
<script src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.13.7/js/dataTables.bootstrap5.min.js"></script>
<link rel="stylesheet" href="https://cdn.datatables.net/1.13.7/css/dataTables.bootstrap5.min.css">

<script>
let rolesTable;

// Initialize page
$(document).ready(function() {
  // Initialize DataTable
  rolesTable = $('#roles-table').DataTable({
    processing: true,
    serverSide: true,
    ajax: {
      url: '/admin/roles/api',
      type: 'GET',
      data: function(d) {
        // Add custom filters
        d.search_custom = $('#search').val();
      }
    },
    columns: [
      {
        data: 'id',
        name: 'id',
        title: 'ID',
        width: '60px'
      },
      {
        data: 'name',
        name: 'name',
        title: 'Tên Role',
        render: function(data, type, row) {
          return `<strong class="text-primary">${data}</strong>`;
        }
      },
      {
        data: 'permissions_count',
        name: 'permissions_count',
        title: 'Permissions',
        render: function(data, type, row) {
          return `<span class="permission-count-badge">${data} permissions</span>`;
        }
      },
      {
        data: 'users_count',
        name: 'users_count',
        title: 'Users',
        render: function(data, type, row) {
          return `<span class="users-count-badge">${data} users</span>`;
        }
      },
      {
        data: 'actions',
        name: 'actions',
        title: 'Thao tác',
        orderable: false,
        searchable: false,
        render: function(data, type, row) {
          return `
            <div class="btn-group btn-group-sm" role="group">
              <a href="/admin/roles/${row.id}" 
                 class="btn btn-outline-info" title="Xem chi tiết">
                <i class="fas fa-eye"></i>
              </a>
              <a href="/admin/permissions/roles/${row.id}" 
                 class="btn btn-outline-success" title="Quản lý quyền">
                <i class="fas fa-shield-alt"></i>
              </a>
              <a href="/admin/roles/${row.id}/edit" 
                 class="btn btn-outline-warning" title="Chỉnh sửa">
                <i class="fas fa-edit"></i>
              </a>
              <button type="button" class="btn btn-outline-danger" 
                      onclick="deleteRole(${row.id}, '${row.name}')" 
                      title="Xóa">
                <i class="fas fa-trash"></i>
              </button>
            </div>
          `;
        }
      }
    ],
    order: [[0, 'desc']],
    pageLength: 25,
    language: {
      processing: "Đang xử lý...",
      search: "Tìm kiếm:",
      lengthMenu: "Hiển thị _MENU_ mục",
      info: "Hiển thị _START_ đến _END_ trong tổng số _TOTAL_ mục",
      infoEmpty: "Hiển thị 0 đến 0 trong tổng số 0 mục",
      infoFiltered: "(lọc từ _MAX_ mục)",
      paginate: {
        first: "Đầu",
        last: "Cuối",
        next: "Sau",
        previous: "Trước"
      },
      emptyTable: "Không có dữ liệu"
    },
    drawCallback: function(settings) {
      // Update stats after each draw
      const info = this.api().page.info();
      document.getElementById('totalCount').textContent = info.recordsTotal;
      updateStats();
    }
  });

  // Load initial stats
  refreshStats();

  // Setup filter event listeners
  $('#search').on('keyup', debounce(function() {
    rolesTable.ajax.reload();
  }, 500));
});

// Update stats
async function updateStats() {
  try {
    const info = rolesTable.page.info();
    const total = info.recordsTotal;
    
    document.getElementById('statTotal').textContent = total;
  } catch (error) {
    console.error('Error updating stats:', error);
  }
}

// Refresh stats from API
async function refreshStats() {
  try {
    const response = await fetch('/admin/roles/stats');
    const result = await response.json();
    
    if (result.success) {
      document.getElementById('statTotal').textContent = result.data.total_roles;
      document.getElementById('statPermissions').textContent = result.data.total_permissions_granted;
      document.getElementById('statUsers').textContent = result.data.total_users_with_roles;
      document.getElementById('statAvg').textContent = result.data.avg_permissions_per_role;
    }
  } catch (error) {
    console.error('Error refreshing stats:', error);
  }
}

// Clear filters
function clearFilters() {
  document.getElementById('search').value = '';
  rolesTable.ajax.reload();
}

// Debounce function
function debounce(func, wait) {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}

// Delete role function
let deleteRoleId = null;

function deleteRole(id, name) {
  deleteRoleId = id;
  document.getElementById('deleteRoleName').textContent = name;
  $('#deleteModal').modal('show');
  //new bootstrap.Modal(document.getElementById('deleteModal')).show();
}

document.getElementById('confirmDelete').addEventListener('click', function() {
  if (deleteRoleId) {
    fetch(`/admin/roles/${deleteRoleId}`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
      }
    })
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        showAlert('success', data.message);
        rolesTable.ajax.reload();
        refreshStats();
      } else {
        showAlert('danger', data.message);
      }
    })
    .catch(error => {
      showAlert('danger', 'Có lỗi xảy ra khi xóa role');
    });
    $('#deleteModal').modal('hide');
    //bootstrap.Modal.getInstance(document.getElementById('deleteModal')).hide();
  }
});

// Alert function
function showAlert(type, message) {
  const alertDiv = document.createElement('div');
  alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
  alertDiv.style.top = '20px';
  alertDiv.style.right = '20px';
  alertDiv.style.zIndex = '9999';
  alertDiv.innerHTML = `
    ${message}
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
  `;
  document.body.appendChild(alertDiv);
  
  setTimeout(() => {
    if (alertDiv.parentNode) {
      alertDiv.parentNode.removeChild(alertDiv);
    }
  }, 5000);
}
</script> 