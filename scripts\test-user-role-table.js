const db = require('../config/database');

async function testUserRoleTable() {
  try {
    console.log('🔍 Testing user_role table setup...\n');
    
    db.connect('development');
    
    // 1. Check if user_role table exists
    console.log('1. Checking if user_role table exists...');
    try {
      const userRoles = await db.query('SELECT * FROM user_role LIMIT 1');
      console.log('✅ user_role table exists');
    } catch (error) {
      if (error.code === 'ER_NO_SUCH_TABLE') {
        console.log('❌ user_role table does not exist');
        console.log('📋 Creating user_role table...');
        
        await db.query(`
          CREATE TABLE user_role (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            role_id INT NOT NULL,
            assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            assigned_by INT NULL,
            is_active BOOLEAN DEFAULT TRUE,
            
            UNIQUE KEY unique_user_role (user_id, role_id),
            <PERSON>OREIG<PERSON> KEY (user_id) REFERENCES user(id) ON DELETE CASCADE,
            FOREI<PERSON><PERSON> KEY (role_id) REFERENCES role(id) ON DELETE CASCADE,
            FOREIGN KEY (assigned_by) REFERENCES user(id) ON DELETE SET NULL,
            
            INDEX idx_user_id (user_id),
            INDEX idx_role_id (role_id)
          ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        `);
        
        console.log('✅ user_role table created');
        
        // Insert default user roles
        console.log('📋 Inserting default user roles...');
        await db.query(`
          INSERT INTO user_role (user_id, role_id, assigned_by) VALUES
          (1, 1, 1), -- Admin -> Admin role
          (2, 2, 1), -- Manager -> Manager role
          (3, 3, 1), -- Editor -> Editor role
          (4, 4, 1)  -- User -> User role
        `);
        console.log('✅ Default user roles inserted');
      } else {
        throw error;
      }
    }
    
    // 2. Check if role_user table exists (old table)
    console.log('\n2. Checking if old role_user table exists...');
    try {
      const roleUsers = await db.query('SELECT * FROM role_user LIMIT 1');
      console.log('⚠️  Old role_user table still exists');
      
      // Migrate data if needed
      const userRoleCount = await db.queryOne('SELECT COUNT(*) as count FROM user_role');
      if (userRoleCount.count === 0) {
        console.log('📋 Migrating data from role_user to user_role...');
        await db.query(`
          INSERT INTO user_role (user_id, role_id, assigned_at, assigned_by, is_active)
          SELECT user_id, role_id, assigned_at, assigned_by, is_active 
          FROM role_user
        `);
        console.log('✅ Data migrated successfully');
      }
      
    } catch (error) {
      if (error.code === 'ER_NO_SUCH_TABLE') {
        console.log('✅ Old role_user table does not exist (good)');
      } else {
        throw error;
      }
    }
    
    // 3. Test permissions service
    console.log('\n3. Testing permission service with user_role...');
    const permissionService = require('../services/permissionService');
    
    const isAdmin = await permissionService.isAdmin(1);
    console.log(`✅ Admin check: ${isAdmin}`);
    
    const hasPermission = await permissionService.checkUserPermission(1, 'read_admintable');
    console.log(`✅ Permission check: ${hasPermission}`);
    
    // 4. Check user role assignments
    console.log('\n4. Checking user role assignments...');
    const userRoles = await db.query(`
      SELECT u.email, r.name as role_name, ur.assigned_at
      FROM user_role ur
      JOIN user u ON ur.user_id = u.id
      JOIN role r ON ur.role_id = r.id
      ORDER BY u.id
    `);
    
    console.log('📋 User role assignments:');
    userRoles.forEach(ur => {
      console.log(`   - ${ur.email}: ${ur.role_name}`);
    });
    
    console.log('\n🎉 user_role table test completed successfully!');
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  } finally {
    try {
      db.get().end();
      console.log('\n🔌 Database connection closed');
    } catch (e) {
      console.log('⚠️  Database already closed');
    }
  }
}

testUserRoleTable();
