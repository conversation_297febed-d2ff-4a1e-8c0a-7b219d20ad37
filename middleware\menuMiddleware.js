const menuService = require('../services/menuService');

// Middleware để load menu data cho sidebar
const loadMenuData = async (req, res, next) => {
  try {
    // Chỉ load menu cho các trang không phải login/register
    if (req.path.includes('/login') || req.path.includes('/register')) {
      return next();
    }

    // Load menu data
    const menus = await menuService.getAllMenus();

    // Thêm menu data vào res.locals để có thể sử dụng trong views
    res.locals.sidebarMenus = menus;

    next();
  } catch (error) {
    console.error('Error loading menu data:', error);
    // Nếu có lỗi, sử dụng menu mặc định
    res.locals.sidebarMenus = getDefaultMenus();
    next();
  }
};

// Menu mặc định khi không load được từ database
function getDefaultMenus() {
  return [
    {
      id: 1,
      title: 'Dashboard',
      url: '/',
      icon: 'speedometer',
      order_index: 1,
      is_active: true,
      is_title: false,
      is_divider: false,
      badge_text: 'NEW',
      badge_color: 'info',
      children: []
    },
    {
      id: 2,
      title: 'Database Admin',
      url: null,
      icon: null,
      order_index: 10,
      is_active: true,
      is_title: true,
      is_divider: false,
      children: [
        {
          id: 3,
          title: 'Admin Dashboard',
          url: '/admin',
          icon: 'storage',
          order_index: 11,
          is_active: true,
          children: []
        },
        {
          id: 4,
          title: 'Manage Tables',
          url: '/admin/tables',
          icon: 'grid',
          order_index: 12,
          is_active: true,
          children: []
        },
        {
          id: 5,
          title: 'Menu Management',
          url: '/admin/menus',
          icon: 'menu',
          order_index: 13,
          is_active: true,
          children: []
        }
      ]
    }
  ];
}

module.exports = {
  loadMenuData
};
