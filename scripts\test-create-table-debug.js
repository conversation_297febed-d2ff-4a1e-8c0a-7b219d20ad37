const adminService = require('../services/adminService');
const prismaMigrationService = require('../services/prismaMigrationService');

async function testCreateTable() {
    try {
        console.log('=== Testing Table Creation ===');
        
        // Test data
        const testTableData = {
            name: 'test_table_debug',
            display_name: 'Test Table Debug',
            description: 'Test table for debugging',
            model_name: 'TestTableDebug',
            icon: 'fas fa-bug',
            order_index: 0,
            is_active: true,
            columns: [
                {
                    name: 'id',
                    display_name: 'ID',
                    type: 'int',
                    length: null,
                    default_value: null,
                    is_nullable: false,
                    is_primary: true,
                    is_unique: false,
                    is_auto_increment: true,
                    order_index: 0
                },
                {
                    name: 'name',
                    display_name: 'Name',
                    type: 'varchar',
                    length: 255,
                    default_value: null,
                    is_nullable: false,
                    is_primary: false,
                    is_unique: false,
                    is_auto_increment: false,
                    order_index: 1
                },
                {
                    name: 'created_at',
                    display_name: 'Created At',
                    type: 'datetime',
                    length: null,
                    default_value: 'CURRENT_TIMESTAMP',
                    is_nullable: false,
                    is_primary: false,
                    is_unique: false,
                    is_auto_increment: false,
                    order_index: 2
                }
            ]
        };

        console.log('1. Creating admin table record...');
        const adminTable = await adminService.createAdminTable(testTableData);
        console.log('✅ Admin table created:', adminTable.id);

        console.log('2. Checking if table exists in database...');
        const { exec } = require('child_process');
        const { promisify } = require('util');
        const execAsync = promisify(exec);

        try {
            // Kiểm tra xem bảng có tồn tại trong database không
            const { stdout } = await execAsync('npx prisma db execute --stdin', {
                cwd: require('path').join(__dirname, '..'),
                input: `SHOW TABLES LIKE '${testTableData.name}';`
            });
            console.log('Database check result:', stdout);
            
            if (stdout.includes(testTableData.name)) {
                console.log('✅ Table exists in database');
            } else {
                console.log('❌ Table does not exist in database');
            }
        } catch (error) {
            console.log('❌ Error checking database:', error.message);
        }

        console.log('3. Checking Prisma schema...');
        const fs = require('fs').promises;
        const schemaPath = require('path').join(__dirname, '../prisma/schema.prisma');
        const schemaContent = await fs.readFile(schemaPath, 'utf8');
        
        if (schemaContent.includes(`model ${testTableData.model_name}`)) {
            console.log('✅ Model exists in schema.prisma');
        } else {
            console.log('❌ Model not found in schema.prisma');
        }

        console.log('4. Testing Prisma client...');
        const prisma = require('../prisma/client');
        try {
            // Thử query bảng mới
            const result = await prisma.$queryRaw`SHOW TABLES LIKE ${testTableData.name}`;
            console.log('Prisma query result:', result);
            
            if (result && result.length > 0) {
                console.log('✅ Table accessible via Prisma client');
            } else {
                console.log('❌ Table not accessible via Prisma client');
            }
        } catch (error) {
            console.log('❌ Error querying via Prisma:', error.message);
        }

        console.log('=== Test completed ===');
        
    } catch (error) {
        console.error('❌ Test failed:', error);
        console.error('Stack trace:', error.stack);
    } finally {
        process.exit(0);
    }
}

testCreateTable(); 