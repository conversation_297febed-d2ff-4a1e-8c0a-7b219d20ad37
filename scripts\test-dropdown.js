const db = require('../config/database');

async function testDropdown() {
  try {
    console.log('Testing dropdown functionality...');
    
    // Test getting role data for dropdown
    const roles = await db.query('SELECT id, name FROM role ORDER BY name ASC');
    console.log('Roles for dropdown:', roles);
    
    // Test getting user data for dropdown
    const users = await db.query('SELECT id, fullname FROM user ORDER BY fullname ASC');
    console.log('Users for dropdown:', users);
    
  } catch (error) {
    console.error('Error testing dropdown:', error);
  }
}

testDropdown();
