// Test script để kiểm tra menu display
const menuService = require('../services/menuService');

async function testMenuDisplay() {
  try {
    console.log('🧪 Testing menu display functionality...\n');

    // 1. Test getAllMenus
    console.log('1. Testing getAllMenus():');
    const menus = await menuService.getAllMenus();
    console.log(`   Found ${menus.length} root menus`);
    
    menus.forEach(menu => {
      console.log(`   - ${menu.title} (Active: ${menu.is_active}, Title: ${menu.is_title}, Children: ${menu.children.length})`);
      if (menu.children.length > 0) {
        menu.children.forEach(child => {
          console.log(`     └─ ${child.title} (Active: ${child.is_active}) → ${child.url}`);
        });
      }
    });

    // 2. Test active menus only
    console.log('\n2. Active menus only:');
    const activeMenus = menus.filter(menu => menu.is_active);
    console.log(`   Found ${activeMenus.length} active root menus`);
    
    activeMenus.forEach(menu => {
      console.log(`   - ${menu.title} (Type: ${menu.is_title ? 'Title' : 'Menu'})`);
    });

    // 3. Test menu structure for sidebar
    console.log('\n3. Menu structure for sidebar:');
    const sidebarData = menus.map(menu => ({
      id: menu.id,
      title: menu.title,
      url: menu.url,
      icon: menu.icon,
      is_active: menu.is_active,
      is_title: menu.is_title,
      is_divider: menu.is_divider,
      badge_text: menu.badge_text,
      badge_color: menu.badge_color,
      children_count: menu.children.length,
      children: menu.children.map(child => ({
        title: child.title,
        url: child.url,
        is_active: child.is_active
      }))
    }));

    console.log('   Sidebar data structure:');
    console.log(JSON.stringify(sidebarData, null, 2));

    // 4. Test specific menu queries
    console.log('\n4. Menu statistics:');
    const totalMenus = await menuService.getFlatMenuList();
    const activeCount = totalMenus.filter(m => m.is_active).length;
    const titleCount = totalMenus.filter(m => m.is_title).length;
    const tableLinkedCount = totalMenus.filter(m => m.table_id).length;
    
    console.log(`   - Total menus: ${totalMenus.length}`);
    console.log(`   - Active menus: ${activeCount}`);
    console.log(`   - Title menus: ${titleCount}`);
    console.log(`   - Table-linked: ${tableLinkedCount}`);

    console.log('\n✅ All menu display tests completed!');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Stack trace:', error.stack);
  }
}

// Chạy test
if (require.main === module) {
  testMenuDisplay().then(() => {
    console.log('\n🎉 Test completed!');
    process.exit(0);
  }).catch(error => {
    console.error('💥 Test crashed:', error);
    process.exit(1);
  });
}

module.exports = { testMenuDisplay };
