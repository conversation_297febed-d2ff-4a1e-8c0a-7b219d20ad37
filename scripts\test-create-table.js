// Test script để kiểm tra việc tạo bảng và thêm cột
const adminService = require('../services/adminService');

async function testCreateTable() {
  try {
    console.log('🧪 Testing create table functionality...\n');

    // Test data
    const tableData = {
      name: 'test_table_' + Date.now(),
      display_name: 'Test Table',
      description: 'Test table for migration',
      model_name: 'TestTable',
      icon: 'fas fa-database',
      order_index: 1,
      is_active: true,
      columns: [
        {
          name: 'id',
          display_name: 'ID',
          type: 'int',
          is_primary: true,
          is_auto_increment: true,
          is_nullable: false,
          order_index: 0
        },
        {
          name: 'name',
          display_name: 'Name',
          type: 'varchar',
          length: 255,
          is_nullable: false,
          order_index: 1
        },
        {
          name: 'description',
          display_name: 'Description',
          type: 'text',
          is_nullable: true,
          order_index: 2
        },
        {
          name: 'created_at',
          display_name: 'Created At',
          type: 'datetime',
          is_nullable: false,
          default_value: 'CURRENT_TIMESTAMP',
          order_index: 3
        }
      ]
    };

    console.log('1. Creating admin table...');
    console.log('   Table name:', tableData.name);
    console.log('   Columns:', tableData.columns.length);

    const result = await adminService.createAdminTable(tableData);
    
    console.log('   ✅ Admin table created successfully!');
    console.log('   Admin table ID:', result.id);
    console.log('   Admin table name:', result.name);
    console.log('   Columns created:', result.columns.length);

    console.log('\n2. Testing table creation in database...');
    const schemaService = require('../services/schemaService');
    const tables = await schemaService.getAllTables();
    
    if (tables.includes(tableData.name)) {
      console.log('   ✅ Table exists in database');
    } else {
      console.log('   ❌ Table not found in database');
    }

    console.log('\n3. Testing Prisma schema...');
    const fs = require('fs').promises;
    const schemaPath = require('path').join(__dirname, '../prisma/schema.prisma');
    const schemaContent = await fs.readFile(schemaPath, 'utf8');
    
    if (schemaContent.includes(`model ${tableData.model_name}`)) {
      console.log('   ✅ Model found in schema.prisma');
    } else {
      console.log('   ❌ Model not found in schema.prisma');
    }

    console.log('\n✅ Create table test completed successfully!');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error(error.stack);
  }
}

// Chạy test
testCreateTable();

module.exports = { testCreateTable };
