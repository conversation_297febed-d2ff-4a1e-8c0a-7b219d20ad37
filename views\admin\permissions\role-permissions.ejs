<%- contentFor('title') %>
<%= title %>
<%- contentFor('title') %>

<%- contentFor('breadcrumb') %>
<nav aria-label="breadcrumb">
  <ol class="breadcrumb">
    <li class="breadcrumb-item"><a href="/admin">Dashboard</a></li>
    <li class="breadcrumb-item"><a href="/admin/crud/role">Roles</a></li>
    <li class="breadcrumb-item active" aria-current="page">Permissions</li>
  </ol>
</nav>
<%- contentFor('breadcrumb') %>

<div class="container-fluid">
  <div class="row">
    <div class="col-12">
      <div class="card">
        <div class="card-header">
          <div class="row align-items-center">
            <div class="col">
              <h4 class="card-title mb-0">
                <i class="fas fa-shield-alt"></i> Manage Permissions for Role: <strong><%= role.name %></strong>
              </h4>
              <% if (role.description) { %>
                <p class="text-muted mb-0"><%= role.description %></p>
              <% } %>
            </div>
            <div class="col-auto">
              <a href="/admin/crud/role" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to Roles
              </a>
            </div>
          </div>
        </div>

        <div class="card-body">
          <form id="rolePermissionsForm">
            <div class="row mb-3">
              <div class="col-md-6">
                <div class="form-check">
                  <input class="form-check-input" type="checkbox" id="selectAll">
                  <label class="form-check-label font-weight-bold" for="selectAll">
                    Select All Permissions
                  </label>
                </div>
              </div>
              <div class="col-md-6 text-right">
                <button type="submit" class="btn btn-primary">
                  <i class="fas fa-save"></i> Save Permissions
                </button>
              </div>
            </div>

            <!-- Permissions by Table -->
            <% Object.keys(permissionsByTable).forEach(tableName => { %>
              <div class="card mb-3">
                <div class="card-header bg-light">
                  <div class="row align-items-center">
                    <div class="col">
                      <h6 class="mb-0">
                        <i class="fas fa-table"></i> 
                        <strong><%= tableName || 'System Permissions' %></strong>
                      </h6>
                    </div>
                    <div class="col-auto">
                      <div class="form-check">
                        <input class="form-check-input table-select-all" 
                               type="checkbox" 
                               id="selectTable_<%= tableName %>"
                               data-table="<%= tableName %>">
                        <label class="form-check-label" for="selectTable_<%= tableName %>">
                          Select All
                        </label>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="card-body">
                  <div class="row">
                    <% permissionsByTable[tableName].forEach((permission, index) => { %>
                      <div class="col-md-6 col-lg-4 mb-2">
                        <div class="form-check">
                          <input class="form-check-input permission-checkbox" 
                                 type="checkbox" 
                                 name="permission_ids" 
                                 value="<%= permission.id %>"
                                 id="permission_<%= permission.id %>"
                                 data-table="<%= tableName %>"
                                 <%= permission.hasPermission ? 'checked' : '' %>>
                          <label class="form-check-label" for="permission_<%= permission.id %>">
                            <span class="badge badge-<%= getActionBadgeClass(permission.action) %> mr-1">
                              <%= permission.action %>
                            </span>
                            <%= permission.display_name %>
                          </label>
                          <% if (permission.description) { %>
                            <small class="form-text text-muted">
                              <%= permission.description %>
                            </small>
                          <% } %>
                        </div>
                      </div>
                    <% }) %>
                  </div>
                </div>
              </div>
            <% }) %>

            <div class="text-center mt-4">
              <button type="submit" class="btn btn-primary btn-lg">
                <i class="fas fa-save"></i> Save Permissions
              </button>
              <a href="/admin/crud/role" class="btn btn-secondary btn-lg ml-2">
                <i class="fas fa-times"></i> Cancel
              </a>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Summary Card -->
<div class="row mt-4">
  <div class="col-12">
    <div class="card">
      <div class="card-header">
        <h6 class="mb-0"><i class="fas fa-info-circle"></i> Permission Summary</h6>
      </div>
      <div class="card-body">
        <div class="row">
          <div class="col-md-3">
            <div class="text-center">
              <h4 class="text-primary" id="totalPermissions">0</h4>
              <small class="text-muted">Total Permissions</small>
            </div>
          </div>
          <div class="col-md-3">
            <div class="text-center">
              <h4 class="text-success" id="selectedPermissions">0</h4>
              <small class="text-muted">Selected</small>
            </div>
          </div>
          <div class="col-md-3">
            <div class="text-center">
              <h4 class="text-info" id="tableCount">0</h4>
              <small class="text-muted">Tables</small>
            </div>
          </div>
          <div class="col-md-3">
            <div class="text-center">
              <h4 class="text-warning" id="coveragePercentage">0%</h4>
              <small class="text-muted">Coverage</small>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
  const selectAllCheckbox = document.getElementById('selectAll');
  const tableSelectAllCheckboxes = document.querySelectorAll('.table-select-all');
  const permissionCheckboxes = document.querySelectorAll('.permission-checkbox');
  const form = document.getElementById('rolePermissionsForm');

  // Update summary on page load
  updateSummary();

  // Select All functionality
  selectAllCheckbox.addEventListener('change', function() {
    permissionCheckboxes.forEach(checkbox => {
      checkbox.checked = this.checked;
    });
    tableSelectAllCheckboxes.forEach(checkbox => {
      checkbox.checked = this.checked;
    });
    updateSummary();
  });

  // Table Select All functionality
  tableSelectAllCheckboxes.forEach(tableCheckbox => {
    tableCheckbox.addEventListener('change', function() {
      const tableName = this.dataset.table;
      const tablePermissions = document.querySelectorAll(`input[data-table="${tableName}"].permission-checkbox`);
      
      tablePermissions.forEach(checkbox => {
        checkbox.checked = this.checked;
      });
      
      updateSelectAllState();
      updateSummary();
    });
  });

  // Individual permission checkbox functionality
  permissionCheckboxes.forEach(checkbox => {
    checkbox.addEventListener('change', function() {
      updateTableSelectAllState(this.dataset.table);
      updateSelectAllState();
      updateSummary();
    });
  });

  // Form submission
  form.addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(form);
    const selectedPermissions = formData.getAll('permission_ids');
    
    fetch(`/admin/permissions/role/<%= role.id %>/permissions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Requested-With': 'XMLHttpRequest'
      },
      body: JSON.stringify({
        permission_ids: selectedPermissions
      })
    })
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        alert('Permissions updated successfully!');
        // Optionally redirect or reload
        // window.location.href = '/admin/crud/role';
      } else {
        alert('Error updating permissions: ' + data.message);
      }
    })
    .catch(error => {
      console.error('Error:', error);
      alert('Error updating permissions');
    });
  });

  // Update table select all state based on individual checkboxes
  function updateTableSelectAllState(tableName) {
    const tablePermissions = document.querySelectorAll(`input[data-table="${tableName}"].permission-checkbox`);
    const tableSelectAll = document.querySelector(`input[data-table="${tableName}"].table-select-all`);
    
    const checkedCount = Array.from(tablePermissions).filter(cb => cb.checked).length;
    
    if (checkedCount === 0) {
      tableSelectAll.indeterminate = false;
      tableSelectAll.checked = false;
    } else if (checkedCount === tablePermissions.length) {
      tableSelectAll.indeterminate = false;
      tableSelectAll.checked = true;
    } else {
      tableSelectAll.indeterminate = true;
      tableSelectAll.checked = false;
    }
  }

  // Update main select all state
  function updateSelectAllState() {
    const checkedCount = Array.from(permissionCheckboxes).filter(cb => cb.checked).length;
    
    if (checkedCount === 0) {
      selectAllCheckbox.indeterminate = false;
      selectAllCheckbox.checked = false;
    } else if (checkedCount === permissionCheckboxes.length) {
      selectAllCheckbox.indeterminate = false;
      selectAllCheckbox.checked = true;
    } else {
      selectAllCheckbox.indeterminate = true;
      selectAllCheckbox.checked = false;
    }
  }

  // Update summary statistics
  function updateSummary() {
    const totalPermissions = permissionCheckboxes.length;
    const selectedPermissions = Array.from(permissionCheckboxes).filter(cb => cb.checked).length;
    const tableCount = Object.keys(<%- JSON.stringify(permissionsByTable) %>).length;
    const coveragePercentage = totalPermissions > 0 ? Math.round((selectedPermissions / totalPermissions) * 100) : 0;

    document.getElementById('totalPermissions').textContent = totalPermissions;
    document.getElementById('selectedPermissions').textContent = selectedPermissions;
    document.getElementById('tableCount').textContent = tableCount;
    document.getElementById('coveragePercentage').textContent = coveragePercentage + '%';
  }

  // Initialize states on page load
  tableSelectAllCheckboxes.forEach(checkbox => {
    updateTableSelectAllState(checkbox.dataset.table);
  });
  updateSelectAllState();
});

// Helper function for action badge classes
function getActionBadgeClass(action) {
  const classes = {
    'browse': 'primary',
    'read': 'info',
    'edit': 'warning', 
    'add': 'success',
    'delete': 'danger'
  };
  return classes[action] || 'secondary';
}
</script>

<%
// Helper function for EJS template
function getActionBadgeClass(action) {
  const classes = {
    'browse': 'primary',
    'read': 'info',
    'edit': 'warning',
    'add': 'success', 
    'delete': 'danger'
  };
  return classes[action] || 'secondary';
}
%>
