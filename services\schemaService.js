const db = require('../config/database');

class SchemaService {

  // Get a connection from the pool for DDL operations
  async getConnection() {
    return db.get().promise();
  }

  // L<PERSON>y danh sách tất cả bảng trong database
  async getAllTables() {
    try {
      const pool = await this.getConnection();
      const [rows] = await pool.query('SHOW TABLES');
      const tableNames = rows.map(row => Object.values(row)[0]);
      
      // Lấy thông tin chi tiết cho từng bảng
      const tables = [];
      for (const tableName of tableNames) {
        const columns = await this.getTableColumns(tableName);
        tables.push({
          name: tableName,
          columns: columns
        });
      }
      
      return tables;
    } catch (error) {
      console.error('Error getting tables:', error);
      throw error;
    }
  }

  // Lấy cấu trúc của một bảng
  async getTableStructure(tableName) {
    try {
      const pool = await this.getConnection();
      const [columns] = await pool.query(`DESCRIBE ${tableName}`);
      const [indexes] = await pool.query(`SHOW INDEX FROM ${tableName}`);
      const [foreignKeys] = await pool.query(`
        SELECT 
          COLUMN_NAME,
          REFERENCED_TABLE_NAME,
          REFERENCED_COLUMN_NAME
        FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
        WHERE TABLE_SCHEMA = DATABASE() 
        AND TABLE_NAME = ? 
        AND REFERENCED_TABLE_NAME IS NOT NULL
      `, [tableName]);

      return {
        columns,
        indexes,
        foreignKeys
      };
    } catch (error) {
      console.error('Error getting table structure:', error);
      throw error;
    }
  }

  // Lấy thông tin columns của bảng
  async getTableColumns(tableName) {
    try {
      const pool = await this.getConnection();
      const [rows] = await pool.query(`DESCRIBE ${tableName}`);
      
      return rows.map(row => ({
        name: row.Field,
        type: row.Type,
        is_nullable: row.Null === 'YES',
        is_primary: row.Key === 'PRI',
        is_unique: row.Key === 'UNI',
        default_value: row.Default,
        is_auto_increment: row.Extra.includes('auto_increment')
      }));
    } catch (error) {
      console.error('Error getting table columns:', error);
      throw error;
    }
  }

  // Tạo bảng mới
  async createTable(tableName, columns) {
    try {
      const pool = await this.getConnection();

      // Validate input
      if (!tableName || !columns || columns.length === 0) {
        throw new Error('Table name and columns are required');
      }

      let sql = `CREATE TABLE ${tableName} (`;
      const columnDefinitions = [];

      for (const column of columns) {
        // Validate column data
        if (!column.name || !column.type) {
          console.error('Invalid column data:', column);
          throw new Error(`Column must have name and type. Got: ${JSON.stringify(column)}`);
        }

        let definition = `\`${column.name}\` ${this.getColumnType(column)}`;

        if (!column.is_nullable) {
          definition += ' NOT NULL';
        }

        if (column.is_auto_increment) {
          definition += ' AUTO_INCREMENT';
        }

        if (column.default_value) {
          definition += ` DEFAULT ${column.default_value}`;
        }

        columnDefinitions.push(definition);
      }

      sql += columnDefinitions.join(', ');

      // Thêm primary key
      const primaryColumns = columns.filter(col => col.is_primary);
      if (primaryColumns.length > 0) {
        sql += `, PRIMARY KEY (${primaryColumns.map(col => `\`${col.name}\``).join(', ')})`;
      }

      sql += ')';

      console.log('Creating table with SQL:', sql);
      await pool.query(sql);
      return true;
    } catch (error) {
      console.error('Error creating table:', error);
      console.error('Table name:', tableName);
      console.error('Columns:', JSON.stringify(columns, null, 2));
      throw error;
    }
  }

  // Thêm cột vào bảng
  async addColumn(tableName, column) {
    try {
      const pool = await this.getConnection();

      // Validate input
      if (!tableName || !column || !column.name || !column.type) {
        throw new Error(`Invalid input. Table: ${tableName}, Column: ${JSON.stringify(column)}`);
      }

      let sql = `ALTER TABLE \`${tableName}\` ADD COLUMN \`${column.name}\` ${this.getColumnType(column)}`;

      if (!column.is_nullable) {
        sql += ' NOT NULL';
      }

      if (column.default_value) {
        sql += ` DEFAULT ${column.default_value}`;
      }

      console.log('Adding column with SQL:', sql);
      await pool.query(sql);
      return true;
    } catch (error) {
      console.error('Error adding column:', error);
      console.error('Table name:', tableName);
      console.error('Column data:', JSON.stringify(column, null, 2));
      throw error;
    }
  }

  // Sửa cột
  async modifyColumn(tableName, column) {
    try {
      const pool = await this.getConnection();

      // Validate input
      if (!tableName || !column || !column.name || !column.type) {
        throw new Error(`Invalid input. Table: ${tableName}, Column: ${JSON.stringify(column)}`);
      }

      let sql = `ALTER TABLE \`${tableName}\` MODIFY COLUMN \`${column.name}\` ${this.getColumnType(column)}`;

      if (!column.is_nullable) {
        sql += ' NOT NULL';
      }

      if (column.default_value) {
        sql += ` DEFAULT ${column.default_value}`;
      }

      console.log('Modifying column with SQL:', sql);
      await pool.query(sql);
      return true;
    } catch (error) {
      console.error('Error modifying column:', error);
      console.error('Table name:', tableName);
      console.error('Column data:', JSON.stringify(column, null, 2));
      throw error;
    }
  }

  // Xóa cột
  async dropColumn(tableName, columnName) {
    try {
      const pool = await this.getConnection();
      await pool.query(`ALTER TABLE ${tableName} DROP COLUMN ${columnName}`);
      return true;
    } catch (error) {
      console.error('Error dropping column:', error);
      throw error;
    }
  }

  // Xóa bảng
  async dropTable(tableName) {
    try {
      const pool = await this.getConnection();
      await pool.query(`DROP TABLE ${tableName}`);
      return true;
    } catch (error) {
      console.error('Error dropping table:', error);
      throw error;
    }
  }

  // Thêm foreign key
  async addForeignKey(tableName, columnName, referencedTable, referencedColumn, constraintName) {
    try {
      const pool = await this.getConnection();
      const sql = `ALTER TABLE \`${tableName}\` ADD CONSTRAINT \`${constraintName}\` FOREIGN KEY (\`${columnName}\`) REFERENCES \`${referencedTable}\`(\`${referencedColumn}\`) ON DELETE CASCADE ON UPDATE CASCADE`;
      
      console.log('Adding foreign key with SQL:', sql);
      await pool.query(sql);
      return true;
    } catch (error) {
      console.error('Error adding foreign key:', error);
      throw error;
    }
  }

  // Xóa foreign key
  async dropForeignKey(tableName, constraintName) {
    try {
      const pool = await this.getConnection();
      await pool.query(`ALTER TABLE \`${tableName}\` DROP FOREIGN KEY \`${constraintName}\``);
      return true;
    } catch (error) {
      console.error('Error dropping foreign key:', error);
      throw error;
    }
  }

  // Chuyển đổi kiểu dữ liệu từ admin sang MySQL
  getColumnType(column) {
    let type = column.type.toUpperCase();
    
    // Thêm length cho các kiểu có thể có length
    if (column.length && ['VARCHAR', 'CHAR', 'INT', 'BIGINT', 'SMALLINT', 'TINYINT', 'DECIMAL', 'FLOAT', 'DOUBLE'].includes(type)) {
      type += `(${column.length})`;
    }
    
    // Xử lý các kiểu đặc biệt
    if (type === 'BOOLEAN') {
      type = 'TINYINT(1)';
    }
    
    if (type === 'TEXT' && column.length) {
      if (column.length <= 255) {
        type = 'TINYTEXT';
      } else if (column.length <= 65535) {
        type = 'TEXT';
      } else if (column.length <= 16777215) {
        type = 'MEDIUMTEXT';
      } else {
        type = 'LONGTEXT';
      }
    }
    
    return type;
  }

  // Không cần đóng kết nối vì chúng ta sử dụng connection pool
  async closeConnection() {
    // Connection will be automatically released back to the pool
    // No need to explicitly close it
  }
}

module.exports = new SchemaService();
