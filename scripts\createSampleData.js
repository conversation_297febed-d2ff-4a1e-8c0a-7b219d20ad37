const db = require('../config/database');

async function createSampleData() {
  try {
    console.log('Creating sample data...');
    
    // Create sample roles
    const roles = [
      { name: 'Admin' },
      { name: 'Manager' },
      { name: 'User' },
      { name: 'Guest' }
    ];
    
    for (const role of roles) {
      await db.query('INSERT INTO role (name) VALUES (?)', [role.name]);
      console.log(`Created role: ${role.name}`);
    }
    
    // Create sample users
    const users = [
      { fullname: '<PERSON>', email: '<EMAIL>', password: 'password123' },
      { fullname: '<PERSON>', email: '<EMAIL>', password: 'password123' },
      { fullname: '<PERSON>', email: '<EMAIL>', password: 'password123' }
    ];
    
    for (const user of users) {
      await db.query(
        'INSERT INTO user (fullname, email, password) VALUES (?, ?, ?)',
        [user.fullname, user.email, user.password]
      );
      console.log(`Created user: ${user.fullname}`);
    }
    
    // Assign roles to users
    const roleAssignments = [
      { user_id: 1, role_id: 1 }, // John -> Admin
      { user_id: 2, role_id: 2 }, // Jane -> Manager
      { user_id: 3, role_id: 3 }  // Bob -> User
    ];
    
    for (const assignment of roleAssignments) {
      await db.query(
        'INSERT INTO role_user (user_id, role_id) VALUES (?, ?)',
        [assignment.user_id, assignment.role_id]
      );
      console.log(`Assigned role ${assignment.role_id} to user ${assignment.user_id}`);
    }
    
    console.log('Sample data created successfully!');
    
  } catch (error) {
    console.error('Error creating sample data:', error);
  }
}

createSampleData(); 