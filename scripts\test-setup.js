const db = require('../config/database');

async function testSetup() {
  try {
    console.log('🧪 Testing database setup integration...\n');
    
    // Connect to database
    db.connect('development');
    console.log('✅ Connected to database');

    // Test if all required tables exist
    console.log('\n📋 Checking required tables...');
    
    const requiredTables = [
      'user', 'role', 'permissions', 'role_user', 'role_permissions',
      'user_sessions', 'user_session_settings',
      'admintable', 'admincolumn', 'adminrelation', 'admin_menus'
    ];

    const tables = await db.query('SHOW TABLES');
    const tableNames = tables.map(row => Object.values(row)[0]);
    
    let allTablesExist = true;
    for (const table of requiredTables) {
      if (tableNames.includes(table)) {
        console.log(`✅ Table '${table}' exists`);
      } else {
        console.log(`❌ Table '${table}' missing`);
        allTablesExist = false;
      }
    }

    if (allTablesExist) {
      console.log('\n✅ All required tables exist');
    } else {
      console.log('\n❌ Some tables are missing');
    }

    // Test data counts
    console.log('\n📊 Checking data counts...');
    
    const dataCounts = [
      { table: 'user', expected: 4 },
      { table: 'role', expected: 4 },
      { table: 'permissions', expected: 30 },
      { table: 'user_sessions', expected: 0 },
      { table: 'user_session_settings', expected: 4 },
      { table: 'admintable', expected: 9 }
    ];

    for (const check of dataCounts) {
      try {
        const result = await db.query(`SELECT COUNT(*) as count FROM ${check.table}`);
        const count = result[0].count;
        if (count >= check.expected) {
          console.log(`✅ ${check.table}: ${count} records (expected: ${check.expected})`);
        } else {
          console.log(`⚠️  ${check.table}: ${count} records (expected: ${check.expected})`);
        }
      } catch (error) {
        console.log(`❌ ${check.table}: Error - ${error.message}`);
      }
    }

    // Test device management functionality
    console.log('\n🔧 Testing device management...');
    
    try {
      const jwtService = require('../services/jwtService');
      const multiDeviceService = require('../services/multiDeviceService');
      
      const testUser = { id: 1, email: '<EMAIL>', fullname: 'Admin' };
      const { token, tokenId } = jwtService.createToken(testUser);
      console.log('✅ JWT token creation works');
      
      const deviceInfo = multiDeviceService.detectDeviceInfo('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');
      console.log(`✅ Device detection works: ${deviceInfo.deviceName}`);
      
      const sessionSettings = await multiDeviceService.getUserSessionSettings(1);
      if (sessionSettings) {
        console.log(`✅ Session settings work: max_sessions=${sessionSettings.max_sessions}`);
      } else {
        console.log('❌ Session settings not found');
      }
      
    } catch (error) {
      console.log(`❌ Device management error: ${error.message}`);
    }

    console.log('\n🎉 Database setup test completed!');
    
    if (allTablesExist) {
      console.log('\n✅ Your database is ready for production use!');
      console.log('🚀 You can start the application with: npm start');
    } else {
      console.log('\n⚠️  Some setup issues detected. Please run: npm run db:setup');
    }

  } catch (error) {
    console.error('💥 Error testing setup:', error);
  } finally {
    try {
      const pool = db.get();
      if (pool) {
        pool.end();
        console.log('🔌 Database connection closed');
      }
    } catch (error) {
      console.log('⚠️  Database connection already closed');
    }
  }
}

testSetup();
