const db = require('../config/database');

async function fixAllUserRoleReferences() {
  try {
    console.log('🔧 Fixing all user_role references to role_user...\n');
    
    await db.connect('development');

    // 1. Check current table structure
    console.log('1. Checking current table structure...');
    
    try {
      const roleUserExists = await db.query('SHOW TABLES LIKE "role_user"');
      const userRoleExists = await db.query('SHOW TABLES LIKE "user_role"');
      
      console.log(`✅ role_user table exists: ${roleUserExists.length > 0}`);
      console.log(`✅ user_role table exists: ${userRoleExists.length > 0}`);
      
      if (userRoleExists.length > 0 && roleUserExists.length > 0) {
        console.log('⚠️ Both tables exist - need to migrate data');
        
        // Check if role_user has data
        const roleUserCount = await db.queryOne('SELECT COUNT(*) as count FROM role_user');
        const userRoleCount = await db.queryOne('SELECT COUNT(*) as count FROM user_role');
        
        console.log(`   role_user records: ${roleUserCount.count}`);
        console.log(`   user_role records: ${userRoleCount.count}`);
        
        if (userRoleCount.count > 0 && roleUserCount.count === 0) {
          console.log('📋 Migrating data from user_role to role_user...');
          await db.query(`
            INSERT INTO role_user (user_id, role_id, assigned_at, assigned_by, is_active)
            SELECT user_id, role_id, assigned_at, assigned_by, is_active 
            FROM user_role
          `);
          console.log('✅ Data migrated successfully');
        }
        
        // Drop user_role table
        console.log('🗑️ Dropping user_role table...');
        await db.query('DROP TABLE user_role');
        console.log('✅ user_role table dropped');
      }
      
    } catch (error) {
      console.log('❌ Error checking tables:', error.message);
    }

    // 2. Update admin metadata to use role_user
    console.log('\n2. Updating admin metadata...');
    
    // Check if role_user exists in admintable
    const adminTable = await db.query('SELECT * FROM admintable WHERE name = "role_user"');
    if (adminTable.length === 0) {
      console.log('❌ role_user not found in admintable - need to add it');
      
      // Check if user_role exists in admintable
      const userRoleAdminTable = await db.query('SELECT * FROM admintable WHERE name = "user_role"');
      if (userRoleAdminTable.length > 0) {
        console.log('📋 Updating user_role to role_user in admintable...');
        await db.query('UPDATE admintable SET name = "role_user" WHERE name = "user_role"');
        console.log('✅ Updated admintable');
      }
    } else {
      console.log('✅ role_user already exists in admintable');
    }

    // 3. Update permissions
    console.log('\n3. Updating permissions...');
    
    const userRolePermissions = await db.query('SELECT * FROM permissions WHERE table_name = "user_role"');
    if (userRolePermissions.length > 0) {
      console.log(`📋 Updating ${userRolePermissions.length} user_role permissions to role_user...`);
      await db.query('UPDATE permissions SET table_name = "role_user" WHERE table_name = "user_role"');
      
      // Update permission names
      await db.query(`
        UPDATE permissions 
        SET name = REPLACE(name, 'user_role', 'role_user')
        WHERE table_name = 'role_user'
      `);
      console.log('✅ Updated permissions');
    } else {
      console.log('✅ No user_role permissions found to update');
    }

    // 4. Update menu items
    console.log('\n4. Updating menu items...');
    
    const userRoleMenus = await db.query('SELECT * FROM admin_menus WHERE url LIKE "%user_role%"');
    if (userRoleMenus.length > 0) {
      console.log(`📋 Updating ${userRoleMenus.length} menu items...`);
      await db.query(`
        UPDATE admin_menus 
        SET url = REPLACE(url, 'user_role', 'role_user')
        WHERE url LIKE '%user_role%'
      `);
      console.log('✅ Updated menu items');
    } else {
      console.log('✅ No user_role menu items found to update');
    }

    // 5. Verify final state
    console.log('\n5. Verifying final state...');
    
    const finalRoleUserCount = await db.queryOne('SELECT COUNT(*) as count FROM role_user');
    console.log(`✅ role_user table has ${finalRoleUserCount.count} records`);
    
    const finalPermissions = await db.query('SELECT name FROM permissions WHERE table_name = "role_user"');
    console.log(`✅ role_user has ${finalPermissions.length} permissions:`);
    finalPermissions.forEach(perm => {
      console.log(`   - ${perm.name}`);
    });
    
    const finalMenus = await db.query('SELECT title, url FROM admin_menus WHERE url LIKE "%role_user%"');
    console.log(`✅ Found ${finalMenus.length} role_user menu items:`);
    finalMenus.forEach(menu => {
      console.log(`   - ${menu.title}: ${menu.url}`);
    });

    console.log('\n🎉 All user_role references have been fixed to role_user!');
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    process.exit(0);
  }
}

fixAllUserRoleReferences();
