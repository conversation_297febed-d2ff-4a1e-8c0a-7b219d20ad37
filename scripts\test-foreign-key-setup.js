const mysql = require('mysql2/promise');
const db = require('../config/database');

async function setupTestData() {
  try {
    console.log('Setting up test data for foreign key dropdown feature...');

    // Create MySQL connection
    const connection = await mysql.createConnection({
      host: process.env.DATABASE_HOST || 'localhost',
      user: process.env.DATABASE_USER || 'root',
      password: process.env.DATABASE_PASSWORD || '',
      database: process.env.DATABASE_URL || 'backend_coreui'
    });

    // 1. Create role table
    console.log('Creating role table...');
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS role (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        description TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      )
    `);

    // 2. Insert sample roles
    console.log('Inserting sample roles...');
    await connection.execute(`
      INSERT IGNORE INTO role (id, name, description) VALUES 
      (1, 'Admin', 'Administrator role with full access'),
      (2, 'Manager', 'Manager role with limited admin access'),
      (3, 'User', 'Regular user role'),
      (4, 'Guest', 'Guest role with read-only access')
    `);

    // 3. Create role_user table
    console.log('Creating role_user table...');
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS role_user (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        role_id INT NOT NULL,
        assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        assigned_by INT NULL,
        is_active BOOLEAN DEFAULT TRUE
      )
    `);

    await connection.end();

    // 4. Create admin tables in database
    console.log('Creating admin table entries...');
    
    // Create admin table for role
    await db.query(`
      INSERT IGNORE INTO admintable (name, display_name, description, icon, model_name, is_active, order_index)
      VALUES (?, ?, ?, ?, ?, ?, ?)
    `, ['role', 'Roles', 'User roles management', 'fas fa-user-tag', 'Role', true, 1]);

    const roleTable = await db.queryOne('SELECT * FROM admintable WHERE name = ?', ['role']);
    
    if (roleTable) {
      // Create columns for role table
      const roleColumns = [
        { name: 'id', display_name: 'ID', type: 'int', is_primary: true, is_visible_form: false },
        { name: 'name', display_name: 'Name', type: 'varchar', length: 255, is_searchable: true },
        { name: 'description', display_name: 'Description', type: 'text' },
        { name: 'created_at', display_name: 'Created At', type: 'datetime', is_visible_form: false }
      ];

      for (let i = 0; i < roleColumns.length; i++) {
        const col = roleColumns[i];
        await db.query(`
          INSERT IGNORE INTO admincolumn (
            table_id, name, display_name, type, length, is_primary, is_visible_form, 
            is_searchable, order_index
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        `, [
          roleTable.id, col.name, col.display_name, col.type, col.length || null,
          col.is_primary || false, col.is_visible_form !== false, col.is_searchable || false, i
        ]);
      }
    }

    // Create admin table for role_user
    await db.query(`
      INSERT IGNORE INTO admintable (name, display_name, description, icon, model_name, is_active, order_index)
      VALUES (?, ?, ?, ?, ?, ?, ?)
    `, ['role_user', 'User Roles', 'User role assignments', 'fas fa-link', 'RoleUser', true, 2]);

    const roleUserTable = await db.queryOne('SELECT * FROM admintable WHERE name = ?', ['role_user']);
    
    if (roleUserTable) {
      // Create columns for role_user table
      const roleUserColumns = [
        { name: 'id', display_name: 'ID', type: 'int', is_primary: true, is_visible_form: false },
        { name: 'user_id', display_name: 'User', type: 'int', is_searchable: true },
        { name: 'role_id', display_name: 'Role', type: 'int', is_searchable: true },
        { name: 'assigned_at', display_name: 'Assigned At', type: 'datetime', is_visible_form: false }
      ];

      for (let i = 0; i < roleUserColumns.length; i++) {
        const col = roleUserColumns[i];
        await db.query(`
          INSERT IGNORE INTO admincolumn (
            table_id, name, display_name, type, is_primary, is_visible_form, 
            is_searchable, order_index
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        `, [
          roleUserTable.id, col.name, col.display_name, col.type,
          col.is_primary || false, col.is_visible_form !== false, col.is_searchable || false, i
        ]);
      }

      // Create relations for role_user table
      const userColumn = await db.queryOne('SELECT * FROM admincolumn WHERE table_id = ? AND name = ?', [roleUserTable.id, 'user_id']);
      const roleColumn = await db.queryOne('SELECT * FROM admincolumn WHERE table_id = ? AND name = ?', [roleUserTable.id, 'role_id']);
      const userTable = await db.queryOne('SELECT * FROM admintable WHERE name = ?', ['user']);
      const roleTableForRel = await db.queryOne('SELECT * FROM admintable WHERE name = ?', ['role']);

      if (userColumn && userTable) {
        await db.query(`
          INSERT IGNORE INTO adminrelation (
            table_id, column_id, foreign_table_id, foreign_column, display_column, relation_type
          ) VALUES (?, ?, ?, ?, ?, ?)
        `, [roleUserTable.id, userColumn.id, userTable.id, 'id', 'fullname', 'belongsTo']);
      }

      if (roleColumn && roleTableForRel) {
        await db.query(`
          INSERT IGNORE INTO adminrelation (
            table_id, column_id, foreign_table_id, foreign_column, display_column, relation_type
          ) VALUES (?, ?, ?, ?, ?, ?)
        `, [roleUserTable.id, roleColumn.id, roleTableForRel.id, 'id', 'name', 'belongsTo']);
      }
    }

    console.log('Test data setup completed successfully!');
    console.log('\nTest data created:');
    console.log('- Role table with 4 sample roles');
    console.log('- Role_user table for assignments');
    console.log('- Admin table configurations');
    console.log('- Foreign key relations');

  } catch (error) {
    console.error('Error setting up test data:', error);
  }
}

setupTestData();
