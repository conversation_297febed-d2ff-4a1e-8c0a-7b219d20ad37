// Validation utilities for admin system

class ValidationError extends Error {
  constructor(message, field = null) {
    super(message);
    this.name = 'ValidationError';
    this.field = field;
    this.errors = [];
  }

  // Add error to the errors array
  addError(field, message) {
    this.errors.push({ field, message });
  }

  // Check if has any errors
  hasErrors() {
    return this.errors.length > 0;
  }
}

class Validator {
  // Validate table name
  static validateTableName(name) {
    if (!name || typeof name !== 'string') {
      throw new ValidationError('Table name is required', 'name');
    }

    const trimmedName = name.trim();
    
    if (trimmedName.length === 0) {
      throw new ValidationError('Table name cannot be empty', 'name');
    }

    if (trimmedName.length > 64) {
      throw new ValidationError('Table name cannot exceed 64 characters', 'name');
    }

    // Chỉ cho phép chữ cái, số, gạch dưới, bắt đầu bằng chữ cái hoặc gạch dưới
    if (!/^[a-zA-Z_][a-zA-Z0-9_]*$/.test(trimmedName)) {
      throw new ValidationError('Table name can only contain letters, numbers, and underscores. Must start with a letter or underscore', 'name');
    }

    // Kiểm tra từ khóa MySQL reserved
    const reservedWords = [
      'SELECT', 'INSERT', 'UPDATE', 'DELETE', 'CREATE', 'DROP', 'ALTER', 'INDEX',
      'TABLE', 'DATABASE', 'SCHEMA', 'VIEW', 'TRIGGER', 'PROCEDURE', 'FUNCTION',
      'USER', 'ROLE', 'GRANT', 'REVOKE', 'ORDER', 'GROUP', 'HAVING', 'WHERE',
      'FROM', 'JOIN', 'INNER', 'LEFT', 'RIGHT', 'OUTER', 'UNION', 'DISTINCT',
      'COUNT', 'SUM', 'AVG', 'MIN', 'MAX', 'AND', 'OR', 'NOT', 'NULL', 'TRUE', 'FALSE'
    ];

    if (reservedWords.includes(trimmedName.toUpperCase())) {
      throw new ValidationError(`"${trimmedName}" is a reserved MySQL keyword and cannot be used as table name`, 'name');
    }

    return trimmedName;
  }

  // Validate column name
  static validateColumnName(name) {
    if (!name || typeof name !== 'string') {
      throw new ValidationError('Column name is required', 'name');
    }

    const trimmedName = name.trim();
    
    if (trimmedName.length === 0) {
      throw new ValidationError('Column name cannot be empty', 'name');
    }

    if (trimmedName.length > 64) {
      throw new ValidationError('Column name cannot exceed 64 characters', 'name');
    }

    // Chỉ cho phép chữ cái, số, gạch dưới, bắt đầu bằng chữ cái hoặc gạch dưới
    if (!/^[a-zA-Z_][a-zA-Z0-9_]*$/.test(trimmedName)) {
      throw new ValidationError('Column name can only contain letters, numbers, and underscores. Must start with a letter or underscore', 'name');
    }

    // Kiểm tra từ khóa MySQL reserved
    const reservedWords = [
      'SELECT', 'INSERT', 'UPDATE', 'DELETE', 'CREATE', 'DROP', 'ALTER', 'INDEX',
      'TABLE', 'DATABASE', 'SCHEMA', 'VIEW', 'TRIGGER', 'PROCEDURE', 'FUNCTION',
      'USER', 'ROLE', 'GRANT', 'REVOKE', 'ORDER', 'GROUP', 'HAVING', 'WHERE',
      'FROM', 'JOIN', 'INNER', 'LEFT', 'RIGHT', 'OUTER', 'UNION', 'DISTINCT',
      'COUNT', 'SUM', 'AVG', 'MIN', 'MAX', 'AND', 'OR', 'NOT', 'NULL', 'TRUE', 'FALSE',
      'KEY', 'PRIMARY', 'FOREIGN', 'UNIQUE', 'AUTO_INCREMENT', 'DEFAULT'
    ];

    if (reservedWords.includes(trimmedName.toUpperCase())) {
      throw new ValidationError(`"${trimmedName}" is a reserved MySQL keyword and cannot be used as column name`, 'name');
    }

    return trimmedName;
  }

  // Validate display name
  static validateDisplayName(name) {
    if (!name || typeof name !== 'string') {
      throw new ValidationError('Display name is required', 'display_name');
    }

    const trimmedName = name.trim();
    
    if (trimmedName.length === 0) {
      throw new ValidationError('Display name cannot be empty', 'display_name');
    }

    if (trimmedName.length > 255) {
      throw new ValidationError('Display name cannot exceed 255 characters', 'display_name');
    }

    return trimmedName;
  }

  // Validate column type
  static validateColumnType(type) {
    if (!type || typeof type !== 'string') {
      throw new ValidationError('Column type is required', 'type');
    }

    const validTypes = [
      'int', 'bigint', 'smallint', 'tinyint', 'mediumint',
      'varchar', 'char', 'text', 'mediumtext', 'longtext',
      'decimal', 'float', 'double',
      'date', 'datetime', 'timestamp', 'time', 'year',
      'boolean', 'tinyint(1)',
      'json', 'blob', 'mediumblob', 'longblob'
    ];

    if (!validTypes.includes(type.toLowerCase())) {
      throw new ValidationError(`Invalid column type: ${type}. Allowed types: ${validTypes.join(', ')}`, 'type');
    }

    return type.toLowerCase();
  }

  // Validate column length
  static validateColumnLength(type, length) {
    if (!length) return null;

    const numericTypes = ['int', 'bigint', 'smallint', 'tinyint', 'mediumint'];
    const stringTypes = ['varchar', 'char'];
    const decimalTypes = ['decimal'];

    if (numericTypes.includes(type)) {
      const num = parseInt(length);
      if (isNaN(num) || num < 1 || num > 255) {
        throw new ValidationError('Numeric type length must be between 1 and 255', 'length');
      }
      return num;
    }

    if (stringTypes.includes(type)) {
      const num = parseInt(length);
      if (isNaN(num) || num < 1 || num > 65535) {
        throw new ValidationError('String type length must be between 1 and 65535', 'length');
      }
      return num;
    }

    if (decimalTypes.includes(type)) {
      // Format: "10,2" for decimal(10,2)
      if (!/^\d+,\d+$/.test(length)) {
        throw new ValidationError('Decimal length must be in format "precision,scale" (e.g., "10,2")', 'length');
      }
      const [precision, scale] = length.split(',').map(n => parseInt(n));
      if (precision < 1 || precision > 65 || scale < 0 || scale > 30 || scale > precision) {
        throw new ValidationError('Invalid decimal precision/scale. Precision: 1-65, Scale: 0-30, Scale <= Precision', 'length');
      }
      return length;
    }

    return null; // Other types don't need length
  }

  // Validate default value
  static validateDefaultValue(type, value, isNullable) {
    if (!value || value === '') return null;

    // NULL is always valid if column is nullable
    if (value.toLowerCase() === 'null') {
      if (!isNullable) {
        throw new ValidationError('Cannot set NULL as default for NOT NULL column', 'default_value');
      }
      return null;
    }

    const numericTypes = ['int', 'bigint', 'smallint', 'tinyint', 'mediumint', 'decimal', 'float', 'double'];
    const stringTypes = ['varchar', 'char', 'text', 'mediumtext', 'longtext'];
    const dateTypes = ['date', 'datetime', 'timestamp'];

    if (numericTypes.includes(type)) {
      if (type === 'decimal' || type === 'float' || type === 'double') {
        if (isNaN(parseFloat(value))) {
          throw new ValidationError('Default value must be a valid number for numeric column', 'default_value');
        }
      } else {
        if (isNaN(parseInt(value))) {
          throw new ValidationError('Default value must be a valid integer for integer column', 'default_value');
        }
      }
    }

    if (stringTypes.includes(type)) {
      // String values are generally valid, but check for SQL injection patterns
      if (/['";\\]/.test(value)) {
        throw new ValidationError('Default value contains invalid characters', 'default_value');
      }
    }

    if (dateTypes.includes(type)) {
      const specialValues = ['CURRENT_TIMESTAMP', 'NOW()', 'CURRENT_DATE', 'CURRENT_TIME'];
      if (!specialValues.includes(value.toUpperCase())) {
        // Try to parse as date
        const date = new Date(value);
        if (isNaN(date.getTime())) {
          throw new ValidationError('Default value must be a valid date or special function (CURRENT_TIMESTAMP, NOW(), etc.)', 'default_value');
        }
      }
    }

    if (type === 'boolean' || type === 'tinyint(1)') {
      const validBooleans = ['0', '1', 'true', 'false', 'TRUE', 'FALSE'];
      if (!validBooleans.includes(value)) {
        throw new ValidationError('Default value for boolean must be 0, 1, true, or false', 'default_value');
      }
    }

    return value;
  }

  // Validate table data
  static validateTableData(data) {
    const errors = [];

    try {
      data.name = this.validateTableName(data.name);
    } catch (error) {
      errors.push(error);
    }

    try {
      data.display_name = this.validateDisplayName(data.display_name);
    } catch (error) {
      errors.push(error);
    }

    // Validate description (optional)
    if (data.description && typeof data.description === 'string' && data.description.length > 1000) {
      errors.push(new ValidationError('Description cannot exceed 1000 characters', 'description'));
    }

    // Validate model_name (optional)
    if (data.model_name) {
      if (typeof data.model_name !== 'string' || !/^[a-zA-Z][a-zA-Z0-9]*$/.test(data.model_name)) {
        errors.push(new ValidationError('Model name must start with a letter and contain only letters and numbers', 'model_name'));
      }
    }

    // Validate columns
    if (!data.columns || !Array.isArray(data.columns) || data.columns.length === 0) {
      errors.push(new ValidationError('At least one column is required', 'columns'));
    } else {
      data.columns.forEach((column, index) => {
        try {
          this.validateColumnData(column, index);
        } catch (error) {
          errors.push(error);
        }
      });

      // Check for duplicate column names
      const columnNames = data.columns.map(col => col.name?.toLowerCase()).filter(Boolean);
      const duplicates = columnNames.filter((name, index) => columnNames.indexOf(name) !== index);
      if (duplicates.length > 0) {
        errors.push(new ValidationError(`Duplicate column names: ${duplicates.join(', ')}`, 'columns'));
      }

      // Check for primary key
      const primaryKeys = data.columns.filter(col => col.is_primary);
      if (primaryKeys.length === 0) {
        errors.push(new ValidationError('At least one column must be marked as primary key', 'columns'));
      }
    }

    if (errors.length > 0) {
      const error = new ValidationError('Validation failed');
      error.errors = errors;
      throw error;
    }

    return data;
  }

  // Validate column data
  static validateColumnData(column, index = 0) {
    const errors = [];

    try {
      column.name = this.validateColumnName(column.name);
    } catch (error) {
      error.field = `columns[${index}].name`;
      errors.push(error);
    }

    try {
      column.display_name = this.validateDisplayName(column.display_name || column.name);
    } catch (error) {
      error.field = `columns[${index}].display_name`;
      errors.push(error);
    }

    try {
      column.type = this.validateColumnType(column.type);
    } catch (error) {
      error.field = `columns[${index}].type`;
      errors.push(error);
    }

    try {
      column.length = this.validateColumnLength(column.type, column.length);
    } catch (error) {
      error.field = `columns[${index}].length`;
      errors.push(error);
    }

    try {
      column.default_value = this.validateDefaultValue(column.type, column.default_value, column.is_nullable);
    } catch (error) {
      error.field = `columns[${index}].default_value`;
      errors.push(error);
    }

    // Validate boolean fields
    column.is_nullable = Boolean(column.is_nullable);
    column.is_primary = Boolean(column.is_primary);
    column.is_unique = Boolean(column.is_unique);
    column.is_auto_increment = Boolean(column.is_auto_increment);

    // Auto increment can only be on integer primary keys
    if (column.is_auto_increment) {
      const integerTypes = ['int', 'bigint', 'smallint', 'tinyint', 'mediumint'];
      if (!integerTypes.includes(column.type)) {
        errors.push(new ValidationError('Auto increment can only be used with integer types', `columns[${index}].is_auto_increment`));
      }
      if (!column.is_primary) {
        errors.push(new ValidationError('Auto increment column must be primary key', `columns[${index}].is_auto_increment`));
      }
      if (column.is_nullable) {
        errors.push(new ValidationError('Auto increment column cannot be nullable', `columns[${index}].is_nullable`));
      }
    }

    // Primary key cannot be nullable
    if (column.is_primary && column.is_nullable) {
      errors.push(new ValidationError('Primary key column cannot be nullable', `columns[${index}].is_nullable`));
    }

    if (errors.length > 0) {
      const error = new ValidationError(`Column validation failed: ${column.name || `column ${index + 1}`}`);
      error.errors = errors;
      throw error;
    }

    return column;
  }

  // Validate record data for dynamic CRUD
  static validateRecordData(data, columns) {
    const errors = [];
    const validatedData = {};

    columns.forEach(column => {
      const value = data[column.name];
      
      // Check required fields
      if (!column.is_nullable && !column.is_auto_increment && (value === null || value === undefined || value === '')) {
        errors.push(new ValidationError(`${column.display_name} is required`, column.name));
        return;
      }

      // Skip auto increment fields
      if (column.is_auto_increment) {
        return;
      }

      // Skip if value is empty and column is nullable
      if ((value === null || value === undefined || value === '') && column.is_nullable) {
        validatedData[column.name] = null;
        return;
      }

      try {
        validatedData[column.name] = this.validateFieldValue(value, column);
      } catch (error) {
        error.field = column.name;
        errors.push(error);
      }
    });

    if (errors.length > 0) {
      const error = new ValidationError('Record validation failed');
      error.errors = errors;
      throw error;
    }

    return validatedData;
  }

  // Validate individual field value
  static validateFieldValue(value, column) {
    if (value === null || value === undefined) return null;

    const { type, length } = column;
    const stringValue = String(value).trim();

    const numericTypes = ['int', 'bigint', 'smallint', 'tinyint', 'mediumint'];
    const floatTypes = ['decimal', 'float', 'double'];
    const stringTypes = ['varchar', 'char', 'text', 'mediumtext', 'longtext'];
    const dateTypes = ['date', 'datetime', 'timestamp'];

    if (numericTypes.includes(type)) {
      const num = parseInt(stringValue);
      if (isNaN(num)) {
        throw new ValidationError(`${column.display_name} must be a valid integer`);
      }
      return num;
    }

    if (floatTypes.includes(type)) {
      const num = parseFloat(stringValue);
      if (isNaN(num)) {
        throw new ValidationError(`${column.display_name} must be a valid number`);
      }
      return num;
    }

    if (stringTypes.includes(type)) {
      if (length && stringValue.length > length) {
        throw new ValidationError(`${column.display_name} cannot exceed ${length} characters`);
      }
      return stringValue;
    }

    if (dateTypes.includes(type)) {
      const date = new Date(stringValue);
      if (isNaN(date.getTime())) {
        throw new ValidationError(`${column.display_name} must be a valid date`);
      }
      return stringValue;
    }

    if (type === 'boolean' || type === 'tinyint(1)') {
      if (['true', '1', 'yes', 'on'].includes(stringValue.toLowerCase())) return 1;
      if (['false', '0', 'no', 'off'].includes(stringValue.toLowerCase())) return 0;
      throw new ValidationError(`${column.display_name} must be true or false`);
    }

    return stringValue;
  }

  // Helper function to format validation errors for API response
  static formatValidationErrors(errors) {
    if (!Array.isArray(errors)) {
      return [{ field: null, message: String(errors) }];
    }

    return errors.map(error => {
      if (typeof error === 'string') {
        return { field: null, message: error };
      }
      if (error instanceof ValidationError) {
        return { field: error.field, message: error.message };
      }
      return { field: error.field || null, message: error.message || String(error) };
    });
  }
}

module.exports = { Validator, ValidationError };
