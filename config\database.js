const dotenv = require('dotenv');
const mysql = require('mysql2');

// Load environment variables
dotenv.config();

const {
    DB_USER,
    DB_PASSWORD,
    DB_NAME,
    DB_HOST = 'localhost',
    DATABASE_CONNECTION_LIMIT = 10,
} = process.env;

// Validate required environment variables
if (!DB_USER || !DB_NAME) {
    throw new Error(
        'Missing database configuration in .env file: DB_USER, DB_PASSWORD, and DB_NAME are required.'
    );
}

const state = {
    pool: null,
    mode: null,
};

const db = {
    /**
     * Connects to the MySQL database using a connection pool.
     * @param {string} mode - The mode of operation (e.g., 'production', 'development').
     */
    connect(mode) {
        if (state.pool) {
            console.log('✅ Database connection pool already exists.');
            return;
        }

        try {
            state.pool = mysql.createPool({
                host: DB_HOST,
                user: DB_USER,
                password: DB_PASSWORD,
                database: DB_NAME,
                waitForConnections: true,
                connectionLimit: parseInt(DATABASE_CONNECTION_LIMIT, 10) || 10,
                queueLimit: 0,
            });

            // Test connection
            state.pool.getConnection((err, connection) => {
                if (err) {
                    console.error('❌ Error getting connection from pool:', err.message);
                } else {
                    console.log('✅ Database connection pool created successfully.');
                    connection.release();
                }
            });

            state.mode = mode;
        } catch (err) {
            console.error('❌ Error creating database connection pool:', err);
            throw new Error(`Failed to connect to the database: ${err.message}`);
        }
    },

    /**
     * Retrieves the database connection pool.
     * @returns {mysql.Pool} - The MySQL connection pool.
     */
    get() {
        if (!state.pool) {
            throw new Error('❌ Database connection pool has not been created. Call connect() first.');
        }
        return state.pool;
    },

    /**
     * Executes a query with promise support
     * @param {string} sql - SQL query
     * @param {Array} params - Query parameters
     * @returns {Promise} - Promise with query results
     */
    async query(sql, params = []) {
        const pool = this.get();
        return new Promise((resolve, reject) => {
            pool.query(sql, params, (error, results) => {
                if (error) {
                    reject(error);
                } else {
                    resolve(results);
                }
            });
        });
    },

    /**
     * Executes a query and returns the first result
     * @param {string} sql - SQL query
     * @param {Array} params - Query parameters
     * @returns {Promise} - Promise with first result
     */
    async queryOne(sql, params = []) {
        const results = await this.query(sql, params);
        return results[0] || null;
    },

    /**
     * Begins a transaction
     * @returns {Promise} - Promise with connection
     */
    async beginTransaction() {
        const pool = this.get();
        return new Promise((resolve, reject) => {
            pool.getConnection((err, connection) => {
                if (err) {
                    reject(err);
                } else {
                    connection.beginTransaction((err) => {
                        if (err) {
                            connection.release();
                            reject(err);
                        } else {
                            resolve(connection);
                        }
                    });
                }
            });
        });
    },

    /**
     * Commits a transaction
     * @param {Object} connection - Database connection
     * @returns {Promise} - Promise
     */
    async commitTransaction(connection) {
        return new Promise((resolve, reject) => {
            connection.commit((err) => {
                if (err) {
                    reject(err);
                } else {
                    connection.release();
                    resolve();
                }
            });
        });
    },

    /**
     * Rollbacks a transaction
     * @param {Object} connection - Database connection
     * @returns {Promise} - Promise
     */
    async rollbackTransaction(connection) {
        return new Promise((resolve, reject) => {
            connection.rollback((err) => {
                if (err) {
                    reject(err);
                } else {
                    connection.release();
                    resolve();
                }
            });
        });
    }
};

module.exports = db; 