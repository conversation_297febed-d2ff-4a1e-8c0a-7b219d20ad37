# Hệ thống Quyền Đơn giản

## Tổng quan

Hệ thống quyền đã được đơn giản hóa từ 5 quyền xuống còn **4 quyền cơ bản** cho mỗi bảng:

- ✅ **Read** - Xem dữ liệu
- ✅ **Edit** - Chỉnh sửa dữ liệu  
- ✅ **Add** - Thêm dữ liệu mới
- ✅ **Delete** - Xóa dữ liệu
- ❌ ~~Browse~~ - Đã loại bỏ

## Cấu trúc Role

### 🔑 Admin Role
- **Quyền**: Tất cả 60 permissions
- **Capabilities**:
  - Quản lý toàn bộ hệ thống
  - Read, Edit, Add, Delete cho tất cả bảng
  - Quản lý Tables, Menus, Roles, Permissions

### 👤 User Role  
- **Quyền**: 2 permissions cơ bản
- **Capabilities**:
  - `read_user` - Xem thông tin user
  - `edit_user` - Chỉnh sửa profile cá nhân

### 🎯 Custom Roles
- <PERSON><PERSON> thể tạo với các permissions cụ thể
- Flexible theo business requirements

## Permission Structure

### Format Permission
```
{action}_{table_name}
```

### Ví dụ cho bảng `user`:
- `read_user` - Xem user
- `edit_user` - Chỉnh sửa user
- `add_user` - Tạo user mới
- `delete_user` - Xóa user

### System Permissions (Admin Only)
```
# Table Management
read_tables, edit_tables, add_tables, delete_tables

# Menu Management  
read_menus, edit_menus, add_menus, delete_menus

# Role Management
read_roles, edit_roles, add_roles, delete_roles

# Permission Management
read_permissions, edit_permissions, add_permissions, delete_permissions
```

## Database Schema

### Tables
```sql
-- Permissions table
permissions (
  id, name, display_name, description,
  table_name, action, created_at, updated_at
)

-- Role permissions mapping
role_permissions (
  id, role_id, permission_id, granted_at, granted_by
)

-- User role mapping
user_role (
  id, user_id, role_id, assigned_at, assigned_by
)
```

### Current Stats
- **Total permissions**: 60
- **Browse permissions**: 0 (removed)
- **Admin permissions**: 60 (all)
- **User permissions**: 2 (basic)

## Middleware Usage

### Basic Permission Check
```javascript
const { checkPermission } = require('../middleware/permissionMiddleware');

// Check specific action on table
router.get('/users', checkPermission('read', 'user'), controller.getUsers);
router.post('/users', checkPermission('add', 'user'), controller.createUser);
router.put('/users/:id', checkPermission('edit', 'user'), controller.updateUser);
router.delete('/users/:id', checkPermission('delete', 'user'), controller.deleteUser);
```

### Admin Only
```javascript
const { requireAdmin } = require('../middleware/permissionMiddleware');

router.post('/admin-only', requireAdmin(), controller.adminFunction);
```

### Multiple Permissions (OR logic)
```javascript
const { checkAnyPermission } = require('../middleware/permissionMiddleware');

router.get('/data', checkAnyPermission([
  'read_user',
  'read_orders'
]), controller.getData);
```

### CRUD Permissions Helper
```javascript
const { createCrudPermissions } = require('../middleware/permissionMiddleware');

const userPermissions = createCrudPermissions('user');
// Returns: { read, edit, add, delete }

router.get('/users', userPermissions.read, controller.getUsers);
router.post('/users', userPermissions.add, controller.createUser);
```

## Service Usage

### Check Permission
```javascript
const permissionService = require('../services/permissionService');

// Check if user is admin
const isAdmin = await permissionService.isAdmin(userId);

// Check specific permission
const hasPermission = await permissionService.checkUserPermission(
  userId, 'read_user'
);

// Check by action + table
const canEdit = await permissionService.checkUserPermission(
  userId, null, 'user', 'edit'
);

// Get all user permissions
const permissions = await permissionService.getUserPermissions(userId);
```

### Manage Permissions
```javascript
// Grant permission to role
await permissionService.grantPermissionToRole(roleId, permissionId);

// Revoke permission from role
await permissionService.revokePermissionFromRole(roleId, permissionId);

// Create permissions for new table
await permissionService.createTablePermissions('tableName', 'Display Name');
```

## Route Examples

### User Routes
```javascript
const { checkPermission } = require('../middleware/permissionMiddleware');

// User management routes
router.get('/', checkPermission('read', 'user'), userController.index);
router.get('/:id', checkPermission('read', 'user'), userController.show);  
router.post('/', checkPermission('add', 'user'), userController.create);
router.put('/:id', checkPermission('edit', 'user'), userController.update);
router.delete('/:id', checkPermission('delete', 'user'), userController.delete);
```

### Admin Routes
```javascript
const { requireAdmin } = require('../middleware/permissionMiddleware');

// Admin only routes
router.get('/tables', requireAdmin(), adminController.getTables);
router.post('/roles', requireAdmin(), roleController.create);
```

## Permission Flow

### 1. Request Flow
```
Request → Auth Middleware → Permission Middleware → Controller
```

### 2. Permission Check Logic
```
1. Check if user is logged in
2. Check if user is Admin (bypass if true)
3. Check specific permission
4. Allow/Deny request
```

### 3. Admin Bypass
Admin role bypasses all permission checks và có full access.

## Scripts

### Setup Commands
```bash
# Clean up and setup simple permissions
node scripts/cleanup-simple-permissions.js

# Setup basic roles
node scripts/setup-simple-roles.js

# Test permission system
node scripts/test-permission-system.js
```

### Generate Permissions
```javascript
// Auto-generate permissions for new table
const permissionService = require('./services/permissionService');
await permissionService.createTablePermissions('tableName', 'Display Name');
```

## Benefits

### ✅ Advantages
1. **Simplicity**: Chỉ 4 quyền cơ bản, dễ hiểu
2. **Performance**: Ít permissions hơn, nhanh hơn
3. **Maintainability**: Code sạch, ít complexity
4. **Flexibility**: Dễ mở rộng với custom roles
5. **Security**: Clear permission boundaries

### 🔄 Migration from Old System
- ✅ Removed all `browse` permissions
- ✅ Updated middleware and services
- ✅ Migrated user roles
- ✅ Updated documentation

## Custom Role Examples

### Content Editor Role
```javascript
// Grant specific permissions for content management
const permissions = [
  'read_posts', 'edit_posts', 'add_posts',
  'read_categories', 'edit_categories'
];
```

### Manager Role
```javascript
// Grant management permissions
const permissions = [
  'read_user', 'edit_user', 'add_user',
  'read_orders', 'edit_orders',
  'read_reports'
];
```

## Error Handling

### Permission Denied
```json
{
  "success": false,
  "message": "Bạn không có quyền xem user",
  "required_permission": "read_user"
}
```

### Multiple Permissions
```json
{
  "success": false,
  "message": "Bạn không có quyền thực hiện hành động này",
  "required_permissions": ["read_user", "read_orders"]
}
```

## Best Practices

### 1. Route Protection
- Luôn protect routes với appropriate permissions
- Sử dụng `requireAdmin()` cho admin-only functions
- Group related permissions logically

### 2. Permission Naming
- Follow convention: `{action}_{table}`
- Use clear, descriptive names
- Maintain consistency

### 3. Role Management
- Keep roles simple and focused
- Assign minimal necessary permissions
- Regular audit of role permissions

### 4. Performance
- Cache permission checks
- Minimize database queries
- Use middleware efficiently

## Troubleshooting

### Common Issues
1. **Permission Denied**: Check role assignments
2. **Missing Permissions**: Run sync script
3. **Cache Issues**: Clear permission cache
4. **Role Issues**: Verify user-role mapping

### Debug Commands
```bash
# Check permission system
node scripts/test-permission-system.js

# Debug user permissions  
SELECT p.name FROM permissions p
JOIN role_permissions rp ON p.id = rp.permission_id
JOIN user_role ur ON rp.role_id = ur.role_id  
WHERE ur.user_id = ?;
``` 