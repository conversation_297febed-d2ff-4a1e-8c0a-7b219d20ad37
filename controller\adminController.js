const adminService = require('../services/adminService');
const schemaService = require('../services/schemaService');
const dynamicCrudService = require('../services/dynamicCrudService');
const { Validator, ValidationError } = require('../utils/validation');

module.exports = {
  // ==================== DASHBOARD ====================
  
  // Trang chủ admin
  index: async (req, res) => {
    try {
      const breadcrumb = [
        { name: 'Home', url: '/', active: false },
        { name: 'Admin', url: '/admin', active: true }
      ];

      const tables = await adminService.getAllAdminTables();
      const totalTables = tables.length;
      const activeTables = tables.filter(t => t.is_active).length;

      res.render('admin/index', {
        title: 'Database Admin',
        breadcrumb,
        stats: {
          totalTables,
          activeTables,
          totalColumns: tables.reduce((sum, t) => sum + t.columns.length, 0),
          totalRelations: tables.reduce((sum, t) => sum + t.relations.length, 0)
        },
        tables
      });
    } catch (error) {
      console.error('Error in admin index:', error);
      res.status(500).render('500', { layout: 'login_layout' });
    }
  },

  // ==================== TABLES MANAGEMENT ====================

  // Danh sách bảng
  tables: async (req, res) => {
    try {
      const breadcrumb = [
        { name: 'Home', url: '/', active: false },
        { name: 'Admin', url: '/admin', active: false },
        { name: 'Tables', url: '/admin/tables', active: true }
      ];

      res.render('admin/tables', {
        title: 'Quản lý bảng',
        breadcrumb
      });
    } catch (error) {
      console.error('Error in tables page:', error);
      res.status(500).render('500', { layout: 'login_layout' });
    }
  },

  // API lấy dữ liệu bảng
  getTablesData: async (req, res) => {
    try {
      const draw = parseInt(req.query.draw) || 1;
      const start = parseInt(req.query.start) || 0;
      const length = parseInt(req.query.length) || 25;
      const searchValue = req.query.search?.value || '';

      const tables = await adminService.getAllAdminTables();
      
      // Filter theo search
      let filteredTables = tables;
      if (searchValue) {
        filteredTables = tables.filter(table => 
          table.name.toLowerCase().includes(searchValue.toLowerCase()) ||
          table.display_name.toLowerCase().includes(searchValue.toLowerCase())
        );
      }

      // Pagination
      const total = tables.length;
      const totalFiltered = filteredTables.length;
      const paginatedTables = filteredTables.slice(start, start + length);

      // Format data
      const formattedTables = paginatedTables.map(table => ({
        id: table.id,
        name: table.name,
        display_name: table.display_name,
        description: table.description || '',
        columns_count: table.columns.length,
        relations_count: table.relations.length,
        is_active: table.is_active,
        created_at: new Date(table.created_at).toLocaleDateString(),
        actions: `
          <button class="btn btn-sm btn-info view-table" data-id="${table.id}" title="Xem cấu trúc">
            <i class="fas fa-eye"></i>
          </button>
          <button class="btn btn-sm btn-primary edit-table" data-id="${table.id}" title="Chỉnh sửa">
            <i class="fas fa-edit"></i>
          </button>
          <button class="btn btn-sm btn-success manage-data" data-id="${table.id}" title="Quản lý dữ liệu">
            <i class="fas fa-database"></i>
          </button>
          <button class="btn btn-sm btn-danger delete-table" data-id="${table.id}" title="Xóa">
            <i class="fas fa-trash"></i>
          </button>
        `
      }));

      res.json({
        draw,
        recordsTotal: total,
        recordsFiltered: totalFiltered,
        data: formattedTables
      });
    } catch (error) {
      console.error('Error getting tables data:', error);
      res.status(500).json({ error: 'Failed to fetch tables data' });
    }
  },

  // Tạo bảng mới
  createTable: async (req, res) => {
    try {
      // Validate input data
      const validatedData = Validator.validateTableData(req.body);

      const table = await adminService.createAdminTable(validatedData);
      res.status(201).json({ success: true, data: table });
    } catch (error) {
      console.error('Error creating table:', error);

      if (error instanceof ValidationError) {
        return res.status(400).json({
          success: false,
          message: error.message,
          errors: error.errors || [{ field: error.field, message: error.message }]
        });
      }

      res.status(500).json({ success: false, message: error.message });
    }
  },

  // Lấy thông tin bảng
  getTable: async (req, res) => {
    try {
      const { id } = req.params;
      const table = await adminService.getAdminTableById(parseInt(id));

      if (!table) {
        return res.status(404).json({ success: false, message: 'Table not found' });
      }

      res.json({ success: true, data: table });
    } catch (error) {
      console.error('Error getting table:', error);
      res.status(500).json({ success: false, message: error.message });
    }
  },

  // Cập nhật bảng
  updateTable: async (req, res) => {
    try {
      const { id } = req.params;
      const tableData = req.body;
      const table = await adminService.updateAdminTable(parseInt(id), tableData);
      res.json({ success: true, data: table });
    } catch (error) {
      console.error('Error updating table:', error);
      res.status(500).json({ success: false, message: error.message });
    }
  },

  // Xóa bảng
  deleteTable: async (req, res) => {
    try {
      const { id } = req.params;
      await adminService.deleteAdminTable(parseInt(id));
      res.json({ success: true, message: 'Table deleted successfully' });
    } catch (error) {
      console.error('Error deleting table:', error);
      res.status(500).json({ success: false, message: error.message });
    }
  },

  // ==================== COLUMNS MANAGEMENT ====================

  // Thêm cột
  addColumn: async (req, res) => {
    try {
      const { tableId } = req.params;

      // Validate column data
      const validatedColumnData = Validator.validateColumnData(req.body);

      const column = await adminService.addAdminColumn(parseInt(tableId), validatedColumnData);
      res.status(201).json({ success: true, data: column });
    } catch (error) {
      console.error('Error adding column:', error);

      if (error instanceof ValidationError) {
        return res.status(400).json({
          success: false,
          message: error.message,
          errors: error.errors || [{ field: error.field, message: error.message }]
        });
      }

      res.status(500).json({ success: false, message: error.message });
    }
  },

  // Cập nhật cột
  updateColumn: async (req, res) => {
    try {
      const { id } = req.params;

      // Validate column data
      const validatedColumnData = Validator.validateColumnData(req.body);

      const column = await adminService.updateAdminColumn(parseInt(id), validatedColumnData);
      res.json({ success: true, data: column });
    } catch (error) {
      console.error('Error updating column:', error);

      if (error instanceof ValidationError) {
        return res.status(400).json({
          success: false,
          message: error.message,
          errors: error.errors || [{ field: error.field, message: error.message }]
        });
      }

      res.status(500).json({ success: false, message: error.message });
    }
  },

  // Xóa cột
  deleteColumn: async (req, res) => {
    try {
      const { id } = req.params;
      await adminService.deleteAdminColumn(parseInt(id));
      res.json({ success: true, message: 'Column deleted successfully' });
    } catch (error) {
      console.error('Error deleting column:', error);
      res.status(500).json({ success: false, message: error.message });
    }
  },

  // ==================== RELATIONS MANAGEMENT ====================

  // Tạo relation
  createRelation: async (req, res) => {
    try {
      const relationData = req.body;
      const relation = await adminService.createAdminRelation(relationData);
      res.status(201).json({ success: true, data: relation });
    } catch (error) {
      console.error('Error creating relation:', error);
      res.status(500).json({ success: false, message: error.message });
    }
  },

  // Xóa relation
  deleteRelation: async (req, res) => {
    try {
      const { id } = req.params;
      await adminService.deleteAdminRelation(parseInt(id));
      res.json({ success: true, message: 'Relation deleted successfully' });
    } catch (error) {
      console.error('Error deleting relation:', error);
      res.status(500).json({ success: false, message: error.message });
    }
  },

  // ==================== SYNC FUNCTIONS ====================

  // Đồng bộ bảng từ database
  syncTables: async (req, res) => {
    try {
      const result = await adminService.syncTablesFromDatabase();

      // Tạo thông báo chi tiết
      let message = result.message;
      if (result.added > 0 || result.deleted > 0) {
        const details = [];
        if (result.added > 0) details.push(`${result.added} table(s) added`);
        if (result.deleted > 0) details.push(`${result.deleted} table(s) removed`);
        if (result.exists > 0) details.push(`${result.exists} table(s) already exist`);
        message += ` (${details.join(', ')})`;
      }

      res.json({
        success: true,
        message,
        ...result
      });
    } catch (error) {
      console.error('Error syncing tables:', error);
      res.status(500).json({ success: false, message: error.message });
    }
  },
  // Trang sửa cấu trúc bảng
  tableStructure: async (req, res) => {
    try {
      const { tableId } = req.params;
      const adminTable = await adminService.getAdminTableById(parseInt(tableId));

      if (!adminTable) {
        return res.status(404).render('404', { layout: 'login_layout' });
      }

      // Lấy cấu trúc thực tế từ database
      const dbStructure = await schemaService.getTableStructure(adminTable.name);

      // Lấy danh sách tất cả bảng để làm foreign key reference
      const allTables = await adminService.getAllAdminTables();

      const breadcrumb = [
        { name: 'Home', url: '/', active: false },
        { name: 'Admin', url: '/admin', active: false },
        { name: 'Tables', url: '/admin/tables', active: false },
        { name: adminTable.display_name, url: `/admin/tables/${tableId}/structure`, active: true }
      ];

      res.render('admin/table-structure', {
        title: `Table Structure - ${adminTable.display_name}`,
        breadcrumb,
        table: adminTable,
        dbStructure,
        allTables
      });
    } catch (error) {
      console.error('Error getting table structure page:', error);
      res.status(500).render('500', { layout: 'login_layout' });
    }
  },

  // Cập nhật cấu trúc bảng (thêm/sửa/xóa cột)
  updateTableStructure: async (req, res) => {
    try {
      const { tableId } = req.params;
      const { action, columnData: columnDataString } = req.body;
      const columnData = JSON.parse(columnDataString);
      console.log("action", action, columnData, req.body);  
      const adminTable = await adminService.getAdminTableById(parseInt(tableId));
      if (!adminTable) {
        return res.status(404).json({ success: false, message: 'Table not found' });
      }

      let result;
      switch (action) {
        case 'add_column':
          result = await adminService.addAdminColumn(parseInt(tableId), columnData);
          break;
        case 'modify_column':
          result = await adminService.updateAdminColumn(parseInt(columnData.id), columnData);
          break;
        case 'drop_column':
          result = await adminService.deleteAdminColumn(parseInt(columnData.id));
          break;
        default:
          return res.status(400).json({ success: false, message: 'Invalid action' });
      }

      res.json({ success: true, data: result, message: 'Table structure updated successfully' });
    } catch (error) {
      console.error('Error updating table structure:', error);
      res.status(500).json({ success: false, message: error.message });
    }
  },

  // ==================== TABLE DATA MANAGEMENT ====================

  // Trang quản lý dữ liệu bảng
  tableData: async (req, res) => {
    try {
      const { tableId } = req.params;
      const table = await adminService.getAdminTableById(parseInt(tableId));

      if (!table) {
        return res.status(404).render('404', { layout: 'login_layout' });
      }

      // Lấy permissions của user cho bảng này (5-action system)
      const permissionService = require('../services/permissionService');
      const userPermissions = {
        canBrowse: await permissionService.checkUserPermission(req.user.id, null, table.name, 'browse'),
        canRead: await permissionService.checkUserPermission(req.user.id, null, table.name, 'read'),
        canCreate: await permissionService.checkUserPermission(req.user.id, null, table.name, 'create'),
        canUpdate: await permissionService.checkUserPermission(req.user.id, null, table.name, 'update'),
        canDelete: await permissionService.checkUserPermission(req.user.id, null, table.name, 'delete')
      };

      // Kiểm tra quyền browse - nếu không có thì redirect về home với flash message
      if (!userPermissions.canBrowse) {
        // JWT-compatible flash message via query parameter
        const errorMessage = encodeURIComponent(`Bạn không có quyền xem danh sách ${table.display_name}`);
        return res.redirect(`/?error=${errorMessage}`);
      }

      const breadcrumb = [
        { name: 'Home', url: '/', active: false },
        { name: 'Admin', url: '/admin', active: false },
        { name: 'Tables', url: '/admin/tables', active: false },
        { name: table.display_name, url: `/admin/tables/${tableId}/data`, active: true }
      ];

      res.render('admin/table-data', {
        title: `Quản lý dữ liệu - ${table.display_name}`,
        breadcrumb,
        table,
        userPermissions,
        user: req.user
      });
    } catch (error) {
      console.error('Error in table data page:', error);
      res.status(500).render('500', { layout: 'login_layout' });
    }
  },
  // API lấy dữ liệu bảng
  getTableDataApi: async (req, res) => {
    try {
      const { tableId } = req.params;
      const adminTable = await adminService.getAdminTableById(parseInt(tableId));

      if (!adminTable) {
        return res.status(404).json({ success: false, message: 'Table not found' });
      }

      const draw = parseInt(req.query.draw) || 1;
      const start = parseInt(req.query.start) || 0;
      const length = parseInt(req.query.length) || 25;
      const searchValue = req.query.search?.value || '';
      const orderColumnIndex = parseInt(req.query.order?.[0]?.column) || 0;
      const orderDirection = req.query.order?.[0]?.dir || 'desc';

      // Lấy tên cột để sort
      // Cột 0 là id, các cột tiếp theo là visible columns
      const visibleColumns = adminTable.columns.filter(col => col.is_visible_list);
      let orderColumn = 'id'; // Mặc định sort theo id
      if (orderColumnIndex > 0) {
        const actualColumnIndex = orderColumnIndex - 1; // Trừ đi 1 vì cột 0 là id
        orderColumn = visibleColumns[actualColumnIndex]?.name || 'id';
      }

      const page = Math.floor(start / length) + 1;

      const result = await dynamicCrudService.getTableData(adminTable.name, {
        page,
        perPage: length,
        search: searchValue,
        orderBy: orderColumn,
        orderDirection
      });

      // Lấy permissions của user cho bảng này (5-action system)
      const permissionService = require('../services/permissionService');
      const userPermissions = {
        canRead: await permissionService.checkUserPermission(req.user.id, null, adminTable.name, 'read'),
        canUpdate: await permissionService.checkUserPermission(req.user.id, null, adminTable.name, 'update'),
        canDelete: await permissionService.checkUserPermission(req.user.id, null, adminTable.name, 'delete')
      };

      // Format data cho DataTables
      const formattedData = result.data.map(row => {
        const formattedRow = {};

        // Luôn thêm trường id trước tiên
        formattedRow.id = row.id;

        // Thêm dữ liệu các cột visible
        visibleColumns.forEach(col => {
          if (row[`${col.name}_display`]) {
            formattedRow[col.name] = row[`${col.name}_display`];
          } else {
            formattedRow[col.name] = row[col.name];
          }
        });

        // Thêm actions dựa trên quyền (5-action system)
        let actions = [];
        
        if (userPermissions.canRead) {
          actions.push(`
            <button class="btn btn-sm btn-info view-record" data-id="${row.id}" title="View">
              <i class="fas fa-eye"></i>
            </button>
          `);
        }
        
        if (userPermissions.canUpdate) {
          actions.push(`
            <button class="btn btn-sm btn-primary edit-record" data-id="${row.id}" title="Update">
              <i class="fas fa-edit"></i>
            </button>
          `);
        }
        
        if (userPermissions.canDelete) {
          actions.push(`
            <button class="btn btn-sm btn-danger delete-record" data-id="${row.id}" title="Delete">
              <i class="fas fa-trash"></i>
            </button>
          `);
        }

        formattedRow.actions = actions.length > 0 ? actions.join('') : '<span class="text-muted">No actions</span>';

        return formattedRow;
      });

      res.json({
        draw,
        recordsTotal: result.total,
        recordsFiltered: result.totalFiltered,
        data: formattedData
      });
    } catch (error) {
      console.error('Error getting table data API:', error);
      res.status(500).json({ success: false, message: error.message });
    }
  },
  // API lấy dữ liệu của bảng
  getTableData: async (req, res) => {
    try {
      const { tableId } = req.params;
      const table = await adminService.getAdminTableById(parseInt(tableId));

      if (!table) {
        return res.status(404).json({ success: false, message: 'Table not found' });
      }

      const options = {
        page: parseInt(req.query.page) || 1,
        perPage: parseInt(req.query.perPage) || 25,
        search: req.query.search || '',
        orderBy: req.query.orderBy || 'id',
        orderDirection: req.query.orderDirection || 'desc',
        filters: req.query.filters ? JSON.parse(req.query.filters) : {}
      };

      const result = await dynamicCrudService.getTableData(table.name, options);
      res.json({ success: true, ...result });
    } catch (error) {
      console.error('Error getting table data:', error);
      res.status(500).json({ success: false, message: error.message });
    }
  },
  
  // Lấy một record
  getRecord: async (req, res) => {
    try {
      const { tableId, recordId } = req.params;
      const adminTable = await adminService.getAdminTableById(parseInt(tableId));

      if (!adminTable) {
        return res.status(404).json({ success: false, message: 'Table not found' });
      }

      const record = await dynamicCrudService.getRecord(adminTable.name, recordId);

      if (!record) {
        return res.status(404).json({ success: false, message: 'Record not found' });
      }

      res.json({ success: true, data: record });
    } catch (error) {
      console.error('Error getting record:', error);
      res.status(500).json({ success: false, message: error.message });
    }
  },

  // API tạo record mới
  createRecord: async (req, res) => {
    try {
      const { tableId } = req.params;
      const table = await adminService.getAdminTableById(parseInt(tableId));

      if (!table) {
        return res.status(404).json({ success: false, message: 'Table not found' });
      }

      // Validate dữ liệu với validation mới
      const validatedData = Validator.validateRecordData(req.body, table.columns);

      const record = await dynamicCrudService.createRecord(table.name, validatedData);
      res.status(201).json({ success: true, data: record });
    } catch (error) {
      console.error('Error creating record:', error);

      if (error instanceof ValidationError) {
        return res.status(400).json({
          success: false,
          message: error.message,
          errors: Validator.formatValidationErrors(error.errors || [error])
        });
      }

      res.status(500).json({ success: false, message: error.message });
    }
  },

  // API cập nhật record
  updateRecord: async (req, res) => {
    try {
      const { tableId, recordId } = req.params;
      const table = await adminService.getAdminTableById(parseInt(tableId));

      if (!table) {
        return res.status(404).json({ success: false, message: 'Table not found' });
      }

      // Validate dữ liệu với validation mới
      const validatedData = Validator.validateRecordData(req.body, table.columns);

      const record = await dynamicCrudService.updateRecord(table.name, parseInt(recordId), validatedData);
      res.json({ success: true, data: record });
    } catch (error) {
      console.error('Error updating record:', error);

      if (error instanceof ValidationError) {
        return res.status(400).json({
          success: false,
          message: error.message,
          errors: Validator.formatValidationErrors(error.errors || [error])
        });
      }

      res.status(500).json({ success: false, message: error.message });
    }
  },

  // API kiểm tra related records trước khi xóa
  checkRelatedRecords: async (req, res) => {
    try {
      const { tableId, recordId } = req.params;
      const table = await adminService.getAdminTableById(parseInt(tableId));

      if (!table) {
        return res.status(404).json({ success: false, message: 'Table not found' });
      }

      const relatedRecords = await dynamicCrudService.getRelatedRecords(table.name, parseInt(recordId));

      res.json({
        success: true,
        hasRelatedRecords: relatedRecords.length > 0,
        relatedRecords: relatedRecords
      });
    } catch (error) {
      console.error('Error checking related records:', error);
      res.status(500).json({ success: false, message: error.message });
    }
  },

  // API xóa record
  deleteRecord: async (req, res) => {
    try {
      const { tableId, recordId } = req.params;
      const table = await adminService.getAdminTableById(parseInt(tableId));

      if (!table) {
        return res.status(404).json({ success: false, message: 'Table not found' });
      }

      const deleted = await dynamicCrudService.deleteRecord(table.name, parseInt(recordId));

      if (deleted) {
        res.json({
          success: true,
          message: 'Record deleted successfully (including related records if any)'
        });
      } else {
        res.status(404).json({
          success: false,
          message: 'Record not found or could not be deleted'
        });
      }
    } catch (error) {
      console.error('Error deleting record:', error);

      // Provide more specific error messages
      if (error.code === 'ER_ROW_IS_REFERENCED_2') {
        res.status(400).json({
          success: false,
          message: 'Cannot delete record: it is referenced by other records. Please delete related records first.'
        });
      } else if (error.code === 'ER_NO_REFERENCED_ROW_2') {
        res.status(400).json({
          success: false,
          message: 'Cannot delete record: foreign key constraint violation.'
        });
      } else {
        res.status(500).json({
          success: false,
          message: error.message || 'An error occurred while deleting the record'
        });
      }
    }
  },

  // API lấy dữ liệu dropdown
  getDropdownData: async (req, res) => {
    try {
      const { tableName } = req.params;
      const { valueColumn, displayColumn } = req.query;
      
      const data = await dynamicCrudService.getDropdownData(
        tableName,
        valueColumn || 'id',
        displayColumn // Let the service auto-detect if not provided
      );
      
      res.json({ success: true, data });
    } catch (error) {
      console.error('Error getting dropdown data:', error);
      res.status(500).json({ success: false, message: error.message });
    }
  },
  // Lấy dữ liệu dropdown cho relation cụ thể
  getRelationDropdownData: async (req, res) => {
    try {
      const { tableId, columnName } = req.params;

      // Lấy thông tin bảng admin
      const adminTable = await adminService.getAdminTableById(parseInt(tableId));
      if (!adminTable) {
        return res.status(404).json({ success: false, message: 'Table not found' });
      }

      // Tìm relation cho column này
      const relation = adminTable.relations.find(rel => rel.column.name === columnName);
      if (!relation) {
        return res.status(404).json({ success: false, message: 'Relation not found for this column' });
      }

      // Lấy dữ liệu dropdown từ foreign table
      const data = await dynamicCrudService.getDropdownData(
        relation.foreign_table.name,
        relation.foreign_column,
        relation.display_column
      );

      res.json({ success: true, data });
    } catch (error) {
      console.error('Error getting relation dropdown data:', error);
      res.status(500).json({ success: false, message: error.message });
    }
  },
    // Lấy columns của một bảng admin
  getTableColumns: async (req, res) => {
    try {
      const { tableId } = req.params;
      const adminTable = await adminService.getAdminTableById(parseInt(tableId));
      
      if (!adminTable) {
        return res.status(404).json({ success: false, message: 'Table not found' });
      }

      res.json({ success: true, data: adminTable.columns });
    } catch (error) {
      console.error('Error getting table columns:', error);
      res.status(500).json({ success: false, message: error.message });
    }
  },
};
