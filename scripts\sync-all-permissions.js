const db = require('../config/database');
const permissionService = require('../services/permissionService');

async function syncAllPermissions() {
  try {
    console.log('🔄 Starting permission synchronization...');

    // Kết nối database trước
    console.log('🔌 Connecting to database...');
    await db.connect();

    // 1. Tạo system permissions trước
    console.log('📋 Creating system permissions...');
    await permissionService.createSystemPermissions();
    
    // 2. Đồng bộ permissions cho tất cả bảng trong admintable
    console.log('🔄 Syncing permissions for all admin tables...');
    const result = await permissionService.syncAllTablePermissions();
    
    console.log('✅ Permission synchronization completed successfully!');
    console.log(`📊 Summary:`);
    console.log(`   - Tables processed: ${result.tablesProcessed}`);
    console.log(`   - Permissions created: ${result.permissionsCreated}`);
    
    // 3. <PERSON><PERSON><PERSON> thị thống kê permissions hiện tại
    const stats = await getPermissionStats();
    console.log(`📈 Current permission statistics:`);
    console.log(`   - Total permissions: ${stats.totalPermissions}`);
    console.log(`   - Tables with permissions: ${stats.tablesWithPermissions}`);
    console.log(`   - Unique actions: ${stats.uniqueActions}`);
    
    process.exit(0);
  } catch (error) {
    console.error('❌ Error syncing permissions:', error);
    process.exit(1);
  }
}

async function getPermissionStats() {
  try {
    const [totalResult, tablesResult, actionsResult] = await Promise.all([
      db.queryOne('SELECT COUNT(*) as count FROM permissions'),
      db.queryOne('SELECT COUNT(DISTINCT table_name) as count FROM permissions WHERE table_name IS NOT NULL'),
      db.queryOne('SELECT COUNT(DISTINCT action) as count FROM permissions WHERE action IS NOT NULL')
    ]);

    return {
      totalPermissions: totalResult.count,
      tablesWithPermissions: tablesResult.count,
      uniqueActions: actionsResult.count
    };
  } catch (error) {
    console.error('Error getting permission stats:', error);
    return {
      totalPermissions: 0,
      tablesWithPermissions: 0,
      uniqueActions: 0
    };
  }
}

// Chạy script nếu được gọi trực tiếp
if (require.main === module) {
  syncAllPermissions();
}

module.exports = { syncAllPermissions, getPermissionStats };
